# Admin Wallet Controller Implementation

## Tổng quan
Đã tạo thành công Admin Wallet Controller với đầy đủ chức năng quản lý ví người dùng cho admin, bao gồm thông tin chi tiết và thống kê.

## Các file đã tạo

### 1. DTOs (Data Transfer Objects)
- **AdminWalletResponse.java** - Response DTO với thông tin chi tiết wallet và thống kê
- **AdminWalletFilter.java** - Filter DTO cho tìm kiếm và lọc wallet

### 2. Service Layer
- **AdminWalletService.java** - Interface định nghĩa các method cho admin wallet
- **AdminWalletServiceImpl.java** - Implementation với logic nghiệp vụ

### 3. Controller Layer
- **AdminWalletController.java** - Interface REST API
- **AdminWalletControllerImpl.java** - Implementation controller

### 4. Permissions
- **WalletPermissions.java** - Enum định nghĩa permissions cho wallet
- **PermissionSeeding.java** - Đ<PERSON> cập nhật để include WalletPermissions

### 5. Enums
- **TransactionType.java** - Đã thêm các loại transaction mới

## Chức năng chính

### 1. Tìm kiếm và lọc wallet
```http
POST /v1/admin/wallets/search
```
**Filters hỗ trợ:**
- `userId` - Lọc theo ID người dùng
- `userName` - Tìm kiếm theo tên người dùng (LIKE)
- `userEmail` - Tìm kiếm theo email người dùng (LIKE)
- `minPoints`, `maxPoints` - Lọc theo khoảng điểm
- `createdFrom`, `createdTo` - Lọc theo ngày tạo
- `lastModifiedFrom`, `lastModifiedTo` - Lọc theo ngày cập nhật
- `createdBy` - Lọc theo người tạo

### 2. Xem chi tiết wallet
```http
GET /v1/admin/wallets/{id}
GET /v1/admin/wallets/user/{userId}
```

### 3. Xem lịch sử giao dịch
```http
POST /v1/admin/wallets/{id}/transactions
```

### 4. Điều chỉnh điểm wallet
```http
PATCH /v1/admin/wallets/{id}/adjust?points={points}&reason={reason}
```

### 5. Thống kê tổng quan
```http
GET /v1/admin/wallets/statistics
```

## Thông tin chi tiết trong AdminWalletResponse

### Thông tin cơ bản
- ID wallet
- Thông tin người dùng (BasicUserInfoResponse)
- Số điểm hiện tại
- Thông tin audit (createdAt, lastModifiedAt, createdBy, lastModifiedBy)

### Thống kê chi tiết (WalletStatistics)
- `totalTransactions` - Tổng số giao dịch
- `totalPointsEarned` - Tổng điểm đã kiếm được
- `totalPointsSpent` - Tổng điểm đã tiêu
- `totalPaymentRequests` - Tổng số yêu cầu thanh toán
- `totalAmountPaid` - Tổng số tiền đã thanh toán
- `lastTransactionDate` - Ngày giao dịch cuối cùng
- `lastPaymentDate` - Ngày thanh toán cuối cùng
- `mostUsedTransactionType` - Loại giao dịch được sử dụng nhiều nhất
- `pendingPaymentRequests` - Số yêu cầu thanh toán đang chờ
- `successfulPaymentRequests` - Số yêu cầu thanh toán thành công
- `failedPaymentRequests` - Số yêu cầu thanh toán thất bại

## Permissions được sử dụng
- `wallet_read` - Đọc thông tin wallet
- `wallet_update` - Cập nhật wallet (điều chỉnh điểm)
- `wallet_statistics` - Xem thống kê (có thể sử dụng cho endpoint statistics riêng)

## Transaction Types mới đã thêm
- `REDEEM_POINTS` - Đổi điểm
- `ADMIN_ADJUSTMENT_ADD` - Admin tăng điểm
- `ADMIN_ADJUSTMENT_SUBTRACT` - Admin giảm điểm
- `REWARD` - Thưởng điểm
- `PURCHASE` - Mua hàng

## Cách sử dụng

### Ví dụ tìm kiếm wallet
```json
POST /v1/admin/wallets/search
{
  "userName": "john",
  "minPoints": 100,
  "maxPoints": 1000,
  "createdFrom": "2024-01-01T00:00:00",
  "createdTo": "2024-12-31T23:59:59",
  "page": 0,
  "limit": 20
}
```

### Ví dụ điều chỉnh điểm
```http
PATCH /v1/admin/wallets/123/adjust?points=100&reason=Bonus for good performance
```

### Response mẫu
```json
{
  "success": true,
  "message": "Wallet fetched successfully",
  "data": {
    "id": 123,
    "user": {
      "id": 456,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "point": 1500,
    "createdAt": "2024-01-01T10:00:00",
    "statistics": {
      "totalTransactions": 25,
      "totalPointsEarned": 2000,
      "totalPointsSpent": 500,
      "mostUsedTransactionType": "ADD_MONEY_TO_WALLET",
      "lastTransactionDate": "2024-01-15T14:30:00"
    }
  }
}
```

## Lưu ý
- Tất cả endpoints đều yêu cầu authentication và authorization
- Permissions sẽ được tự động seed vào database khi chạy ứng dụng
- Service layer có transaction management để đảm bảo data consistency
- Có logging chi tiết cho việc debug và monitoring
