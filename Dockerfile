# Stage 1: Build the application
FROM gradle:8.7-jdk as builder

# Set the working directory inside the container
WORKDIR /app

# Copy the Gradle build files
COPY build.gradle .
COPY gradlew .
COPY gradle gradle
COPY settings.gradle .

# Copy the source code
COPY src src

# Build the application
RUN ./gradlew build -x test

# Stage 2: Create the final Docker image
FROM openjdk:21-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the JAR file from the builder stage
COPY --from=builder /app/build/libs/*.jar app.jar

# Expose the port that the Spring Boot application listens on
EXPOSE 8080

# Define the command to run the Spring Boot application
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
