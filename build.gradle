plugins {
	id 'java'
	id 'org.springframework.boot' version '3.2.4'
	id 'io.spring.dependency-management' version '1.1.4'
}

group = 'vn.flexin.backend'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

ext {
    keycloakVersion = '23.0.7'
}


dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	// Core Spring Boot Dependencies
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation "org.springframework.boot:spring-boot-starter-thymeleaf:3.4.3"
	implementation 'org.springframework.boot:spring-boot-starter-webflux'

	implementation 'org.springframework.security.oauth.boot:spring-security-oauth2-autoconfigure:2.5.5'
	implementation 'io.hypersistence:hypersistence-utils-hibernate-63:3.8.1'

	// JWT Dependencies
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

	// OpenAPI/Swagger dependencies
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'

	// Keycloak Dependencies
	implementation "org.keycloak:keycloak-admin-client:${keycloakVersion}"
	implementation "org.keycloak:keycloak-core:${keycloakVersion}"
	implementation "org.keycloak:keycloak-adapter-core:${keycloakVersion}"
	implementation 'org.keycloak:keycloak-spring-boot-starter:15.0.2'
	implementation 'jakarta.ws.rs:jakarta.ws.rs-api:3.1.0'
	implementation 'org.jboss.resteasy:resteasy-core:6.2.7.Final'
	implementation 'org.jboss.resteasy:resteasy-client:6.2.7.Final'
	implementation 'org.jboss.resteasy:resteasy-jackson2-provider:6.2.7.Final'
    implementation 'org.apache.commons:commons-collections4:4.4'

    implementation 'jakarta.ws.rs:jakarta.ws.rs-api:3.1.0'

	// Database
	implementation 'org.postgresql:postgresql'
	runtimeOnly 'com.h2database:h2'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'

	// ulid generator
	implementation 'com.github.f4b6a3:ulid-creator:1.1.0'

	// google cloud config
	implementation 'com.google.cloud:google-cloud-storage:2.43.1'
	implementation 'com.google.firebase:firebase-admin:9.1.1'
	implementation 'com.google.api-client:google-api-client:1.32.1'
	implementation 'com.google.oauth-client:google-oauth-client-jetty:1.32.1'

	//AWS config
	implementation 'com.amazonaws:aws-java-sdk-s3:1.12.375'

	// mapper
	implementation 'org.modelmapper:modelmapper:3.1.0'
	implementation 'org.json:json:20220320'
}
tasks.named('test') {
	useJUnitPlatform()
}
