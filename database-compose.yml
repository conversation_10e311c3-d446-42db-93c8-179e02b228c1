version: '3.8'

services:
  postgres:
    image: postgres:latest  # Use the latest PostgreSQL image
    container_name: postgres_db  # Name for the container
    environment:
      POSTGRES_USER: tungls          # Database user
      POSTGRES_PASSWORD: password   # Database password
      POSTGRES_DB: flexin-db-dev               # Database name
    ports:
      - "5454:5432"                       # Expose port 5432 on the host
    volumes:
      - ./local_storage/db/db:/var/lib/postgresql/  # Persist data between container restarts