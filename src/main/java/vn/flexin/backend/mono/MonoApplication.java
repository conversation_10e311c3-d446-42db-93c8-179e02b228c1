package vn.flexin.backend.mono;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import lombok.AllArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import vn.flexin.backend.mono.common.job.GetApiAuthKeyParamJob;
@AllArgsConstructor
@SpringBootApplication
@OpenAPIDefinition(
		info = @Info(
				title = "Flexin Service API",
				version = "1.0",
				description = "API documentation for Flexin services",
				contact = @Contact(name = "Support Team", email = "<EMAIL>")
		),
		servers = {
				@Server(url = "/", description = "Default Server URL")
		}
)
public class MonoApplication implements CommandLineRunner {

	private final GetApiAuthKeyParamJob getApiAuthKeyParamJob;

	public static void main(String[] args) {
		SpringApplication.run(MonoApplication.class, args);
	}

	@Override
	public void run(String... args) {
		getApiAuthKeyParamJob.createCache();
	}

}
