package vn.flexin.backend.mono.address.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.address.dto.response.DistrictResponse;
import vn.flexin.backend.mono.address.dto.response.ProvinceResponse;
import vn.flexin.backend.mono.address.dto.response.WardResponse;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;

import java.util.List;

@Tag(name = "Address APIs", description = "API for managing address data (provinces, districts, wards)")
@RequestMapping("/v1/addresses/")
public interface AddressController {

    @Operation(summary = "Synchronize address data", description = "Fetches and saves the latest address data from the external API")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Data synchronization successful"),
            @ApiResponse(responseCode = "500", description = "Internal server error during synchronization")
    })
    @PostMapping("/sync")
    ResponseEntity<ApiResponseDto<Boolean>> syncData();

    @Operation(summary = "Get all provinces", description = "Retrieves a list of all provinces")
    @ApiResponse(responseCode = "200", description = "Successfully retrieved list of provinces",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ProvinceResponse.class)))
    @GetMapping("/provinces")
    ResponseEntity<ApiResponseDto<List<ProvinceResponse>>> getAllProvinces(
            @RequestParam(value = "name", required = false, defaultValue = "") String name
    );

    @Operation(summary = "Get province by code", description = "Retrieves a specific province by its code")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the province"),
            @ApiResponse(responseCode = "404", description = "Province not found")
    })
    @GetMapping("/provinces/{code}")
    ResponseEntity<ApiResponseDto<ProvinceResponse>> getProvinceByCode(
            @Parameter(description = "Code of the province to be retrieved") @PathVariable Long code
    );

    @Operation(summary = "Get districts by province code", description = "Retrieves all districts within a specific province")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list of districts"),
            @ApiResponse(responseCode = "404", description = "Province not found")
    })
    @GetMapping("/provinces/{provinceCode}/districts")
    ResponseEntity<ApiResponseDto<List<DistrictResponse>>> getDistrictsByProvinceCode(
            @Parameter(description = "Code of the province") @PathVariable Long provinceCode,
            @RequestParam(value = "name", required = false, defaultValue = "") String name
            );

    @Operation(summary = "Get district by code", description = "Retrieves a specific district by its code")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the district"),
            @ApiResponse(responseCode = "404", description = "District not found")
    })
    @GetMapping("/districts/{code}")
    ResponseEntity<ApiResponseDto<DistrictResponse>> getDistrictByCode(
            @Parameter(description = "Code of the district to be retrieved") @PathVariable Long code);

    @Operation(summary = "Get wards by district code", description = "Retrieves all wards within a specific district")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list of wards"),
            @ApiResponse(responseCode = "404", description = "District not found")
    })
    @GetMapping("/districts/{districtCode}/wards")
    ResponseEntity<ApiResponseDto<List<WardResponse>>> getWardsByDistrictCode(
            @Parameter(description = "Code of the district") @PathVariable Long districtCode,
            @RequestParam(value = "name", required = false, defaultValue = "") String name
    );
}

