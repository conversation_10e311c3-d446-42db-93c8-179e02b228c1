package vn.flexin.backend.mono.address.dto.request;

import lombok.Data;

@Data
public class AddressRequest {
    private Long id;
    private String detailAddress;
    private Long provinceCode;
    private Long districtCode;
    private Long wardCode;
    
    // Additional fields for response (not required for input)
    private String provinceName;
    private String districtName;
    private String wardName;
}
