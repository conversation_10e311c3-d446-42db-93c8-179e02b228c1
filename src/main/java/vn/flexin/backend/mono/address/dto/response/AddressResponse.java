package vn.flexin.backend.mono.address.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.entity.Address;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddressResponse {
    private Long id;
    private String detailAddress;
    private WardResponse ward;
    private DistrictResponse district;
    private ProvinceResponse province;

    public AddressResponse(Address address) {
        this.id = address.getId();
        this.detailAddress = address.getDetailAddress();
        if (address.getWard() != null) {
            this.ward = new WardResponse(address.getWard());
        }
        if (address.getDistrict() != null) {
            this.district = new DistrictResponse(address.getDistrict());
        }
        if (address.getProvince() != null) {
            this.province = new ProvinceResponse(address.getProvince());
        }
    }
}
