package vn.flexin.backend.mono.address.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.entity.Province;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProvinceResponse {
    private Long code;
    private String name;
    private String codename;
    private String divisionType;
    private Integer phoneCode;
    private List<DistrictResponse> districts;

    public ProvinceResponse(Province province) {
        this.code = province.getCode();
        this.name = province.getName();
        this.codename = province.getCodename();
        this.divisionType = province.getDivisionType();
        this.phoneCode = province.getPhoneCode();
    }
}
