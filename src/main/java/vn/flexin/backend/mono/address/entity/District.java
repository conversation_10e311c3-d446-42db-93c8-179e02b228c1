package vn.flexin.backend.mono.address.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Data
@Entity
@Table(name = "t_districts")
public class District {

    @Id
    @Column(name = "code")
    private Long code;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "codename", nullable = false)
    private String codename;

    @Column(name = "division_type", nullable = false)
    private String divisionType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "province_code", nullable = false)
    private Province province;

//    @OneToMany(mappedBy = "district", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    private List<Ward> wards;

}
