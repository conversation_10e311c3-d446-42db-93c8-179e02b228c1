package vn.flexin.backend.mono.address.entity;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "t_wards")
public class Ward {

    @Id
    @Column(name = "code")
    private Long code;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "codename", nullable = false)
    private String codename;

    @Column(name = "division_type", nullable = false)
    private String divisionType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "district_code", nullable = false)
    private District district;

}
