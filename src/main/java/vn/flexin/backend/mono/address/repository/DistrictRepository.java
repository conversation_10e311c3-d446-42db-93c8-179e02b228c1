package vn.flexin.backend.mono.address.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.address.entity.District;

import java.util.List;
import java.util.Optional;

public interface DistrictRepository extends JpaRepository<District, Long> {
    @Query("""
        SELECT district 
        FROM District district
        WHERE district.province.code = :provinceCode
            AND (:name IS NULL OR (district.name like %:name% OR district.codename like %:name%))
    """)
    List<District> findByProvinceCodeAndName(Long provinceCode, String name);

    Optional<District> findByCode(Long code);
}
