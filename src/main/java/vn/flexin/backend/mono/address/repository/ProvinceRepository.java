package vn.flexin.backend.mono.address.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.address.entity.Province;

import java.util.List;
import java.util.Optional;

public interface ProvinceRepository extends JpaRepository<Province, Long> {
    Optional<Province> findByCode(Long code);

    @Query("""
        SELECT p
        FROM Province p
        WHERE :name IS NULL OR p.name LIKE %:name% OR p.codename LIKE %:name%
    """)
    List<Province> findAllByName(String name);
}
