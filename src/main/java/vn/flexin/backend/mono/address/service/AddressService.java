package vn.flexin.backend.mono.address.service;

import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.address.dto.response.DistrictResponse;
import vn.flexin.backend.mono.address.dto.response.ProvinceResponse;
import vn.flexin.backend.mono.address.dto.response.WardResponse;
import vn.flexin.backend.mono.address.entity.Address;

import java.util.List;

public interface AddressService {

    List<ProvinceResponse> getAllProvinces(String name);

    ProvinceResponse getProvinceByCode(Long code);

    List<DistrictResponse> getDistrictsByProvinceCode(Long provinceCode, String name);

    DistrictResponse getDistrictByCode(Long code);

    List<WardResponse> getWardsByDistrictCode(Long districtCode, String name);

    Address createAddress(AddressRequest request);

    Address updateAddress(AddressRequest request);

    AddressResponse toAddressResponse(Address address);

    Address duplicateAddress(Address origin);
}
