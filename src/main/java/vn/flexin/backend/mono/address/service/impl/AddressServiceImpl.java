package vn.flexin.backend.mono.address.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.address.dto.response.DistrictResponse;
import vn.flexin.backend.mono.address.dto.response.ProvinceResponse;
import vn.flexin.backend.mono.address.dto.response.WardResponse;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.address.entity.District;
import vn.flexin.backend.mono.address.entity.Province;
import vn.flexin.backend.mono.address.entity.Ward;
import vn.flexin.backend.mono.address.repository.AddressRepository;
import vn.flexin.backend.mono.address.repository.DistrictRepository;
import vn.flexin.backend.mono.address.repository.ProvinceRepository;
import vn.flexin.backend.mono.address.repository.WardRepository;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.StringUtils;

import java.util.List;

@Service
@AllArgsConstructor
public class AddressServiceImpl implements AddressService {
    private final ProvinceRepository provinceRepository;
    private final DistrictRepository districtRepository;
    private final WardRepository wardRepository;
    private final AddressRepository addressRepository;


    @Override
    public List<ProvinceResponse> getAllProvinces(String name) {
        List<Province> provinces = provinceRepository.findAllByName(name);
        return provinces.stream()
                .map(this::mapToProvinceResponse)
                .toList();
    }

    @Override
    public ProvinceResponse getProvinceByCode(Long code) {
        Province province = provinceRepository.findByCode(code)
                .orElseThrow(() -> new BadRequestException("Province not found with code: " + code));
        return mapToProvinceResponse(province);
    }

    @Override
    public List<DistrictResponse> getDistrictsByProvinceCode(Long provinceCode, String name) {
        List<District> districts = districtRepository.findByProvinceCodeAndName(provinceCode, name);
        return districts.stream()
                .map(this::mapToDistrictResponse)
                .toList();
    }

    @Override
    public DistrictResponse getDistrictByCode(Long code) {
        District district = districtRepository.findByCode(code)
                .orElseThrow(() -> new RuntimeException("District not found with code: " + code));
        return mapToDistrictResponse(district);
    }

    @Override
    public List<WardResponse> getWardsByDistrictCode(Long districtCode, String name) {
        List<Ward> wards = wardRepository.findByDistrictCodeAndName(districtCode, name);
        return wards.stream()
                .map(this::mapToWardResponse)
                .toList();
    }

    @Override
    public Address createAddress(AddressRequest request) {
        Address address = new Address();
        setAddressInformation(request, address);
        return addressRepository.save(address);
    }

    private void setAddressInformation(AddressRequest request, Address address) {
        if (request.getProvinceCode() != null) {
            Province province = provinceRepository.findByCode(request.getProvinceCode())
                    .orElseThrow(() -> new ResourceNotFoundException("Province not found"));
            address.setProvince(province);
        }
        if (request.getDistrictCode() != null) {
            District district = districtRepository.findByCode(request.getDistrictCode())
                    .orElseThrow(() -> new ResourceNotFoundException("District not found"));
            address.setDistrict(district);
        }
        if (request.getWardCode() != null) {
            Ward ward = wardRepository.findByCode(request.getWardCode())
                    .orElseThrow(() -> new ResourceNotFoundException("Ward not found"));
            address.setWard(ward);
        }
        if (!StringUtils.isEmpty(request.getDetailAddress())) {
            address.setDetailAddress(request.getDetailAddress());
        }
    }

    @Override
    public Address updateAddress(AddressRequest request) {
        Address address = request.getId() == null
                ? new Address()
                : addressRepository.findById(request.getId())
                .orElse(new Address());
        setAddressInformation(request, address);
        return addressRepository.save(address);
    }

    @Override
    public AddressResponse toAddressResponse(Address address) {
        if (address == null) {
            return null;
        }
        AddressResponse response = new AddressResponse();
        response.setId(address.getId());
        response.setDetailAddress(address.getDetailAddress());
        response.setWard(mapToWardResponse(address.getWard()));
        response.setDistrict(mapToDistrictResponse(address.getDistrict()));
        response.setProvince(mapToProvinceResponse(address.getProvince()));
        return response;
    }

    @Override
    public Address duplicateAddress(Address origin) {
        Address cloneLocation = new Address();
        cloneLocation.setProvince(origin.getProvince());
        cloneLocation.setDistrict(origin.getDistrict());
        cloneLocation.setWard(origin.getWard());
        cloneLocation.setDetailAddress(origin.getDetailAddress());

        return addressRepository.save(cloneLocation);
    }

    private ProvinceResponse mapToProvinceResponse(Province province) {
        return ProvinceResponse.builder()
                .code(province.getCode())
                .name(province.getName())
                .build();
    }

    private DistrictResponse mapToDistrictResponse(District district) {
        return DistrictResponse.builder()
                .code(district.getCode())
                .name(district.getName())
                .build();
    }

    private WardResponse mapToWardResponse(Ward ward) {
        return WardResponse.builder()
                .code(ward.getCode())
                .name(ward.getName())
                .build();
    }
}
