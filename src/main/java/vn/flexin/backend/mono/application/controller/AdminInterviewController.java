package vn.flexin.backend.mono.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;

import java.util.List;

@Tag(name = "Admin Interview Management", description = "Admin interview management endpoints")
@SecurityRequirement(name = "bearerAuth")
public interface AdminInterviewController {

    @Operation(summary = "Search and filter interviews", description = "Search and filter interviews with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interviews retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<PaginationApiResponseDto<List<InterviewResponse>>> searchInterviews(InterviewFilter filter);

    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview retrieved successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<InterviewResponse>> getInterviewById(@PathVariable Long id);

    @Operation(summary = "Create Interview", description = "Create a new Interview (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Interview created successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<InterviewResponse>> createInterview(@Valid @RequestBody InterviewRequest requet);

    @Operation(summary = "Update Interview", description = "Update an existing Interview (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview updated successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<InterviewResponse>> updateInterview(
            @PathVariable Long id, @Valid @RequestBody InterviewRequest requet);

    @Operation(summary = "Update Interview status", description = "Update the status of a Interview (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview status updated successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<InterviewResponse>> updateInterviewStatus(@PathVariable Long id, @RequestParam String status);

    @Operation(summary = "Delete Interview", description = "Delete a Interview (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteInterview(@PathVariable Long id);

    @Operation(summary = "Create feedback", description = "Create a new feedback (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Feedback created successfully",
                    content = @Content(schema = @Schema(implementation = InterviewFeedbackResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/{id}/feedback")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<InterviewFeedbackResponse>> createFeedback(@PathVariable Long id, @Valid @RequestBody InterviewFeedbackRequest requet);
}
