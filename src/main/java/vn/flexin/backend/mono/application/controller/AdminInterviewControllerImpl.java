package vn.flexin.backend.mono.application.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.interview.service.InterviewService;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;

import java.util.List;

@RestController
@RequestMapping("/v1/admin/interviews")
@RequiredArgsConstructor
@Slf4j
public class AdminInterviewControllerImpl implements AdminInterviewController {

    private final InterviewService interviewService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<InterviewResponse>>> searchInterviews(@Valid InterviewFilter filter) {
        log.info("Admin searching and filtering interview with request: {}", filter);
        var result = interviewService.searchInterviews(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Interview retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> getInterviewById(Long id) {
        log.info("Admin retrieving interview with ID: {}", id);
        var response = interviewService.getInterviewById(id);
        return new ResponseEntity<>(ApiResponseDto.success(response), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> createInterview(@Valid InterviewRequest ContractRequet) {
        log.info("Admin creating new interview: {}", ContractRequet);
        var response = interviewService.createInterview(ContractRequet);
        return new ResponseEntity<>(ApiResponseDto.success(response), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> updateInterview(Long id, @Valid InterviewRequest requet) {
        log.info("Admin updating interview with ID: {}", id);
        requet.setId(id);
        var response = interviewService.updateInterview(requet);
        return new ResponseEntity<>(ApiResponseDto.success(response), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> updateInterviewStatus(Long id, String status) {
        log.info("Admin updating interview status for ID: {} to status: {}", id, status);
        var response = interviewService.updateInterviewStatus(id, status);
        return new ResponseEntity<>(ApiResponseDto.success(response), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteInterview(Long id) {
        log.info("Admin deleting interview with ID: {}", id);
        return new ResponseEntity<>(ApiResponseDto.success(interviewService.deleteInterview(id)), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewFeedbackResponse>> createFeedback(Long interviewId, @Valid InterviewFeedbackRequest request) {
        log.info("Admin creating new feedback: {}", request);
        request.setInterviewId(interviewId);
        var response = interviewService.createInterviewFeedback(request);
        return new ResponseEntity<>(ApiResponseDto.success(response), HttpStatus.CREATED);
    }
}
