package vn.flexin.backend.mono.application.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;
import vn.flexin.backend.mono.application.service.JobApplicationService;
import vn.flexin.backend.mono.common.dto.ApiResponse;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/admin/job-applications")
@RequiredArgsConstructor
@Slf4j
public class AdminJobApplicationControllerImpl implements AdminJobApplicationController {

    private final JobApplicationService jobApplicationService;

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getAllJobApplications() {
        log.info("Admin retrieving all job applications");
        List<JobApplicationDto> applications = jobApplicationService.getJobApplicationsByJobId(null);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", applications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Page<JobApplicationDto>>> searchJobApplications(@Valid SearchJobApplicationRequest searchJobApplicationRequest) {
        log.info("Admin searching and filtering job applications with request: {}", searchJobApplicationRequest);
        Page<JobApplicationDto> applications = jobApplicationService.searchJobApplications(searchJobApplicationRequest);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", applications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> getJobApplicationById(Long id) {
        log.info("Admin retrieving job application with ID: {}", id);
        JobApplicationDto application = jobApplicationService.getJobApplicationById(id);
        return new ResponseEntity<>(ApiResponse.success("Job application retrieved successfully", application), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobId(Long jobId) {
        log.info("Admin retrieving job applications for job ID: {}", jobId);
        List<JobApplicationDto> applications = jobApplicationService.getJobApplicationsByJobId(jobId);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", applications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByUserId(Long userId) {
        log.info("Admin retrieving job applications for user ID: {}", userId);
        List<JobApplicationDto> applications = jobApplicationService.getJobApplicationsByJobSeekerId(userId);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", applications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> updateJobApplicationStatus(Long id, String status, String adminNotes) {
        log.info("Admin updating job application status for ID: {} to status: {}", id, status);
        JobApplicationDto updatedApplication = jobApplicationService.updateJobApplicationStatus(id, status, adminNotes);
        return new ResponseEntity<>(ApiResponse.success("Job application status updated successfully", updatedApplication), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteJobApplication(Long id) {
        log.info("Admin deleting job application with ID: {}", id);
        jobApplicationService.deleteJobApplication(id);
        return new ResponseEntity<>(ApiResponse.success("Job application deleted successfully"), HttpStatus.OK);
    }
} 