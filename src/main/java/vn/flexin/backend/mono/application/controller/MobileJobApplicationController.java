package vn.flexin.backend.mono.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;

import java.util.List;

@Tag(name = "Mobile Job Application Management", description = "Mobile job application management endpoints")
public interface MobileJobApplicationController {
    
    @Operation(summary = "Create a new job application", description = "Submit a new job application (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully submitted job application"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> createJobApplication(@Valid @RequestBody JobApplicationDto jobApplicationDto);
    
    @Operation(summary = "Get job application by ID", description = "Get a job application by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job application"),
        @ApiResponse(responseCode = "404", description = "Job application not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> getJobApplicationById(@PathVariable Long id);
    
    @Operation(summary = "Get job applications by job ID", description = "Get all job applications for a specific job (Employer or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/job/{jobId}")
    @PreAuthorize("hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobId(@PathVariable Long jobId);
    
    @Operation(summary = "Get job applications by job seeker ID", description = "Get all job applications for a specific job seeker (Job Seeker or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/job-seeker/{jobSeekerId}")
    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobSeekerId(@PathVariable Long jobSeekerId);
    
    @Operation(summary = "Get job applications by resume ID", description = "Get all job applications for a specific resume (Job Seeker or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/resume/{resumeId}")
    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByResumeId(@PathVariable Long resumeId);
    
    @Operation(summary = "Get job applications by job ID and status", description = "Get all job applications for a specific job with a specific status (Employer or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/job/{jobId}/status/{status}")
    @PreAuthorize("hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobIdAndStatus(
            @PathVariable Long jobId, @PathVariable String status);
    
    @Operation(summary = "Get job applications by job seeker ID and status", description = "Get all job applications for a specific job seeker with a specific status (Job Seeker or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/job-seeker/{jobSeekerId}/status/{status}")
    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobSeekerIdAndStatus(
            @PathVariable Long jobSeekerId, @PathVariable String status);
    
    @Operation(summary = "Update job application", description = "Update a job application (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated job application"),
        @ApiResponse(responseCode = "404", description = "Job application not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> updateJobApplication(
            @PathVariable Long id, @Valid @RequestBody JobApplicationDto jobApplicationDto);
    
    @Operation(summary = "Update job application status", description = "Update a job application status (Employer or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated job application status"),
        @ApiResponse(responseCode = "404", description = "Job application not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> updateJobApplicationStatus(
            @PathVariable Long id, 
            @RequestParam String status, 
            @RequestParam(required = false) String employerNotes);
    
    @Operation(summary = "Delete job application", description = "Delete a job application (Job Seeker or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted job application"),
        @ApiResponse(responseCode = "404", description = "Job application not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteJobApplication(@PathVariable Long id);
    
    @Operation(summary = "Search and filter job applications", description = "Search and filter job applications with pagination and sorting")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job applications"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Page<JobApplicationDto>>> searchJobApplications(
            @Valid @RequestBody SearchJobApplicationRequest searchJobApplicationRequest);
} 