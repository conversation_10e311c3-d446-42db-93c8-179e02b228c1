package vn.flexin.backend.mono.application.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;
import vn.flexin.backend.mono.application.service.JobApplicationService;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/mobile/job-applications")
@RequiredArgsConstructor
public class MobileJobApplicationControllerImpl implements MobileJobApplicationController {

    private final JobApplicationService jobApplicationService;

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> createJobApplication(@Valid JobApplicationDto jobApplicationDto) {
        JobApplicationDto createdJobApplication = jobApplicationService.createJobApplication(jobApplicationDto);
        return new ResponseEntity<>(ApiResponse.success("Job application submitted successfully", createdJobApplication), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> getJobApplicationById(Long id) {
        JobApplicationDto jobApplication = jobApplicationService.getJobApplicationById(id);
        return new ResponseEntity<>(ApiResponse.success("Job application retrieved successfully", jobApplication), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobId(Long jobId) {
        List<JobApplicationDto> jobApplications = jobApplicationService.getJobApplicationsByJobId(jobId);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobSeekerId(Long jobSeekerId) {
        List<JobApplicationDto> jobApplications = jobApplicationService.getJobApplicationsByJobSeekerId(jobSeekerId);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByResumeId(Long resumeId) {
        List<JobApplicationDto> jobApplications = jobApplicationService.getJobApplicationsByResumeId(resumeId);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobIdAndStatus(Long jobId, String status) {
        List<JobApplicationDto> jobApplications = jobApplicationService.getJobApplicationsByJobIdAndStatus(jobId, status);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobSeekerIdAndStatus(Long jobSeekerId, String status) {
        List<JobApplicationDto> jobApplications = jobApplicationService.getJobApplicationsByJobSeekerIdAndStatus(jobSeekerId, status);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> updateJobApplication(Long id, @Valid JobApplicationDto jobApplicationDto) {
        JobApplicationDto updatedJobApplication = jobApplicationService.updateJobApplication(id, jobApplicationDto);
        return new ResponseEntity<>(ApiResponse.success("Job application updated successfully", updatedJobApplication), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobApplicationDto>> updateJobApplicationStatus(Long id, String status, String employerNotes) {
        JobApplicationDto updatedJobApplication = jobApplicationService.updateJobApplicationStatus(id, status, employerNotes);
        return new ResponseEntity<>(ApiResponse.success("Job application status updated successfully", updatedJobApplication), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteJobApplication(Long id) {
        jobApplicationService.deleteJobApplication(id);
        return new ResponseEntity<>(ApiResponse.success("Job application deleted successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Page<JobApplicationDto>>> searchJobApplications(@Valid SearchJobApplicationRequest searchJobApplicationRequest) {
        Page<JobApplicationDto> jobApplications = jobApplicationService.searchJobApplications(searchJobApplicationRequest);
        return new ResponseEntity<>(ApiResponse.success("Job applications retrieved successfully", jobApplications), HttpStatus.OK);
    }
} 