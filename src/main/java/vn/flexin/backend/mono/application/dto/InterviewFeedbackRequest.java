package vn.flexin.backend.mono.application.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;
import vn.flexin.backend.mono.interview.entity.InterviewFeedback;

import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InterviewFeedbackRequest {
    private Long interviewId;
    private Integer rating;
    private Integer communication;
    private Integer technicalSkills;
    private Integer culturalFit;
    private Integer experience;
    private String notes;
    @NotBlank(message = "Decision is required")
    private String decision;
    private OfferDetails offerDetails;
    private Set<String> strengths;
    private Set<String> weaknesses;

    public InterviewFeedback toEntity() {
        InterviewFeedback feedback = new InterviewFeedback();
        feedback.setRating(rating);
        feedback.setCommunication(communication);
        feedback.setTechnicalSkills(technicalSkills);
        feedback.setCulturalFit(culturalFit);
        feedback.setExperience(experience);
        feedback.setNotes(notes);
        feedback.setDecision(decision);
        feedback.setOfferSalary(offerDetails.getSalary());
        feedback.setOfferStartDate(offerDetails.getStartDate());
        feedback.setOfferNotes(offerDetails.getNotes());
        feedback.setStrengths(strengths);
        feedback.setWeaknesses(weaknesses);
        return feedback;
    }

}