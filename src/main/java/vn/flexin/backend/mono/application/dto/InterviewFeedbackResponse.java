package vn.flexin.backend.mono.application.dto;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InterviewFeedbackResponse {
    private Long interviewId;
    private Integer rating;
    private Integer communication;
    private Integer technicalSkills;
    private Integer culturalFit;
    private Integer experience;
    private String notes;
    private String decision;
    private OfferDetails offerDetails;
    private Set<String> strengths;
    private Set<String> weaknesses;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime lastModifiedAt;
    private String lastModifiedBy;
}