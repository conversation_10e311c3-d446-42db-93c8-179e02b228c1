package vn.flexin.backend.mono.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InterviewFilter extends BaseFilter<Interview> {

    private List<String> status;
    private Long employerId;
    private Long jobSeekerId;
    private Long jobPostId;
    private LocalDateTime startDateRange;
    private LocalDateTime endDateRange;
    private String cancelFrom;
    private Integer minDuration;
    private Integer maxDuration;

    @Override
    public Specification<Interview> toSpecification() {
        var condition = new Condition();

        // Chỉ append điều kiện khi trường khác null
        if (employerId != null) {
            condition.append(new Join(Interview.Fields.employer, List.of(new Where(User.Fields.id, employerId))));
        }

        if (jobSeekerId != null) {
            condition.append(new Join(Interview.Fields.jobSeeker, List.of(new Where(User.Fields.id, jobSeekerId))));
        }

        if (status != null && !status.isEmpty()) {
            condition.append(new Where(Interview.Fields.status, Operator.IN, status));
        }

        if (jobPostId != null) {
            condition.append(new Join(Interview.Fields.post, List.of(new Where("id", jobPostId))));
        }

        if (startDateRange != null) {
            condition.append(new Where(Interview.Fields.scheduledTime, Operator.GREATER_THAN_OR_EQUAL,
                    LocalDateTime.of(startDateRange.toLocalDate(), LocalTime.MIN)));
        }

        if (endDateRange != null) {
            condition.append(new Where(Interview.Fields.scheduledTime, Operator.LESS_THAN_OR_EQUAL,
                    LocalDateTime.of(endDateRange.toLocalDate(), LocalTime.MAX)));
        }

        if (cancelFrom != null) {
            condition.append(new Where(Interview.Fields.cancelFrom, cancelFrom));
        }

        if (minDuration != null) {
            condition.append(new Where(Interview.Fields.durationMinutes, Operator.GREATER_THAN_OR_EQUAL, minDuration));
        }

        if (maxDuration != null) {
            condition.append(new Where(Interview.Fields.durationMinutes, Operator.LESS_THAN_OR_EQUAL, maxDuration));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}