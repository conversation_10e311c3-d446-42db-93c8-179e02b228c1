package vn.flexin.backend.mono.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import vn.flexin.backend.mono.interview.entity.Interview;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class InterviewRequest {
    private Long id;
    
    @NotNull(message = "Employer ID is required")
    private Long employerId;
    
    private String employerName;
    
    @NotNull(message = "Job seeker ID is required")
    private Long jobSeekerId;
    
    private String jobSeekerName;
    
    @NotNull(message = "Scheduled time is required")
    private LocalDateTime scheduledTime;
    
    @Positive(message = "Duration must be positive")
    private Integer durationMinutes = 30;
    
    private String status = "scheduled"; // scheduled, in_progress, completed, cancelled
    
    private String meetingLink;
    
    private String meetingId;
    
    private String meetingPassword;
    
    private String notes;
    
    private LocalDateTime startedAt;
    
    private LocalDateTime endedAt;
    
    private String cancellationReason;
    
    private String feedback;
    
    private Integer employerRating;
    
    private Integer jobSeekerRating;

    private Long postId;
    
    public Interview toEntity() {
        Interview interview = new Interview();
        interview.setCancellationReason(cancellationReason);
        interview.setScheduledTime(scheduledTime);
        interview.setDurationMinutes(durationMinutes);
        interview.setStatus(status);
        interview.setMeetingLink(meetingLink);
        interview.setMeetingId(meetingId);
        interview.setMeetingPassword(meetingPassword);
        interview.setNotes(notes);
        interview.setStartedAt(startedAt);
        interview.setEndedAt(endedAt);
        interview.setFeedback(feedback);
        interview.setEmployerRating(employerRating);
        interview.setJobSeekerRating(jobSeekerRating);
        return interview;
    }
} 