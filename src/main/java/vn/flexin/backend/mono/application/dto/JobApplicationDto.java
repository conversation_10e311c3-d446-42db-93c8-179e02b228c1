package vn.flexin.backend.mono.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class JobApplicationDto {
    private Long id;
    
    @NotNull(message = "Job ID is required")
    private Long jobId;
    
    private String jobTitle;
    
    @NotNull(message = "Job seeker ID is required")
    private Long jobSeekerId;
    
    private String jobSeekerName;
    
    @NotNull(message = "Resume ID is required")
    private Long resumeId;
    
    private String resumeName;
    
    private String coverLetter;
    
    private String status = "applied"; // applied, reviewed, shortlisted, rejected, interview_scheduled, hired
    
    private String employerNotes;
    
    private String rejectionReason;
} 