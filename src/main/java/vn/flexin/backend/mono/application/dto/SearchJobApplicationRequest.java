package vn.flexin.backend.mono.application.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.flexin.backend.mono.common.dto.SearchRequest;

import jakarta.validation.Valid;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchJobApplicationRequest extends SearchRequest {
    private List<@Valid JobApplicationFilter> filters;
    
    private JobApplicationSortField sortBy;
} 