package vn.flexin.backend.mono.application.service;

import org.springframework.data.domain.Page;
import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;
import vn.flexin.backend.mono.application.entity.JobApplication;

import java.util.List;

public interface JobApplicationService {
    JobApplicationDto createJobApplication(JobApplicationDto jobApplicationDto);
    JobApplicationDto getJobApplicationById(Long id);
    List<JobApplicationDto> getJobApplicationsByJobId(Long jobId);
    List<JobApplicationDto> getJobApplicationsByJobSeekerId(Long jobSeekerId);
    List<JobApplicationDto> getJobApplicationsByResumeId(Long resumeId);
    List<JobApplicationDto> getJobApplicationsByJobIdAndStatus(Long jobId, String status);
    List<JobApplicationDto> getJobApplicationsByJobSeekerIdAndStatus(Long jobSeekerId, String status);
    Page<JobApplicationDto> searchJobApplications(SearchJobApplicationRequest searchJobApplicationRequest);
    JobApplicationDto updateJobApplication(Long id, JobApplicationDto jobApplicationDto);
    JobApplicationDto updateJobApplicationStatus(Long id, String status, String employerNotes);
    void deleteJobApplication(Long id);
    boolean existsByJobIdAndJobSeekerId(Long jobId, Long jobSeekerId);
    JobApplication getJobApplicationEntityById(Long id);
} 