package vn.flexin.backend.mono.auth.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.user.dto.CreateUserResponse;
import vn.flexin.backend.mono.user.dto.UpdateRoleRequest;
import vn.flexin.backend.mono.user.dto.UserDeviceResponse;

import java.util.List;

@Tag(name = "Mobile Authentication APIs", description = "Mobile authentication endpoints")
@RequestMapping("/v1/mobile/auth")
public interface MobileAuthController {
    
    @Operation(summary = "Login with phone number", description = "Authenticate user with phone number and password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully authenticated"),
        @ApiResponse(responseCode = "401", description = "Invalid credentials"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/login")
    ResponseEntity<ApiResponseDto<AuthResponse>> login(@RequestHeader(value = "x-forwarded-for", required = false) String clientIp,
                                                       @Valid @RequestBody LoginRequest loginRequest);

    @Operation(summary = "Register new user", description = "Start registration process by providing phone number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Registration initiated successfully"),
        @ApiResponse(responseCode = "400", description = "Phone number already registered")
    })
    @PostMapping("/register")
    ResponseEntity<ApiResponseDto<TempTokenResponse>> register(@RequestHeader(value = "x-forwarded-for", required = false) String clientIp,
                                                               @Valid @RequestBody RegisterRequest registerRequest);

    @Operation(summary = "Verify OTP", description = "Verify OTP sent to phone number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP verified successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid OTP or expired")
    })
    @PostMapping("/verify-otp")
    ResponseEntity<ApiResponseDto<VerifyOtpResponse>> verifyOtp(@Valid @RequestBody VerifyOtpRequest verifyOtpRequest);

    @Operation(summary = "Create password", description = "Create password for new account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Password created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid password or token")
    })
    @PostMapping("/create-password")
    ResponseEntity<ApiResponseDto<TempTokenResponse>> createPassword(@Valid @RequestBody CreatePasswordRequest createPasswordRequest);

    @Operation(summary = "Select role", description = "Select role and complete registration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Role selected and account created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid role or token")
    })
    @PostMapping("/select-role")
    ResponseEntity<ApiResponseDto<AuthResponse>> selectRole(@Valid @RequestBody SelectRoleRequest selectRoleRequest);

    @Operation(summary = "Request OTP", description = "Request new OTP for various purposes")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP sent successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @PostMapping("/request-otp")
    ResponseEntity<ApiResponseDto<TempTokenResponse>> requestOtp(@RequestHeader(value = "x-forwarded-for", required = false) String clientIp,
                                                                 @Valid @RequestBody RequestOtpRequest requestOtpRequest);

    @Operation(summary = "Reset password", description = "Reset password using OTP verification")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Password reset successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    @PostMapping("/reset-password")
    ResponseEntity<ApiResponseDto<Boolean>> resetPassword(@Valid @RequestBody ResetPasswordRequest resetPasswordRequest);

    @Operation(summary = "Get user sessions", description = "Get list of active sessions for current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Sessions retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping("/sessions")
    ResponseEntity<ApiResponseDto<List<UserDeviceResponse>>> getSessions(@RequestParam(value = "deviceId", required = false) String deviceId);

    @Operation(summary = "Logout", description = "Logout current session")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logged out successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/logout")
    ResponseEntity<ApiResponseDto<Boolean>> logout();

    @Operation(summary = "Logout device", description = "Logout specific device")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Device logged out successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/logout-device")
    ResponseEntity<ApiResponseDto<Boolean>> logoutDevice(@Valid @RequestBody LogoutDeviceRequest logoutDeviceRequest);

    @Operation(summary = "Logout all devices", description = "Logout all devices except current")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "All devices logged out successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/logout-all-devices")
    ResponseEntity<ApiResponseDto<Boolean>> logoutAllDevices();

    @Operation(summary = "Refresh token", description = "Get new access token using refresh token")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
        @ApiResponse(responseCode = "401", description = "Invalid refresh token")
    })
    @PostMapping("/refresh-token")
    ResponseEntity<ApiResponseDto<TokenDto>> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest);

    @Operation(summary = "Update user's role", description = "User change role between employer and jobSeeker")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "update role successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid token")
    })
    @PostMapping("/update-role")
    ResponseEntity<ApiResponseDto<CreateUserResponse>> updateRole(@Valid @RequestBody UpdateRoleRequest refreshTokenRequest);
}
