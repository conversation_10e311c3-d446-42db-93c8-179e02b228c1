package vn.flexin.backend.mono.auth.controller.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.service.mobile.MobileAuthService;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.user.dto.CreateUserResponse;
import vn.flexin.backend.mono.user.dto.UpdateRoleRequest;
import vn.flexin.backend.mono.user.dto.UserDeviceResponse;

import java.util.List;

@RestController
@RequestMapping("/v1/mobile/auth")
@RequiredArgsConstructor
@Slf4j
public class MobileAuthControllerImpl implements MobileAuthController {

    private final MobileAuthService mobileAuthService;

    @Override
    public ResponseEntity<ApiResponseDto<AuthResponse>> login(@RequestHeader(value = "x-forwarded-for", required = false) String clientIp, LoginRequest loginRequest) {
        log.info("Login request with phone: {}, deviceId: {}", loginRequest.getPhone(), loginRequest.getDeviceId());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.login(clientIp, loginRequest), "Login successful"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<TempTokenResponse>> register(String clientIp, RegisterRequest registerRequest) {
        log.info("Register request with phone: {}", registerRequest.getPhone());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.register(clientIp, registerRequest), "OTP sent to your phone"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<VerifyOtpResponse>> verifyOtp(VerifyOtpRequest verifyOtpRequest) {
        log.info("Verify OTP request for phone: {}", verifyOtpRequest.getPhone());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.verifyOtp(verifyOtpRequest), "OTP verified successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<TempTokenResponse>> createPassword(CreatePasswordRequest createPasswordRequest) {
        log.info("Create password request for phone: {}", createPasswordRequest.getPhone());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.createPassword(createPasswordRequest), "Password created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AuthResponse>> selectRole(SelectRoleRequest selectRoleRequest) {
        log.info("Select role request for phone: {}, role: {}", selectRoleRequest.getPhone(), selectRoleRequest.getRole());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.selectRole(selectRoleRequest), "Role selected"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<TempTokenResponse>> requestOtp(String clientIp, RequestOtpRequest requestOtpRequest) {
        log.info("Request OTP for phone: {}, purpose: {}", requestOtpRequest.getPhone(), requestOtpRequest.getPurpose());
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.requestOtp(clientIp, requestOtpRequest), "OTP sent"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> resetPassword(ResetPasswordRequest resetPasswordRequest) {
        log.info("Reset password request for phone: {}", resetPasswordRequest.getPhone());
        mobileAuthService.resetPassword(resetPasswordRequest);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Password reset"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<UserDeviceResponse>>> getSessions(@RequestParam(value = "deviceId", required = false) String deviceId) {
        log.info("Get sessions request");
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.getSessions(deviceId), "Sessions retrieved"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> logout() {
        log.info("Logout request");
        mobileAuthService.logout();
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Logged out"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> logoutDevice(LogoutDeviceRequest logoutDeviceRequest) {
        log.info("Logout device request for deviceId: {}", logoutDeviceRequest.getDeviceId());
        mobileAuthService.logoutDevice(logoutDeviceRequest);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Device logged out"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> logoutAllDevices() {
        log.info("Logout all devices request");
        mobileAuthService.logoutAllDevices();
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "All devices logged out"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<TokenDto>> refreshToken(RefreshTokenRequest refreshTokenRequest) {
        log.info("Refresh token request");
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.refreshToken(refreshTokenRequest), "Token refreshed"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateUserResponse>> updateRole(UpdateRoleRequest updateRoleRequest) {
        return ResponseEntity.ok(ApiResponseDto.success(mobileAuthService.updateRole(updateRoleRequest), "Role update successfully."));
    }
} 