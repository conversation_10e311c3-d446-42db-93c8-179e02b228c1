package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminUpdateProfileRequest {

    @Size(max = 50, message = "Name must be less than 50 characters")
    private String name;

    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phoneNumber;

    private String dateOfBirth; // ISO datetime string format

    @Pattern(regexp = "^(male|female|other)$", message = "Gender must be either 'male', 'female', or 'other'")
    private String gender;

    @Valid
    private AddressRequest address;
} 