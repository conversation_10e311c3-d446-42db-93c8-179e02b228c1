package vn.flexin.backend.mono.auth.dto;

import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JwtToken {
	private String access_token;
	private Integer expires_in;
	private Integer refresh_expires_in;
	private String refresh_token;
	private String token_type;
	private String id_token;
	private String session_state;
	private String scope;
	private Integer not_before_policy;
}
