package vn.flexin.backend.mono.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class KeycloakUserInfo {
    @JsonProperty("sub")
    private String keycloakUserId;
    @JsonProperty("email_verified")
    private String emailVerified;
    @JsonProperty("name")
    private String name;
    @JsonProperty("preferred_username")
    private String userId;
    @JsonProperty("given_name")
    private String givenName;
    @JsonProperty("family_name")
    private String familyName;
    @JsonProperty("email")
    private String email;
}
