package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestOtpRequest {
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phone;

    @NotBlank(message = "Purpose is required")
    @Pattern(regexp = "^(registration|reset_password|login)$", 
             message = "Purpose must be either 'registration', 'reset_password', or 'login'")
    private String purpose;

    @NotBlank(message = "Device ID is required")
    private String deviceId;
} 