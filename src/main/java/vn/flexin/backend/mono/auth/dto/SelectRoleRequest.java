package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectRoleRequest {
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phone;

    @NotBlank(message = "Role is required")
    @Pattern(regexp = "^(jobSeeker|employer)$", message = "Role must be either 'jobSeeker' or 'employer'")
    private String role;

    @NotBlank(message = "Token is required")
    private String token;

    @NotBlank(message = "Device ID is required")
    private String deviceId;
} 