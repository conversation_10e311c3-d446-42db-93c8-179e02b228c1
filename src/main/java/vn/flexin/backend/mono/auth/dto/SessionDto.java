package vn.flexin.backend.mono.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionDto {
    private String deviceId;
    private String deviceName;
    private LocalDateTime createdAt;
    private LocalDateTime expiresAt;
    private boolean isCurrentDevice;
} 