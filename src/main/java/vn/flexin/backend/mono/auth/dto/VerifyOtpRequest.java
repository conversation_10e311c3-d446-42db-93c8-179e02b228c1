package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerifyOtpRequest {
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phone;

    @NotBlank(message = "OTP is required")
    @Pattern(regexp = "^[0-9]{6}$", message = "OTP must be 6 digits")
    private String otp;

    @NotBlank(message = "Token is required")
    private String token;

    @NotBlank(message = "Device Id is required")
    private String deviceId;

    @NotBlank(message = "Purpose is required")
    @Pattern(regexp = "^(registration|reset_password|create_password)$",
            message = "Purpose must be either 'registration', 'reset_password', or 'create_password'")
    private String purpose;
} 