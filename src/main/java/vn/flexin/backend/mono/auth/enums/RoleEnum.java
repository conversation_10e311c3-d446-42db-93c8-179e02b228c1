package vn.flexin.backend.mono.auth.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum RoleEnum {
    ADMIN("ADMIN"),
    EMPLOYER("EMPLOYER"),
    JOB_SEEKER("JOB_SEEKER");

    private final String value;

    public String getValue() {
        return this.value;
    }

    RoleEnum(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static RoleEnum fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Role type must be any of [" + getValues() + "]");
        }
    }

}
