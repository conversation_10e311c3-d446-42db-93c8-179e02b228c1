package vn.flexin.backend.mono.auth.service.keycloak;

import jakarta.ws.rs.NotFoundException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.*;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.permission.dto.request.CreatePermissionRequest;
import vn.flexin.backend.mono.permission.dto.request.UpdatePermissionRequest;
import vn.flexin.backend.mono.role.entity.Role;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Getter
public class AdminKeycloakService extends KeycloakService{

    @Autowired
    private Keycloak adminKeycloak;

    @Value("${keycloak.admin.client-id}")
    private String clientId;

    @Value("${keycloak.admin.client-secret}")
    private String clientSecret;

    @Value("${keycloak.default-role-name}")
    private String defaultRoleName;

    @Autowired
    private KeycloakTransactionManager keycloakTransactionManager;

    @Override
    public Keycloak getKeycloak() {
        return this.adminKeycloak;
    }

    @Override
    public RealmResource getRealmResource() {
        return getKeycloak().realm(getRealm());
    }

    public Pair<RoleRepresentation, Set<RoleRepresentation>> findRoleByName(String roleName) {
        RoleRepresentation role = getRealmResource().roles().get(roleName).toRepresentation();
        Set<RoleRepresentation> permissions = getRealmResource().roles().get(roleName).getRoleComposites();
        return Pair.of(role, permissions);
    }
    public Pair<RoleRepresentation, Set<RoleRepresentation>> findRoleById(String roleId) {
        try {
            RoleByIdResource rolesResource = getRealmResource().rolesById();
            RoleRepresentation role = rolesResource.getRole(roleId);
            Set<RoleRepresentation> permissions = rolesResource.getRoleComposites(roleId);
            return Pair.of(role, permissions);
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_ROLE);
        }
    }

    public RoleRepresentation findByName(String roleName) {
        try{
            RoleResource roleResource = getRealmResource().roles().get(roleName);
            return roleResource.toRepresentation();
        } catch (NotFoundException ex) {
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_ROLE);
        }
    }

    public RoleRepresentation findById(String roleId) {
        try {
            RoleByIdResource rolesResource = getRealmResource().rolesById();
            return rolesResource.getRole(roleId);
        } catch (NotFoundException ex) {
            return null;
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_ROLE);
        }
    }

    public String createRole(Role role) {
        return keycloakTransactionManager.executeTransaction(keycloak -> {
            RolesResource rolesResource = getRealmResource().roles();

            RoleRepresentation newRole = new RoleRepresentation();
            newRole.setName(role.getName());
            newRole.setDescription(role.getDescription());
            rolesResource.create(newRole);

            RoleRepresentation createdRole = rolesResource.get(role.getName()).toRepresentation();

            if (!CollectionUtils.isEmpty(role.getPermissions())) {
                List<RoleRepresentation> rolesToComposite = role.getPermissions().stream()
                        .map(permission -> getRealmResource().rolesById().getRole(permission.getKeycloakId()))
                        .toList();
                rolesResource.get(createdRole.getName()).addComposites(rolesToComposite);
            } else {
                RoleRepresentation defaultPermission = getRealmResource().roles().get(defaultRoleName).toRepresentation();
                rolesResource.get(createdRole.getName()).addComposites(List.of(defaultPermission));
            }

            return createdRole.getId();
        });
    }

    public RoleRepresentation createPermission(CreatePermissionRequest request) {
        RolesResource rolesResource = getRealmResource().roles();

        RoleRepresentation newRole = new RoleRepresentation();
        newRole.setName(request.getName());
        newRole.setDescription(request.getDescription());

        rolesResource.create(newRole);

        return rolesResource.get(request.getName()).toRepresentation();
    }

    public void updateRole(Role role) {
        keycloakTransactionManager.executeTransactionWithoutResult(keycloak -> {
            String roleId = role.getKeycloakId();
            RoleByIdResource rolesResource = getRealmResource().rolesById();
            var currentRole = rolesResource.getRole(roleId);

            currentRole.setDescription(role.getDescription());
            currentRole.setName(role.getName());
            rolesResource.updateRole(roleId, currentRole);

            if (currentRole.isComposite()) {
                Set<RoleRepresentation> currentComposites = rolesResource.getRoleComposites(roleId);

                List<RoleRepresentation> newComposites = role.getPermissions().stream()
                        .map(permission -> rolesResource.getRole(permission.getKeycloakId()))
                        .toList();

                List<RoleRepresentation> rolesToRemove = currentComposites.stream()
                        .filter(permission -> !newComposites.contains(permission))
                        .toList();
                rolesResource.deleteComposites(roleId, rolesToRemove);

                List<RoleRepresentation> rolesToAdd = newComposites.stream()
                        .filter(permission -> !currentComposites.contains(permission))
                        .toList();
                rolesResource.addComposites(roleId, rolesToAdd);
            }
        });
    }

    public void updatePermission(UpdatePermissionRequest request) {
        RolesResource rolesResource = getRealmResource().roles();
        var currentRole = findById(request.getKeycloakId());
        RoleResource roleResource = rolesResource.get(currentRole.getName());

        currentRole.setName(request.getName());
        roleResource.update(currentRole);
    }

    public void deleteRole(String roleId) {
        RoleRepresentation representation = findById(roleId);
        RoleResource resource = getRealmResource().roles().get(representation.getName());
        List<UserRepresentation> usersWithRole = resource.getUserMembers();

        if (!usersWithRole.isEmpty()) {
            throw new BadRequestException(ErrorMessage.ROLE_IN_USED);
        }

        getRealmResource().roles().deleteRole(representation.getName());
    }

    public void deletePermission(String permissionId) {
        RoleRepresentation representation = findById(permissionId);

        List<RoleRepresentation> compositeRoles = getRealmResource().roles().list().stream()
                .filter(RoleRepresentation::isComposite).toList();
        for (RoleRepresentation compositeRole : compositeRoles) {
            Set<RoleRepresentation> permissions =
                    getRealmResource().roles().get(compositeRole.getName()).getRoleComposites();
            if (permissions.stream().anyMatch(permission -> permission.getId().equals(representation.getId()))) {
                throw new BadRequestException(ErrorMessage.PERMISSION_ALREADY_IN_USED);
            }
        }
        getRealmResource().rolesById().deleteRole(representation.getId());
    }

    public void assignRoleToUser(String userId, List<String> roleIds) {
        UserResource userResource = getRealmResource().users().get(userId);
        List<RoleRepresentation> userRoles = userResource.roles().realmLevel().listAll();

        List<RoleRepresentation> roles = new ArrayList<>();
        for (String roleId : roleIds) {
            RoleRepresentation role = findById(roleId);

            if (role == null) {
                throw new BadRequestException(ErrorMessage.ROLE_NOT_FOUND);
            }

            if (userRoles.stream().anyMatch(r -> r.getId().equals(roleId))) {
                continue;
            }
            roles.add(role);
        }

        userResource.roles().realmLevel().add(roles);
    }

    public void removeRoleOfUser(String userId, String roleId) {
        RoleRepresentation role = findById(roleId);

        if (role == null) {
            throw new BadRequestException(ErrorMessage.ROLE_NOT_FOUND);
        }

        UserResource userResource = getRealmResource().users().get(userId);

        List<RoleRepresentation> userRoles = userResource.roles().realmLevel().listAll();
        boolean hasRole = userRoles.stream().anyMatch(r -> r.getId().equals(roleId));

        if (!hasRole) {
            throw new BadRequestException(ErrorMessage.USER_NOT_ASSIGNED_ROLE);
        }
        userResource.roles().realmLevel().remove(Collections.singletonList(role));
    }

    public void addPermissionsToRole(String roleId, List<String> permissionIds) {
        RoleByIdResource roleByIdResource = getRealmResource().rolesById();

        List<RoleRepresentation> permissions = new ArrayList<>();

        permissionIds.forEach(permissionId -> {
            RoleRepresentation permission = roleByIdResource.getRole(permissionId);
            permissions.add(permission);
        });

        roleByIdResource.addComposites(roleId, permissions);
    }

    public void removePermissionsOfRole(String roleId, List<String> permissionIds) {
        RoleByIdResource roleByIdResource = getRealmResource().rolesById();
        List<RoleRepresentation> permissions = new ArrayList<>();

        permissionIds.forEach(permissionId -> {
            RoleRepresentation permission = roleByIdResource.getRole(permissionId);
            permissions.add(permission);
        });

        roleByIdResource.deleteComposites(roleId, permissions);
    }

    public Set<String> getAllPermissions() {
        return getRealmResource().roles().list().stream()
                .filter(representation -> !representation.isComposite())
                .map(RoleRepresentation::getName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public void updateRoleOfUser(String userId, List<String> roleIds) {
        UserResource userResource = getRealmResource().users().get(userId);
        List<RoleRepresentation> userRoles = userResource.roles().realmLevel().listAll();

        List<RoleRepresentation> roleToAdds = new ArrayList<>();
        for (String roleId : roleIds) {
            RoleRepresentation role = findById(roleId);

            if (role == null) {
                throw new BadRequestException(ErrorMessage.ROLE_NOT_FOUND);
            }

            if (userRoles.stream().anyMatch(r -> r.getId().equals(roleId))) {
                continue;
            }
            roleToAdds.add(role);
        }

        List<RoleRepresentation> roleToRemoves = userRoles.stream().filter(old -> !roleIds.contains(old.getId())).toList();

        userResource.roles().realmLevel().add(roleToAdds);
        userResource.roles().realmLevel().remove(roleToRemoves);
    }
}
