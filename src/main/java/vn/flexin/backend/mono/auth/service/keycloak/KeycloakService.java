package vn.flexin.backend.mono.auth.service.keycloak;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import vn.flexin.backend.mono.auth.dto.JwtToken;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.UnauthorizedException;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Slf4j
@Service
public abstract class KeycloakService {

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.auth-server-url}")
    private String authServerUrl;

    @Autowired
    private WebClient webClient;

    public abstract Keycloak getKeycloak();

    public abstract String getClientId();

    public abstract String getClientSecret();

    public RealmResource getRealmResource() {
        return getKeycloak().realm(getRealm());
    }


    public JwtToken getToken(String username, String password) {
        try {
            Keycloak keycloak = getGrantTypePasswordKeycloak(username, password);

            String accessToken = keycloak.tokenManager().getAccessToken().getToken();
            String refreshToken = keycloak.tokenManager().refreshToken().getRefreshToken();

            return JwtToken.builder()
                    .access_token(accessToken)
                    .refresh_token(refreshToken)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_TOKEN);
        }
    }

    protected Keycloak getGrantTypePasswordKeycloak(String username, String password) {
        return KeycloakBuilder.builder()
                .serverUrl(getAuthServerUrl())
                .realm(getRealm())
                .clientId(getClientId())
                .clientSecret(getClientSecret())
                .grantType(AuthConstant.PASSWORD)
                .username(username)
                .password(password)
                .build();
    }

    public void updatePassword(String keycloakUserId, String password) {
        try {
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(password);
            credential.setTemporary(false);

            UserResource userResource = getRealmResource().users().get(keycloakUserId);
            userResource.resetPassword(credential);
        } catch (Exception e) {
            log.error("Error updating password in Keycloak", e);
            throw new BadRequestException(ErrorMessage.CAN_NOT_UPDATE_PASSWORD);
        }
    }

    protected String extractUserId(String locationHeader) {
        if (locationHeader != null) {
            String[] parts = locationHeader.split("/");
            return parts[parts.length - 1];
        }
        return null;
    }

    public void logoutUser(String userId) {
        try {
            getRealmResource().users().get(userId).logout();
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_LOG_OUT);
        }
        ResponseEntity.ok().build();
    }

    public UserRepresentation getCurrentUser() {
        try{
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication == null) {
                throw new UnauthorizedException(ErrorMessage.USER_NOT_AUTHENTICATE);
            }

            User principal = (User) authentication.getPrincipal();
            String userId = principal.getKeycloakUserId();

            // Get the user details from Keycloak using the userId
            UserResource userResource = getRealmResource().users().get(userId);

            UserRepresentation userRepresentation = userResource.toRepresentation();
            userRepresentation.setUsername(userRepresentation.getUsername().toUpperCase());

            return userRepresentation;
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new UnauthorizedException("User not found");
        }
    }

    public JwtToken refreshToken(String refreshToken) {
        try {
            String tokenEndpoint = String.format("%s/realms/%s/protocol/openid-connect/token", authServerUrl, realm);
            String body = String.format("grant_type=refresh_token&client_id=%s&client_secret=%s&refresh_token=%s",
                    getClientId(), getClientSecret(), refreshToken);

            var tokenResponse = webClient
                    .post()
                    .uri(tokenEndpoint)
                    .header(AuthConstant.CONTENT_TYPE, "application/x-www-form-urlencoded")
                    .bodyValue(body)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .block();
            if (tokenResponse == null) {
                throw new UnauthorizedException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_TOKEN);
            }
            return JwtToken.builder()
                    .access_token((String) tokenResponse.get(AuthConstant.ACCESS_TOKEN))
                    .refresh_token(refreshToken)
                    .build();
        } catch (Exception e) {
            throw new UnauthorizedException(ErrorMessage.CAN_NOT_GET_KEYCLOAK_TOKEN);
        }
    }

    public UserRepresentation getUserByUserName(String userName) {
        var representation = getRealmResource().users().get(userName).toRepresentation();
        if (representation == null) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }
        return representation;
    }

    public boolean hasAdminRoleGroup(UserRepresentation user) {
        try {

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public LocalDateTime getAttributeAsLocalDateTime(Map<String, List<String>> attributes, String key) {
        if (attributes != null && attributes.containsKey(key)) {
            String value = attributes.get(key).getFirst();
            try {
                return LocalDateTime.parse(value);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public boolean getAttributeAsBoolean(Map<String, List<String>> attributes, String key) {
        if (attributes != null && attributes.containsKey(key)) {
            String value = attributes.get(key).getFirst();
            return Boolean.parseBoolean(value);
        }
        return false;
    }

}
