package vn.flexin.backend.mono.auth.service.keycloak;

import jakarta.ws.rs.core.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.auth.dto.JwtToken;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.exception.BadRequestException;

import java.util.Collections;
import java.util.HashMap;

@Getter
@Service
@Slf4j
public class UserKeycloakService extends KeycloakService{

    @Autowired
    private Keycloak userKeycloak;

    @Value("${keycloak.user.client-id}")
    private String clientId;

    @Value("${keycloak.user.client-secret}")
    private String clientSecret;

    @Override
    public Keycloak getKeycloak() {
        return this.userKeycloak;
    }

    public ResponseEntity<String> createNormalUser(String ulid, String phoneNumber, String deviceId, String password, String email) {
        try {

            UserRepresentation user = new UserRepresentation();
            user.setEnabled(true);
            user.setUsername(ulid);
            user.setEmail(email);
            user.setEmailVerified(true);
            user.setAttributes(new HashMap<>());
            user.getAttributes().put(AuthConstant.DEVICE_ID, Collections.singletonList(deviceId));
            user.getAttributes().put(AuthConstant.PHONE_NUMBER, Collections.singletonList(phoneNumber));

            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(password);
            credential.setTemporary(false);
            user.setCredentials(Collections.singletonList(credential));

            log.info("Sending request to Keycloak to create user");

            Response response = userKeycloak.realm(getRealm()).users().create(user);
            int status = response.getStatus();

            if (status == 201) {
                String locationHeader = response.getHeaderString(AuthConstant.LOCATION);
                String userId = extractUserId(locationHeader);
                log.info("User created successfully in Keycloak");
                return ResponseEntity.status(status).body(userId);
            } else {
                String responseBody = response.readEntity(String.class);
                log.error("Failed to create user in Keycloak. Status: {}, Response: {}", status, responseBody);
                return ResponseEntity.status(status).body("Failed to create user in Keycloak: " + responseBody);
            }
        } catch (Exception e) {
            log.error("Error creating user in Keycloak: {}", e.getMessage(), e);
            throw new BadRequestException(ErrorMessage.CAN_NOT_CREATE_USER);
        }
    }

    public JwtToken getUserToken (String username, String password, String deviceId, String keycloakUserId) {
        JwtToken token = getToken(username, password);

        UsersResource usersResource = getRealmResource().users();
        UserRepresentation userRepresentation = usersResource.get(keycloakUserId).toRepresentation();
        userRepresentation.singleAttribute(AuthConstant.DEVICE_ID, deviceId);
        usersResource.get(keycloakUserId).update(userRepresentation);

        return token;
    }

    public void deleteUser(String keycloakUserId) {
        getRealmResource().users().delete(keycloakUserId);
    }

}
