package vn.flexin.backend.mono.auth.service.keycloak.impl;

import org.keycloak.admin.client.Keycloak;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.auth.service.keycloak.KeycloakTransactionManager;

import java.util.function.Consumer;
import java.util.function.Function;

@Service
public class KeycloakTransactionManagerImpl implements KeycloakTransactionManager {

    private final Keycloak adminKeycloak;

    @Autowired
    public KeycloakTransactionManagerImpl(@Qualifier("adminKeycloak") Keycloak adminKeycloak) {
        this.adminKeycloak = adminKeycloak;
    }

    @Override
    public <T> T executeTransaction(Function<Keycloak, T> operation) {
        try {
            return operation.apply(adminKeycloak);
        } catch (Exception e) {
            throw new RuntimeException("Transaction failed", e);
        }
    }

    @Override
    public void executeTransactionWithoutResult(Consumer<Keycloak> operation) {
        try {
            operation.accept(adminKeycloak);
        } catch (Exception e) {
            throw new RuntimeException("Transaction failed", e);
        }
    }
}
