package vn.flexin.backend.mono.auth.service.mobile;

import org.springframework.http.ResponseEntity;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.user.dto.CreateUserResponse;
import vn.flexin.backend.mono.user.dto.UpdateRoleRequest;
import vn.flexin.backend.mono.user.dto.UserDeviceResponse;

import java.util.List;

public interface MobileAuthService {
    AuthResponse login(String clientIp, LoginRequest loginRequest);
    
    TempTokenResponse register(String clientIp, RegisterRequest registerRequest);
    
    VerifyOtpResponse verifyOtp(VerifyOtpRequest verifyOtpRequest);
    
    TempTokenResponse createPassword(CreatePasswordRequest createPasswordRequest);
    
    AuthResponse selectRole(SelectRoleRequest selectRoleRequest);
    
    AuthResponse socialLogin(SocialLoginRequest socialLoginRequest);
    
    TempTokenResponse requestOtp(String clientIp, RequestOtpRequest requestOtpRequest);
    
    void resetPassword(ResetPasswordRequest resetPasswordRequest);
    
    List<UserDeviceResponse> getSessions(String deviceId);

    void logout();
    
    void logoutDevice(LogoutDeviceRequest logoutDeviceRequest);
    
    void logoutAllDevices();
    
    TokenDto refreshToken(RefreshTokenRequest refreshTokenRequest);

    CreateUserResponse updateRole(UpdateRoleRequest updateRoleRequest);
} 