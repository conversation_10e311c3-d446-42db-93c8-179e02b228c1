package vn.flexin.backend.mono.auth.service.mobile.impl;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.enums.RoleEnum;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.auth.service.mobile.MobileAuthService;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.UnauthorizedException;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.common.util.SecurityUtil;
import vn.flexin.backend.mono.notification.service.SmsService;
import vn.flexin.backend.mono.user.dto.CreateUserResponse;
import vn.flexin.backend.mono.user.dto.UpdateRoleRequest;
import vn.flexin.backend.mono.user.dto.UserDeviceResponse;
import vn.flexin.backend.mono.user.entity.OtpEntry;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;
import vn.flexin.backend.mono.user.enums.OtpEntryStatus;
import vn.flexin.backend.mono.user.enums.TokenPurpose;
import vn.flexin.backend.mono.user.service.OtpEntryService;
import vn.flexin.backend.mono.user.service.UserDeviceService;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class MobileAuthServiceImpl implements MobileAuthService {

    private final UserService userService;
    private final UserKeycloakService userKeycloakService;
    private final PasswordEncoder passwordEncoder;
    private final UserDeviceService userDeviceService;
    private final OtpEntryService otpEntryService;
    private final SmsService smsService;

    @Value("${jwt.temp-token.secret}")
    private String secretKey;


    @Override
    public AuthResponse login(String clientIp, LoginRequest request) {
        try {
            User user = userService.getUserByPhoneNumber(request.getPhone());
            if (user == null) {
                throw new UnauthorizedException(ErrorMessage.INVALID_PHONE_NUMBER_PASSWORD);
            }

            // Get token from Keycloak
            JwtToken token = userKeycloakService.getUserToken(user.getUlid(), request.getPassword(), request.getDeviceId(), user.getKeycloakUserId());
            CreateUserResponse userResponse = new CreateUserResponse(user);

            updateOrCreateNewUserDeviceOfUser(request, user, clientIp);

            return AuthResponse.builder()
                    .token(token.getAccess_token())
                    .refreshToken(token.getRefresh_token())
                    .user(userResponse)
                    .build();
        } catch (Exception e) {
            throw new UnauthorizedException(ErrorMessage.INVALID_PHONE_NUMBER_PASSWORD);
        }
    }

    private void updateOrCreateNewUserDeviceOfUser(LoginRequest request, User user, String clientIp) {
        UserDevice userDevice = userDeviceService.getByDeviceIdAndPhoneNumber(request.getDeviceId(), request.getPhone());
        if (userDevice == null) {
            userDevice = new UserDevice(request, user, clientIp);
            userDeviceService.save(userDevice);
            return;
        }
        userDevice.setLogout(false);
        userDevice.setLocation(request.getLocation());
        userDevice.setClientIp(clientIp);
        userDeviceService.save(userDevice);
    }

    @Override
    @Transactional
    public TempTokenResponse register(String clientIp, RegisterRequest request) {

        validateNumberOfRequests(request.getPhone());
        UserDevice userDevice = null;
        // Check if phone number already exists
        List<UserDevice> userDevices = userDeviceService.getByPhoneNumber(request.getPhone());
        if (!CollectionUtils.isEmpty(userDevices)) {
            if (userDevices.stream().anyMatch(UserDevice::isSignUpComplete)) {
                throw new BadRequestException(ErrorMessage.PHONE_NUMBER_TAKEN);
            }
            userDevice = userDevices.stream().filter(item  ->
                    item.getDeviceId().equals(request.getDeviceId())
                    && item.getPhoneNumber().equals(request.getPhone())
            ).findAny().orElse(null);
        }
        if (userDevice == null) {
            userDevice = new UserDevice(request.getDeviceId(), request.getPhone(), request.getDeviceName(), clientIp);
            userDevice = userDeviceService.save(userDevice);
        }

        Map<String, Object> claims = setClaims(TokenPurpose.REGISTRATION, userDevice.getDeviceId());

        // Generate and save temporary token with phone number
        String token = SecurityUtil.generateTempToken(request.getPhone(), claims, secretKey);

        // TODO: Send OTP to phone number
        //temporary set otp
        String otp = generateSmsOtp();

        OtpEntry otpEntry = new OtpEntry(otp, userDevice, TokenPurpose.REGISTRATION);
        otpEntryService.save(otpEntry);

        smsService.sendOtp(request.getPhone(), otp);

        return TempTokenResponse.builder()
                .tempToken(token)
                .build();
    }

    private Map<String, Object> setClaims (TokenPurpose purpose, String deviceId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(AuthConstant.PURPOSE, purpose);
        claims.put(AuthConstant.DEVICE_ID, deviceId);
        return claims;
    }

    private void validateNumberOfRequests(String phoneNumber) {
        LocalDateTime limitDateTime = LocalDateTime.now().minus(AuthConstant.TIME_15_MINUTES_IN_MILLISECOND, ChronoUnit.MILLIS);
        if (otpEntryService.countOtpByPhoneNumberInFrameTime(phoneNumber, limitDateTime) >= AuthConstant.MAX_REQUEST) {
            throw new BadRequestException(ErrorMessage.TOO_MANY_REQUESTS);
        }
    }

    private String generateSmsOtp() {
//        return CommonUtil.genOtp();
        return "000000";
    }

    @Override
    public VerifyOtpResponse verifyOtp(VerifyOtpRequest request) {
        // verify token
        if (verifyTempToken(request.getToken(), request.getPhone(), request.getDeviceId(), TokenPurpose.fromString(request.getPurpose()))) {
            throw new BadRequestException(ErrorMessage.INVALID_TOKEN);
        }

        validateNumberOfRequests(request.getPhone());

        OtpEntry otpEntry = otpEntryService.getLatestForVerification(request.getPhone(), request.getDeviceId());

        if (otpEntry == null || !Objects.equals(otpEntry.getOtp(), request.getOtp())) {
            throw new BadRequestException("Otp is not valid.");
        }

        if (otpEntry.getExpiredAt().isBefore(CommonUtil.getCurrentUTCTime())) {
            throw new BadRequestException("Otp is expired.");
        }

        otpEntry.setStatus(OtpEntryStatus.USED);
        otpEntryService.save(otpEntry);

        UserDevice userDevice = otpEntry.getUserDevice();
        userDevice.setPhoneVerified(true);
        userDevice.setPhoneVerifiedAt(CommonUtil.getCurrentUTCTime());
        userDeviceService.save(userDevice);

        TokenPurpose nextPurpose = TokenPurpose.fromString(request.getPurpose()) == TokenPurpose.REGISTRATION
                ? TokenPurpose.CREATE_PASSWORD
                : TokenPurpose.RESET_PASSWORD;
        Map<String, Object> claims = setClaims(nextPurpose, request.getDeviceId());
        String token = SecurityUtil.generateTempToken(request.getPhone(), claims, secretKey);

        return VerifyOtpResponse.builder()
                .isVerified(true)
                .tempToken(token)
                .build();
    }

    @Override
    public TempTokenResponse createPassword(CreatePasswordRequest request) {
        // verify token
        if (verifyTempToken(request.getToken(), request.getPhone(), request.getDeviceId(), TokenPurpose.CREATE_PASSWORD)) {
            throw new BadRequestException(ErrorMessage.INVALID_TOKEN);
        }

        UserDevice userDevice = userDeviceService.getByDeviceIdAndPhoneNumber(request.getDeviceId(), request.getPhone());
        if (userDevice == null) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }

        if (!userDevice.isPhoneVerified()) {
            throw new BadRequestException(ErrorMessage.PHONE_IS_NOT_VERIFIED);
        }

        if (userDevice.isSignUpComplete()) {
            throw new BadRequestException(ErrorMessage.USER_REGISTERED);
        }

        userDevice.setTempPassword(request.getPassword());

        userDeviceService.save(userDevice);

        Map<String, Object> claims = setClaims(TokenPurpose.SELECT_ROLE, userDevice.getDeviceId());

        // Generate and save temporary token with phone number
        String token = SecurityUtil.generateTempToken(request.getPhone(), claims, secretKey);

        return TempTokenResponse.builder()
                .tempToken(token).build();
    }

    @Override
    @Transactional
    public AuthResponse selectRole(SelectRoleRequest request) {
        // verify token
        if (verifyTempToken(request.getToken(), request.getPhone(), request.getDeviceId(), TokenPurpose.SELECT_ROLE)) {
            throw new BadRequestException(ErrorMessage.INVALID_TOKEN);
        }

        UserDevice userDevice = userDeviceService.getByDeviceIdAndPhoneNumber(request.getDeviceId(), request.getPhone());
        if (userDevice == null) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }

        if (!userDevice.isPhoneVerified()) {
            throw new BadRequestException(ErrorMessage.PHONE_IS_NOT_VERIFIED);
        }

        if (userDevice.isSignUpComplete()) {
            throw new BadRequestException(ErrorMessage.USER_REGISTERED);
        }

        if(request.getRole().equals(RoleEnum.ADMIN.getValue())) {
            throw new BadRequestException(ErrorMessage.ROLE_INVALID);
        }

        User newUser = userService.createNewMobileUser(userDevice, request);
        ResponseEntity<String> response = userKeycloakService.createNormalUser(newUser.getUlid(), newUser.getPhoneNumber(),
                userDevice.getDeviceId(), userDevice.getTempPassword(), null);
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_CREATE_USER);
        }
        String keycloakUserId = response.getBody();
        if (keycloakUserId != null) {
            newUser.setKeycloakUserId(keycloakUserId);
            userService.save(newUser);
        }
        JwtToken token = userKeycloakService.getUserToken(newUser.getUlid(), userDevice.getTempPassword(), userDevice.getDeviceId(), keycloakUserId);
        userDeviceService.updateUserDeviceAfterUserFullySignUp(newUser, userDevice);

        CreateUserResponse userResponse = new CreateUserResponse(newUser);
        return AuthResponse.builder()
                .refreshToken(token.getRefresh_token())
                .token(token.getAccess_token())
                .user(userResponse)
                .build();
    }

    @Override
    public TempTokenResponse requestOtp(String clientIp, RequestOtpRequest request) {
        User user = userService.getUserByPhoneNumber(request.getPhone());
        if (user == null || !user.isActive() || !user.isPhoneVerified()) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }

        validateNumberOfRequests(request.getPhone());

        String otp = generateSmsOtp();

        UserDevice userDevice = userDeviceService.getByDeviceIdAndPhoneNumber(request.getDeviceId(), request.getPhone());
        if (userDevice == null) {
            userDevice = new UserDevice(request.getDeviceId(), request.getPhone(), AuthConstant.UNKNOWN_DEVICE, clientIp);
            userDevice = userDeviceService.save(userDevice);
        }

        OtpEntry otpEntry = new OtpEntry(otp, userDevice, TokenPurpose.fromString(request.getPurpose()));
        otpEntryService.save(otpEntry);

        Map<String, Object> claims = setClaims(TokenPurpose.valueOf(request.getPurpose().toUpperCase()), userDevice.getDeviceId());
        String token = SecurityUtil.generateTempToken(request.getPhone(), claims, secretKey);

        return TempTokenResponse.builder()
                .tempToken(token)
                .build();
    }

    @Override
    public void resetPassword(ResetPasswordRequest request) {
        verifyTempToken(request.getToken(), request.getPhone(), request.getDeviceId(), TokenPurpose.RESET_PASSWORD);

        User user = userService.getUserByPhoneNumber(request.getPhone());
        if (user == null || !user.isActive()) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userService.save(user);
        userKeycloakService.updatePassword(user.getKeycloakUserId(), user.getPassword());
    }

    @Override
    public List<UserDeviceResponse> getSessions(String deviceId) {
        UserRepresentation currentUser = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(currentUser.getUsername());

        deviceId = deviceId == null ? CommonUtil.getDeviceIdAndPhoneNumber(currentUser).getFirst() : deviceId;
        return userService.getUserDevices(user.getId(), deviceId);
    }

    @Override
    public void logout() {
        UserRepresentation currentUser = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(currentUser.getUsername());

        String deviceId = CommonUtil.getDeviceIdAndPhoneNumber(currentUser).getFirst();
        userDeviceService.logoutDevice(user.getId(), deviceId);
        userKeycloakService.logoutUser(user.getKeycloakUserId());
    }

    @Override
    public void logoutDevice(LogoutDeviceRequest request) {
        String ulid = SecurityUtil.getCurrentUserUlid();

        User user = userService.getUserByUlid(ulid);
        userDeviceService.logoutDevice(user.getId(), request.getDeviceId());
    }

    @Override
    public void logoutAllDevices() {
        UserRepresentation currentUser = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(currentUser.getUsername());

        String deviceId = CommonUtil.getDeviceIdAndPhoneNumber(currentUser).getFirst();
        userDeviceService.logoutAllDeviceExclude(user.getId(), deviceId);
    }

    @Override
    public TokenDto refreshToken(RefreshTokenRequest refreshTokenRequest) {
        JwtToken token = userKeycloakService.refreshToken(refreshTokenRequest.getRefreshToken());

        return TokenDto.builder()
                .token(token.getAccess_token())
                .refreshToken(refreshTokenRequest.getRefreshToken())
                .build();
    }

    @Override
    public CreateUserResponse updateRole(UpdateRoleRequest updateRoleRequest) {
        User user = userService.getById(updateRoleRequest.getId());
        user.setRole(updateRoleRequest.getRole());
        userService.save(user);
        return new CreateUserResponse(user);
    }

    @Override
    public AuthResponse socialLogin(SocialLoginRequest socialLoginRequest) {
        // TODO: Implement social login
        return null;
    }

    public boolean verifyTempToken(String token, String phoneNumber, String deviceId, TokenPurpose purpose) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(Keys.hmacShaKeyFor(secretKey.getBytes()))
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            String subject = claims.getSubject();
            String tokenDeviceId = claims.get(AuthConstant.DEVICE_ID, String.class);
            String tokenPurpose = claims.get(AuthConstant.PURPOSE, String.class);
            Date expiryDate = claims.getExpiration();

            // Validate the token's claims
            return !subject.equals(phoneNumber) ||
                    !tokenDeviceId.equals(deviceId) ||
                    !tokenPurpose.equals(purpose.toString()) ||
                    isTokenExpired(expiryDate);
        } catch (SignatureException e) {
            log.error("Invalid token signature", e);
            return true;  // Token signature is invalid
        } catch (Exception e) {
            log.error("Error verifying temporary token", e);
            return true;  // Other errors such as malformed token
        }
    }

    private boolean isTokenExpired(Date expiryDate) {
        return expiryDate != null && expiryDate.before(new Date());
    }

} 