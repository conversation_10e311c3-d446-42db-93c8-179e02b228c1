package vn.flexin.backend.mono.auth.util;

public class AuthConstant {
    public static final String ACCESS_TOKEN = "access_token";
    public static final String PURPOSE = "purpose";
    public static final String EMAIL = "email";
    public static final long TIME_15_MINUTES_IN_MILLISECOND = 9_000_000;
    public static final String PASSWORD = "password";
    public static final String DEVICE_ID = "deviceId";
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final String LOCATION = "Location";
    public static final String UNKNOWN_DEVICE = "Unknown Device";
    public static final String CREATED_AT = "createdAt";
    public static final String UPDATED_AT = "updatedAt";
    public static final String IS_DEFAULT = "isDefault";
    public static final String ADMIN_ROLE = "admin";

    public static final int MAX_REQUEST = 3;
    public static final String CONTENT_TYPE = "Content-Type";

    public static final String PHONE_REGEX = "^(?:\\+84|0)(?:9[0-9]|3[2-9]|5[6-8|9]|7[0-9]|8[1-9])\\d{7}$";
    public static final String PASSWORD_REGEX = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=!])(?=\\S+$).{8,}$";
    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BASIC_AUTH_PREFIX = "Basic ";
    public static final String API_KEY = "apikey";

    private AuthConstant(){}

}
