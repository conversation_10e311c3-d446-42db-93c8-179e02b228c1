package vn.flexin.backend.mono.common.config;

import lombok.Getter;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import vn.flexin.backend.mono.auth.service.keycloak.KeycloakTransactionManager;
import vn.flexin.backend.mono.auth.service.keycloak.impl.KeycloakTransactionManagerImpl;

@Getter
@Configuration
public class KeycloakConfig {

    @Value("${keycloak.auth-server-url}")
    private String authServerUrl;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.user.client-id}")
    private String userClientId;

    @Value("${keycloak.user.client-secret}")
    private String userClientSecret;

    @Value("${keycloak.admin.client-id}")
    private String adminClientId;

    @Value("${keycloak.admin.client-secret}")
    private String adminClientSecret;

    @Value("${spring.security.oauth2.resource-server.jwt.jwk-set-uri}")
    private String publicKeyUrl;

    @Bean(name = "userKeycloak")
    public Keycloak userKeycloak() {
        return KeycloakBuilder.builder()
                .serverUrl(authServerUrl)
                .realm(realm)
                .clientId(userClientId)
                .grantType("client_credentials")
                .clientSecret(userClientSecret)
                .build();
    }

    @Bean(name = "adminKeycloak")
    public Keycloak adminKeycloak() {
        return KeycloakBuilder.builder()
                .serverUrl(authServerUrl)
                .realm(realm)
                .clientId(adminClientId)
                .grantType("client_credentials")
                .clientSecret(adminClientSecret)
                .build();
    }

    @Bean
    public KeycloakTransactionManager keycloakTransactionManager(@Qualifier("adminKeycloak") Keycloak adminKeycloak) {
        return new KeycloakTransactionManagerImpl(adminKeycloak);
    }
} 