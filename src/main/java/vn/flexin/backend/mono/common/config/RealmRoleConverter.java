package vn.flexin.backend.mono.common.config;

import lombok.AllArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.auth.service.keycloak.KeycloakService;
import vn.flexin.backend.mono.auth.util.AuthConstant;

import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class RealmRoleConverter implements Converter<Jwt, AbstractAuthenticationToken> {
    
    private final AdminKeycloakService adminKeycloakService;
    
    @Override
    public AbstractAuthenticationToken convert(Jwt source) {
        Map<String, Collection<String>> realmAccess = source.getClaim("realm_access");
        Collection<String> roles = realmAccess.get("roles");
        var converterRoles = new ArrayList<SimpleGrantedAuthority>();

        if (roles.contains(AuthConstant.ADMIN_ROLE)) {
            Set<String> allPermissions = adminKeycloakService.getAllPermissions();
            for (String permission : allPermissions) {
                if (permission != null) {
                    converterRoles.add(new SimpleGrantedAuthority(permission));
                }
            }
        } else {
            for (String role : roles) {
                converterRoles.add(new SimpleGrantedAuthority(role));
            }
        }
        return new JwtAuthenticationToken(source, converterRoles);
    }

}
