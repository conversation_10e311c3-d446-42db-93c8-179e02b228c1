package vn.flexin.backend.mono.common.dto;

import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApiResponseDto<T> {
    private T data;
    private List<ErrorDto> errors;
    private String message;
    private Integer statusCode;
    private boolean success;

    public static <T> ApiResponseDto<T> success(T data, String message) {
        return ApiResponseDto.<T>builder()
                .data(data)
                .message(message)
                .statusCode(200)
                .success(true)
                .build();
    }

    public static <T> ApiResponseDto<T> success(String message, T data) {
        return ApiResponseDto.<T>builder()
                .data(data)
                .message(message)
                .statusCode(200)
                .success(true)
                .build();
    }

    public static <T> ApiResponseDto<T> success(T data) {
        return success(data, null);
    }

    public static <T> ApiResponseDto<T> error(String message, Integer statusCode) {
        return ApiResponseDto.<T>builder()
                .success(false)
                .statusCode(statusCode)
                .message(message)
                .build();
    }
}
