package vn.flexin.backend.mono.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import vn.flexin.backend.mono.common.util.Constant;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseFilter<T> implements SpecificationFilter<T> {

    protected static final String CREATED_AT = "createdAt";

    protected static final String UPDATED_AT = "updatedAt";

    protected static final String CREATED_BY = "createdBy";

    protected static final String LAST_MODIFIED_BY = "lastModifiedBy";

    @Builder.Default
    protected int page = 0;
    @Builder.Default
    protected int limit = 20;

    protected String sortBy;

    protected String sortOrder;

    protected String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? "id" : sortBy;
    }

    protected String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_DESC : sortOrder;
    }

    @Override
    public Pageable toPageable() {
        if (StringUtils.isEmpty(getSortBy())) {
            return PageRequest.of(page, limit);
        }
        Sort.Order order = Constant.SORT_ASC.equalsIgnoreCase(getSortOrder())
                ? Sort.Order.asc(getSortBy())
                : Sort.Order.desc(getSortBy());
        return PageRequest.of(page, limit, Sort.by(order));
    }
}