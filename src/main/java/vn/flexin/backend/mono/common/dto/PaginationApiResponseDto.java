package vn.flexin.backend.mono.common.dto;

import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class PaginationApiResponseDto<T> extends ApiResponseDto<T>{
    private PaginationResponse paginationMetadata;

    public static <T> PaginationApiResponseDto<T> success(String message, T data, PaginationResponse paging) {
        PaginationApiResponseDto<T> response = new PaginationApiResponseDto<>();
        response.setSuccess(true);
        response.setMessage(message);
        response.setStatusCode(200);
        response.setData(data);
        response.setPaginationMetadata(paging);

        return response;
    }
}
