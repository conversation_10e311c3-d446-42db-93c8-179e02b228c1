package vn.flexin.backend.mono.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class PaginationResponse {
    private int currentPage;
    private int totalPages;
    private int totalItems;
    private int itemsPerPage;
    @JsonProperty("hasMorePages")
    private boolean hasMorePages;
    @JsonProperty("hasPreviousPage")
    private boolean hasPreviousPage;
    private int firstItemIndex;
    private int lastItemIndex;

    public PaginationResponse(int pageSize, int pageNumber, int totalItems) {
        this.itemsPerPage = pageSize;
        this.currentPage = pageNumber;
        this.totalItems = totalItems;
        this.totalPages = calculateTotalPages();
        this.firstItemIndex = calculateFirstItemIndex();
        this.lastItemIndex = calculateLastItemIndex();
        this.hasMorePages = this.currentPage < this.totalPages;
        this.hasPreviousPage = this.currentPage > 1;
    }

    // Calculate total pages based on total items and page size
    private int calculateTotalPages() {
        if (itemsPerPage <= 0) return 0;
        return (totalItems + itemsPerPage - 1) / itemsPerPage;
    }

    // Calculate the index of the first item on the current page
    private int calculateFirstItemIndex() {
        return (currentPage - 1) * itemsPerPage + 1;
    }

    // Calculate the index of the last item on the current page
    private int calculateLastItemIndex() {
        int lastIndex = currentPage * itemsPerPage;
        return Math.min(lastIndex, totalItems);
    }

}
