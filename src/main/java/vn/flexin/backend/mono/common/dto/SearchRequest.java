package vn.flexin.backend.mono.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class SearchRequest {
    @NotNull
    @Min(value = 1, message = "Page must ber greater or equal 1.")
    private Integer pageNumber;
    
    @NotNull
    @Min(value = 1, message = "Page size must be greater than or equals 1.")
    private Integer pageSize;
    
    private String keyword;
    
    private Boolean ascending;
} 