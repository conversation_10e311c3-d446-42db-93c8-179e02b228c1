package vn.flexin.backend.mono.common.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.enums.ClientSystem;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity()
@Table(name = "t_api_auth_header_params")
public class ApiAuthHeaderParam {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String apiKey;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ClientSystem system;

    private boolean active;
}
