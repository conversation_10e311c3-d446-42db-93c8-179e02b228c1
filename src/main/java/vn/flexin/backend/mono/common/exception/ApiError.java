package vn.flexin.backend.mono.common.exception;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.enums.AppStatus;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class ApiError {

    private AppStatus status;
    private String message;
    private List<String> errors;

    public ApiError(AppStatus status, String message, List<String> errors) {
        super();
        this.status = status;
        this.message = message;
        this.errors = errors;
    }

    public ApiError(AppStatus status, String message, String error) {
        super();
        this.status = status;
        this.message = message;
        errors = Collections.singletonList(error);
    }
}
