package vn.flexin.backend.mono.common.exception;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.util.ValidEmail;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@ControllerAdvice
public class ErrorHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException exception,
                                                                     ServletWebRequest webRequest) {
        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<?> violation : exception.getConstraintViolations()) {
            String error = ValidEmail.INVALID_EMAIL_MESSAGE.equals(violation.getMessage()) ?
                    ValidEmail.INVALID_EMAIL_MESSAGE : violation.getPropertyPath() + ": " + violation.getMessage();
            errors.add(error);
        }
        ApiError apiError = new ApiError(AppStatus.BAD_REQUEST, exception.getMessage(), errors);
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus().value());
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex,
            HttpHeaders headers,
            HttpStatusCode status,
            WebRequest request) {
        List<String> errors = new ArrayList<String>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(error.getField() + ": " + error.getDefaultMessage());
        }
        for (ObjectError error : ex.getBindingResult().getGlobalErrors()) {
            errors.add(error.getObjectName() + ": " + error.getDefaultMessage());
        }

        String message = errors.isEmpty() ? ex.getLocalizedMessage() : ex.getBindingResult().getFieldErrors().getFirst().getDefaultMessage();

        ApiError apiError =
                new ApiError(AppStatus.BAD_REQUEST, message, errors);
        return new ResponseEntity<>(apiError, headers, apiError.getStatus().value());
    }

    @ExceptionHandler(ResponseAppStatusException.class)
    public ResponseEntity<Object> handleResponseAppStatusException(ResponseAppStatusException exception,
                                                                   ServletWebRequest webRequest) {
        ApiError apiError = new ApiError(exception.getStatus(), exception.getReason(), exception.getReason());
        return new ResponseEntity<>(apiError, new HttpHeaders(), apiError.getStatus().value());
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(
            MissingServletRequestParameterException ex, HttpHeaders headers,
            HttpStatusCode status, WebRequest request) {
        String error = ex.getParameterName() + " parameter is missing";

        ApiError apiError =
                new ApiError(AppStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<Object>(
                apiError, new HttpHeaders(), apiError.getStatus().value());
    }

    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    public ResponseEntity<Object> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException ex, WebRequest request) {
        String error =
                ex.getName() + " should be of type " + Objects.requireNonNull(ex.getRequiredType()).getName();

        ApiError apiError =
                new ApiError(AppStatus.BAD_REQUEST, ex.getLocalizedMessage(), error);
        return new ResponseEntity<Object>(
                apiError, new HttpHeaders(), apiError.getStatus().value());
    }


    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode status,
                                                                  WebRequest request) {
        if (ex.getCause() instanceof InvalidFormatException) {
            ApiError apiError = getApiError(ex);
            return new ResponseEntity<Object>(
                    apiError, new HttpHeaders(), apiError.getStatus().value());
        }
        ApiError apiError =
                new ApiError(AppStatus.INTERNAL_SERVER_ERROR, ex.getLocalizedMessage(), "");
        return new ResponseEntity<Object>(
                apiError, new HttpHeaders(), apiError.getStatus().value());
    }

    private static ApiError getApiError(HttpMessageNotReadableException ex) {
        List<String> errors = new ArrayList<>();
        InvalidFormatException cause = (InvalidFormatException) ex.getCause();
        List<JsonMappingException.Reference> references = cause.getPath();
        for (JsonMappingException.Reference reference : references) {
            String message = reference.getFieldName() + " must be any of: " + cause.getTargetType().getEnumConstants();
            errors.add(message);
        }
        ApiError apiError =
                new ApiError(AppStatus.BAD_REQUEST, ex.getLocalizedMessage(), errors);
        return apiError;
    }

    @ExceptionHandler(ApiException.class)
    public ResponseEntity<Object> handleApiException(ApiException exception,
                                                     ServletWebRequest webRequest) {
        ApiResponseDto<Object> apiResponse = ApiResponseDto.builder()
                .errors(exception.getErrors())
                .build();
        return new ResponseEntity<>(apiResponse, exception.getHttpStatus());
    }

}
