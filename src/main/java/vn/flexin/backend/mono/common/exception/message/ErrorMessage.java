package vn.flexin.backend.mono.common.exception.message;

public class ErrorMessage {
    public static final String CAN_NOT_GET_KEYCLOAK_TOKEN = "Can not get token from Key<PERSON>loak";
    public static final String USER_NOT_AUTHENTICATE = "User is not authenticated";
    public static final String CAN_NOT_UPDATE_PASSWORD = "Error updating password";
    public static final String CAN_NOT_LOG_OUT = "Can not logout";
    public static final String CAN_NOT_CREATE_USER = "Can not create new user";
    public static final String USER_NOT_FOUND = "User not found";
    public static final String CAN_NOT_GENERATE_TEMP_TOKEN = "Error generating temporary token";
    public static final String INVALID_PHONE_NUMBER_PASSWORD = "Invalid phone number or password";
    public static final String PHONE_NUMBER_TAKEN = "Phone number is already taken";
    public static final String TOO_MANY_REQUESTS = "Too many request. Please try again later";
    public static final String INVALID_TOKEN = "Invalid token.";
    public static final String PHONE_IS_NOT_VERIFIED = "Phone number is not verified";
    public static final String USER_REGISTERED = "User is already register successfully";
    public static final String ROLE_INVALID = "Role is invalid";
    public static final String ROLE_NOT_FOUND = "Role not found";
    public static final String ROLE_IN_USED = "Role is currently in used";
    public static final String USER_NOT_ASSIGNED_ROLE = "User is not assigned role";
    public static final String PERMISSION_ALREADY_IN_USED = "Permission is already in used";
    public static final String CAN_NOT_CREATE_ROLE = "Can not create new role";
    public static final String CAN_NOT_GET_KEYCLOAK_ROLE = "Can not get keycloak role";
    public static final String CAN_NOT_UPDATE_ROLE = "Can not update role";
    public static final String CAN_NOT_DELETE_ROLE = "Can not delete role";
    public static final String ROLE_ALREADY_EXISTED = "Role already existed";
    public static final String PERMISSION_ALREADY_EXISTED = "Permission already existed";
    public static final String PERMISSION_NOT_FOUND = "Permission not found";
    public static final String CAN_NOT_CREATE_PERMISSION = "Can not create permission";
    public static final String CAN_NOT_DELETE_PERMISSION = "Can not delete permission";
    public static final String CAN_NOT_ASSIGNED_ROLE_TO_USER = "Can not assigned role to user";
    public static final String CAN_NOT_REMOVE_ROLE_OF_USER = "Can not remove role of user";
    public static final String INTERVIEW_NOT_FOUND = "Interview not found";
    public static final String STAFF_NOT_FOUND = "Staff not found";
    public static final String COMPANY_NOT_FOUND = "Company not found";
    public static final String BRANCH_NOT_FOUND = "Brand not found";
    public static final String CAN_NOT_REGISTER = "Can not register now, please try again later!";
    public static final String LOOKUP_NOT_FOUND = "Lookup not found";
    public static final String PERMISSION_DENIED = "Permission denied";

    private ErrorMessage(){}
}
