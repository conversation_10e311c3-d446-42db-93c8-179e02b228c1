package vn.flexin.backend.mono.common.job;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vn.flexin.backend.mono.common.service.ApiAuthKeyParamService;

@Slf4j
@Component
@AllArgsConstructor
public class GetApiAuthKeyParamJob {
    private final ApiAuthKeyParamService apiAuthKeyParamService;

    public void createCache() {
        try{
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
        apiAuthKeyParamService.createCache();
    }
}
