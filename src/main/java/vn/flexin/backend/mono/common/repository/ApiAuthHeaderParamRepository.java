package vn.flexin.backend.mono.common.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import vn.flexin.backend.mono.common.entity.ApiAuthHeaderParam;
import vn.flexin.backend.mono.common.enums.ClientSystem;

import java.util.Optional;

public interface ApiAuthHeaderParamRepository extends JpaRepository<ApiAuthHeaderParam, Long> {
    Optional<ApiAuthHeaderParam> findBySystemAndActive(ClientSystem system, boolean active);
}
