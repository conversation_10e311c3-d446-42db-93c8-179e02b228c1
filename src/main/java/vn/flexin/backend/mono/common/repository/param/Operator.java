package vn.flexin.backend.mono.common.repository.param;

import lombok.Getter;

@Getter
public enum Operator {
    EQUAL("="),
    NOT_EQUAL("!="),
    IN("IN"),
    NOT_IN("NOT IN"),
    GREATER_THAN_OR_EQUAL(">="),
    LESS_THAN_OR_EQUAL("<="),
    GREATER_THAN(">"),
    LESS_THAN("<"),
    <PERSON>I<PERSON>("%"),
    LIKE_IGNORE_CASE("LIKE_IGNORE_CASE"),
    IS_TRUE("IS TRUE"),
    BETWEEN("BETWEEN"),
    ;
    private final String value;

    private Operator(String value) {
        this.value = value;
    }
}
