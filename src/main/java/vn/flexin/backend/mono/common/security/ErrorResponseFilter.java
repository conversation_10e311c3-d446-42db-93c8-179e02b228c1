package vn.flexin.backend.mono.common.security;

import jakarta.servlet.http.HttpServletResponse;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.util.CommonUtil;

import java.io.IOException;
import java.io.PrintWriter;

public interface ErrorResponseFilter {
    default void setErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        ApiResponseDto<Boolean> error = ApiResponseDto.error(message, HttpServletResponse.SC_UNAUTHORIZED);
        PrintWriter writer = response.getWriter();
        writer.write(CommonUtil.toJsonString(error));
        writer.flush();
    }

    default boolean isErrorResponse(HttpServletResponse response) {
        return response.getStatus() == HttpServletResponse.SC_UNAUTHORIZED
            || response.getStatus() == HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
    }
}
