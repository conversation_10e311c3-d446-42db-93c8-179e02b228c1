package vn.flexin.backend.mono.common.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import vn.flexin.backend.mono.common.config.KeycloakConfig;
import vn.flexin.backend.mono.common.util.JwtUtils;
import vn.flexin.backend.mono.role.service.RoleService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter implements ErrorResponseFilter{

    private final UserService userService;
    private final RoleService roleService;

    private final KeycloakConfig keycloakConfig;
    private static final String BEARER = "Bearer ";
    private static final String BASIC = "Basic ";
    private static final String AUTHORIZATION = "Authorization";

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain
    ) throws ServletException, IOException {
        if (isErrorResponse(response)) {
            return;
        }
        String accessToken = getAccessToken(request);
        if (StringUtils.isEmpty(accessToken)) {
            filterChain.doFilter(request, response);
            return;
        }
        User user = getUser(accessToken);

        Collection<SimpleGrantedAuthority> permissions = getPermissions(user);
        Authentication authentication =
                new UsernamePasswordAuthenticationToken(user, "", permissions);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        filterChain.doFilter(request, response);
    }

    private String getAccessToken(HttpServletRequest httpServletRequest) {
        String authorization = httpServletRequest.getHeader(AUTHORIZATION);
        if (StringUtils.isEmpty(authorization)) {
            return authorization;
        }
        if (authorization.startsWith(BASIC)) {
            return null;
        }
        return authorization.replaceFirst(BEARER, "").trim();
    }

    private User getUser(String accessToken) {
        User user;
        try {
            String keycloakId = JwtUtils.getSubjectFromToken(accessToken, keycloakConfig.getPublicKeyUrl());
            user = userService.getByKeycloakId(keycloakId);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
        return user;
    }

    private Collection<SimpleGrantedAuthority> getPermissions(User user) {
        Set<String> permissions = new HashSet<>();
        if (user != null && !CollectionUtils.isEmpty(user.getRoles())) {
            user.getRoles().forEach(role -> {
                List<String> rolePermissions = roleService.getPermissionByRoleId(role.getId());
                if (!CollectionUtils.isEmpty(rolePermissions)) {
                    permissions.addAll(rolePermissions);
                }
            });
        }

        return permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toSet());
    }
}
