package vn.flexin.backend.mono.common.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.entity.ApiAuthHeaderParam;
import vn.flexin.backend.mono.common.enums.ClientSystem;
import vn.flexin.backend.mono.common.service.ApiAuthKeyParamService;
import vn.flexin.backend.mono.common.util.CommonUtil;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Objects;

@Slf4j
@Component
@AllArgsConstructor
public class PermitAllApiHeaderFilter extends OncePerRequestFilter implements ErrorResponseFilter{

    private final ApiAuthKeyParamService apiAuthKeyParamService;

    private static final String API_KEY_HEADER = "flexin-auth-token";
    private static final String AUTHORIZATION = "Authorization";

    private static final String SMS_WEBHOOK_URL = "/v1/sms/webhook";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (isErrorResponse(response)) {
            return;
        }
//        if (isRequiredApiKeyHeaderApi(request)) {
//            String flexinAuthToken = request.getHeader(API_KEY_HEADER);
//            if (flexinAuthToken == null || flexinAuthToken.isEmpty()) {
//                setErrorResponse(response, "Header Authentication Token parameter is required!");
//                return;
//            }
//
//            String uri = request.getRequestURI();
//            if (!verifyAuthToken(flexinAuthToken, uri)) {
//                setErrorResponse(response, "Wrong Header Authentication parameter!");
//                return;
//            }
//        }

        filterChain.doFilter(request, response);
    }

    private boolean isRequiredApiKeyHeaderApi(HttpServletRequest request) {
        if (request.getRequestURI().equals(SMS_WEBHOOK_URL)) {
            return false;
        }

        String authorizationToken = request.getHeader(AUTHORIZATION);
        return authorizationToken == null
                || authorizationToken.isEmpty();
    }

    private boolean verifyAuthToken(String flexinAuthToken, String uri) {
        ClientSystem clientSystem = getClientSystem(uri);
        ApiAuthHeaderParam validToken = apiAuthKeyParamService.getBySystem(clientSystem);

        return Objects.equals(validToken.getApiKey(), flexinAuthToken);
    }

    private ClientSystem getClientSystem(String uri) {
        if (uri.contains("/admin")) {
            return ClientSystem.ADMIN_PORTAL;
        }
        if (uri.contains("/mobile")) {
            return ClientSystem.MOBILE_APP;
        }
        return ClientSystem.LANDING_PAGE;
    }
}
