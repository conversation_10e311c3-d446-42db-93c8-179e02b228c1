package vn.flexin.backend.mono.common.service.impl;

import vn.flexin.backend.mono.common.entity.ApiAuthHeaderParam;
import vn.flexin.backend.mono.common.enums.ClientSystem;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ApiAuthKeyParamCache {
    private ApiAuthKeyParamCache() {}

    private static final Map<ClientSystem, ApiAuthHeaderParam> API_KEYS = new ConcurrentHashMap<>();

    public static void resetCache(Map<ClientSystem, ApiAuthHeaderParam> apiKeys) {
        for (ClientSystem clientSystem : API_KEYS.keySet()) {
            if (!apiKeys.containsKey(clientSystem)) {
                API_KEYS.remove(clientSystem);
            }
        }

        API_KEYS.putAll(apiKeys);
    }

    public static ApiAuthHeaderParam get(ClientSystem system) {
        return API_KEYS.get(system);
    }

    public static boolean isEmptyCache() {
        return API_KEYS.isEmpty();
    }
}
