package vn.flexin.backend.mono.common.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.entity.ApiAuthHeaderParam;
import vn.flexin.backend.mono.common.enums.ClientSystem;
import vn.flexin.backend.mono.common.repository.ApiAuthHeaderParamRepository;
import vn.flexin.backend.mono.common.service.ApiAuthKeyParamService;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ApiAuthKeyParamServiceImpl implements ApiAuthKeyParamService {

    private final ApiAuthHeaderParamRepository apiAuthHeaderParamRepository;

    @Override
    public void createCache() {
        List<ApiAuthHeaderParam> apiAuthHeaderParams = findAll();
        Map<ClientSystem, ApiAuthHeaderParam> apiKeyMap= apiAuthHeaderParams.stream()
                .filter(ApiAuthHeaderParam::isActive)
                .collect(Collectors.toMap(ApiAuthHeaderParam::getSystem, Function.identity()));
        ApiAuthKeyParamCache.resetCache(apiKeyMap);
    }

    @Override
    public ApiAuthHeaderParam getBySystem(ClientSystem clientSystem) {
        if (ApiAuthKeyParamCache.isEmptyCache()) {
            Optional<ApiAuthHeaderParam> validTokenOptional =
                    apiAuthHeaderParamRepository.findBySystemAndActive(clientSystem, true);
            return validTokenOptional.orElse(null);
        }
        return ApiAuthKeyParamCache.get(clientSystem);
    }

    @Override
    public List<ApiAuthHeaderParam> findAll() {
        return apiAuthHeaderParamRepository.findAll();
    }
}
