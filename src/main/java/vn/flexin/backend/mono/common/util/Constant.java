package vn.flexin.backend.mono.common.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class Constant {
    public static final DateFormat EXPORTED_VIDEO_FILTER_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    public static final String DEVICE_ID_HEADER = "DeviceId";

    public static final String FEEDBACK_PREFIX = "FEEDBACKS/";
    public static final String FEEDBACK_REPLY_PREFIX = "FEEDBACK_REPLIES/";
    public static final ZoneId UTC_ZONE = ZoneId.of("UTC");
    public static final String TRIAL_PLAN_CODE = "TRIAL";
    public static final String SUBSCRIPTION_PLAN_CODE = "SUBSCRIPTION";
    public static final DateTimeFormatter UTC_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    public static final String PLAN_STATUS_ACTIVE = "ACTIVE";
    public static final String PLAN_STATUS_EXPIRED = "EXPIRED";
    public static final String PLAN_STATUS_SCHEDULED = "SCHEDULED";
    public static final String SORT_ASC = "ASC";
    public static final String SORT_DESC = "DESC";
}
