package vn.flexin.backend.mono.common.util;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class RequestValidator {
    public void validatePlanDates(LocalDateTime from, LocalDateTime to) {
      // TODO: <PERSON> disabled this validation
//        if (from.isAfter(to)) {
//            throw new ApiException(HttpStatus.BAD_REQUEST,
//                    List.of(new ErrorDto(ErrorEnum.SYSTEM_ERROR.getCode(), "Start date must be equal or before end date")));
//        }
    }
}
