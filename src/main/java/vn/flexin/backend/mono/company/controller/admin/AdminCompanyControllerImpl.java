package vn.flexin.backend.mono.company.controller.admin;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.company.dto.branch.BranchFilter;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.StaffFilter;
import vn.flexin.backend.mono.company.service.admin.AdminCompanyService;

import java.util.List;

@RestController
@AllArgsConstructor
public class AdminCompanyControllerImpl implements AdminCompanyController {

    private final AdminCompanyService companyService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<AdminCompanyResponse>>> searchCompanyForAdmin(CompanyFilter filter) {
        var result = companyService.searchCompanyForAdmin(filter);

        return ResponseEntity.ok(PaginationApiResponseDto.success("Company retrieved successfully", result.getFirst(),
                result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminCompanyResponse>> getDetailCompany(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getDetailCompany(id),"Company fetched successfully"));

    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminCompanyResponse>> createCompany(CreateAdminCompanyRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.createAdminCompany(request),"Company created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminCompanyResponse>> updateCompany(Long id, UpdateAdminCompanyRequest request) {
        request.setId(id);
        return ResponseEntity.ok(ApiResponseDto.success(companyService.updateAdminCompany(request),"Company updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteCompany(Long id) {
        companyService.deleteCompany(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Company deleted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminCompanyResponse>> verifiesCompany(Long companyId) {
            return ResponseEntity.ok(ApiResponseDto.success(companyService.verifiesCompany(companyId),"Company verified successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchAdminBranchResponse>>> searchBranchesForAdmin(Long companyId, BranchFilter request) {
        request.setCompanyId(companyId);
        var result = companyService.searchBranchesForAdmin(request);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Branch retrieved successfully", result.getFirst(),
                result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> getDetailBranch(Long companyId, Long branchId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getDetailBranch(companyId, branchId),"Branch " +
                "fetched successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> createBranch(Long companyId, CreateAdminBranchRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.createAdminBranch(companyId, request),"Branch created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> updateBranch(Long companyId, Long branchId, UpdateAdminBranchRequest request) {
        request.setCompanyId(companyId);
        request.setId(branchId);
        return ResponseEntity.ok(ApiResponseDto.success(companyService.updateAdminBranch(request),"Branch " +
                "updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteBranch(Long companyId, Long branchId) {
        companyService.deleteBranch(companyId, branchId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Branch deleted successfully"));
    }

    @Override
        public ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> setDefaultBranch(Long companyId, Long branchId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.setDefaultBranch(companyId, branchId),"Branch " +
                "set default successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchAdminStaffResponse>>> searchStaffsForAdmin(Long companyId, StaffFilter filters) {

        var result = companyService.searchStaffsForAdmin(companyId, filters);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Team member retrieved successfully", result.getFirst(),
                result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<SearchAdminStaffResponse>> getDetailStaff(Long companyId, Long memberId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getDetailStaff(companyId, memberId),"Branch " +
                "fetched successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createStaff(Long companyId, CreateAdminStaffRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.createStaff(companyId, request),"Team " +
                "member created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateStaff(Long companyId, Long memberId, UpdateAdminStaffRequest request) {
        companyService.updateStaff(companyId, memberId, request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Team member updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteTeamMember(Long companyId, Long memberId) {
        companyService.deleteStaff(companyId, memberId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Team member deleted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateTeamMemberManagerStatus(Long companyId, Long memberId, UpdateManagerStatusRequest request) {
        companyService.updateStaffManagerStatus(companyId, memberId, request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Manager status updated successfully"));
    }

}
