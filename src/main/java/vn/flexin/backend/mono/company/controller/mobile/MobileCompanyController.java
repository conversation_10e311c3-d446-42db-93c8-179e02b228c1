package vn.flexin.backend.mono.company.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;

import java.util.List;
@RequestMapping("/v1/mobile/companies")
@Tag(name = "Mobile Companies APIs", description = "Mobile Company management")
public interface MobileCompanyController {

    @Operation(summary = "Search company", description = "create new company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully create company"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    ResponseEntity<PaginationApiResponseDto<List<SearchCompanyResponse>>> searchCompany(@Valid @RequestBody CompanyFilter filter);

    @Operation(summary = "Create new company", description = "create new company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully create company"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createCompany(@RequestBody CreateCompanyRequest request);

    @Operation(summary = "Get detail company", description = "get detail company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully fetch company"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{companyId}")
    ResponseEntity<ApiResponseDto<DetailCompanyResponse>> getDetailCompany(@PathVariable(name = "companyId") Long companyId);

    @Operation(summary = "Update company", description = "Update company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully update company"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> updateCompany(@Valid @RequestBody CreateCompanyRequest request,
                                                       @PathVariable("id") Long companyId);

    @Operation(summary = "Add company to list favorite", description = "Add company to list favorite")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully add company to list favorite."),
            @ApiResponse(responseCode = "400", description = "Company already added to list favorite.")
    })
    @PostMapping("/{id}/favorite")
    ResponseEntity<ApiResponseDto<Boolean>> addCompanyToListFavorite(@PathVariable("id") Long companyId);

    @Operation(summary = "Remove company from list favorite", description = "Remove company from list favorite")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully remove company from list favorite."),
            @ApiResponse(responseCode = "400", description = "Company not in list favorite.")
    })
    @DeleteMapping("/{id}/favorite")
    ResponseEntity<ApiResponseDto<Boolean>> removeCompanyFromListFavorite(@PathVariable("id") Long companyId);

    @Operation(summary = "Request verify company", description = "Request verify company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully request verify company"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "400")
    })
    @PostMapping("/{id}/verify")
    ResponseEntity<ApiResponseDto<Boolean>> requestVerifyCompany(@PathVariable("id") Long companyId,
                                                              @RequestBody List<CreateVRDocumentRequest> documents);

    @Operation(summary = "Request verify company", description = "Request verify company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully request verify company"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "400")
    })

    // delete company by companyId
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> deleteCompany(@PathVariable("id") Long companyId);

    @GetMapping("/{companyId}/analytics")
    ResponseEntity<ApiResponseDto<CompanyAnalyticsResponse>> getAnalytics(@PathVariable Long companyId,
                                                                           @RequestParam String period,
                                                                           @RequestParam(required = false) String branchFilter);

    //=================================Branches APIs===================================================

    @Operation(summary = "Get all branches by company", description = "Get a list of all branches by company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved branches"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{companyId}/branches")
    // get all branch by companyId
    ResponseEntity<ApiResponseDto<List<BranchResponse>>> getAllBranchesByCompany(@PathVariable("companyId") Long companyId);

    @Operation(summary = "Create new branch", description = "create new branch")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully create branch"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{companyId}/branches")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createBranch(@PathVariable("companyId") Long companyId,
                                                                      @Valid @RequestBody CreateBranchRequest request);

    @Operation(summary = "Update branch", description = "Update branch")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully update branch"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{companyId}/branches/{branchId}")
    ResponseEntity<ApiResponseDto<Boolean>> updateBranch(@Valid @RequestBody CreateBranchRequest request,
                                                         @PathVariable("companyId") Long companyId,
                                                         @PathVariable("branchId") Long branchId);

    @GetMapping("/{companyId}/branches/{branchId}")
    ResponseEntity<ApiResponseDto<BranchResponse>> getDetailBranch(@PathVariable("branchId") Long branchId,
                                                                   @PathVariable("companyId") Long companyId);

    @DeleteMapping("/{companyId}/branches/{branchId}")
    ResponseEntity<ApiResponseDto<Boolean>> deleteBranch(@PathVariable("branchId") Long branchId,
                                                         @PathVariable("companyId") Long companyId);

    //======================================= Staff APIs ===================================================


    @GetMapping("/{companyId}/staffs")
    ResponseEntity<ApiResponseDto<List<StaffResponse>>> getAllStaffsByCompany(@PathVariable("companyId") Long companyId);

    @DeleteMapping("/{companyId}/branches/{branchId}/staffs/{staffId}")
    ResponseEntity<ApiResponseDto<Boolean>> removeStaff(@PathVariable("branchId") Long branchId,
                                                        @PathVariable("companyId") Long companyId,
                                                        @PathVariable("staffId") String staffId);

    @PutMapping("/{companyId}/branches/{branchId}/staffs/{staffId}/status")
    ResponseEntity<ApiResponseDto<Boolean>> updateStaffStatus(@PathVariable("branchId") Long branchId,
                                                              @PathVariable("companyId") Long companyId,
                                                              @PathVariable("staffId") String staffId,
                                                              @RequestBody UpdateStaffRequest request);

    @PostMapping("/{companyId}/branches/{branchId}/invite")
    ResponseEntity<ApiResponseDto<Boolean>> inviteUser(@PathVariable("branchId") Long branchId,
                                                       @PathVariable("companyId") Long companyId,
                                                       @RequestBody InviteStaffRequest request);

}
