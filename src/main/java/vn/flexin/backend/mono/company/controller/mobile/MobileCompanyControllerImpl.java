package vn.flexin.backend.mono.company.controller.mobile;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;
import vn.flexin.backend.mono.company.service.mobile.CompanyService;

import java.util.List;

@RestController
@AllArgsConstructor
@Slf4j
public class MobileCompanyControllerImpl implements MobileCompanyController {

    private final CompanyService companyService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchCompanyResponse>>> searchCompany(CompanyFilter filter) {
        Pair<List<SearchCompanyResponse>, PaginationResponse> result = companyService.searchCompany(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Companies fetched successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createCompany(CreateCompanyRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.createCompany(request),"Companies created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<DetailCompanyResponse>> getDetailCompany(Long companyId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getDetailCompany(companyId), "Successfully fetch company"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateCompany(CreateCompanyRequest request, Long companyId) {
        request.setId(companyId);
        companyService.updateCompany(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Companies updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> addCompanyToListFavorite(Long companyId) {
        companyService.addCompanyToListFavorite(companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Favorite status toggled successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> removeCompanyFromListFavorite(Long companyId) {
        companyService.removeCompanyFromListFavorite(companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Favorite status toggled successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> requestVerifyCompany(Long companyId, List<CreateVRDocumentRequest> documents) {
        companyService.requestVerifyCompany(companyId, documents);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Request verify successfully"));
    }

    // delete company
    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteCompany(Long companyId) {
        companyService.deleteCompany(companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Verification request submitted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CompanyAnalyticsResponse>> getAnalytics(Long companyId, String period, String branchFilter) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getCompanyAnalytics(companyId, period, branchFilter)));
    }

    // get all branch by company id
    @Override
    public ResponseEntity<ApiResponseDto<List<BranchResponse>>> getAllBranchesByCompany(Long companyId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getAllBranchesByCompany(companyId)));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createBranch(Long companyId, CreateBranchRequest request) {
        request.setCompanyId(companyId);
        return ResponseEntity.ok(ApiResponseDto.success(companyService.createBranch(request)));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateBranch(CreateBranchRequest request, Long companyId, Long branchId) {
        request.setCompanyId(companyId);
        request.setId(branchId);
        companyService.updateBranch(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Branch updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<BranchResponse>> getDetailBranch(Long branchId, Long companyId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getDetailBranch(companyId, branchId)));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteBranch(Long branchId, Long companyId) {
        companyService.deleteBranch(branchId, companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Branch deleted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> removeStaff(Long branchId, Long companyId, String staffId) {
        companyService.removeStaff( branchId, staffId, companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Staff removed successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateStaffStatus(Long branchId, Long companyId, String staffId, UpdateStaffRequest request) {
        companyService.updateStaffStatus(request, branchId, staffId, companyId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Staff status updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> inviteUser(Long branchId, Long companyId, InviteStaffRequest request) {
        companyService.inviteUser(branchId, companyId, request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"User is invited successfully"));
    }

    // get  all staff by company
    @Override
    public ResponseEntity<ApiResponseDto<List<StaffResponse>>> getAllStaffsByCompany(Long companyId) {
        return ResponseEntity.ok(ApiResponseDto.success(companyService.getAllStaffsByCompany(companyId)));
    }


}
