package vn.flexin.backend.mono.company.dto.branch;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.file.dto.FileDto;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BranchResponse {
    private Long id;
    private String name;
    private AddressResponse address;
    private int staffCount;
    @JsonProperty("isDefault")
    private boolean isDefault;
    private BasicUserInfoResponse manager;
    private String phoneNumber;
    private List<FileDto> galleryImages;
    private Map<String, Object> additionalData;

    public BranchResponse(Branch branch) {
        this.id = branch.getId();
        this.name = branch.getName();
        this.address = new AddressResponse(branch.getAddress());
        this.staffCount = branch.getStaffCount();
        this.isDefault = branch.isDefault();
        if (branch.getManager() != null) {
            this.manager = new BasicUserInfoResponse(branch.getManager());
        }
        this.phoneNumber = branch.getPhoneNumber();
        if (!CollectionUtils.isEmpty(branch.getGalleryImages())) {
            this.galleryImages = branch.getGalleryImages().stream().map(FileDto::new).toList();
        }
    }
}
