package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class AdminCompanyRequest {

    private String name;

    private String description;

    private Long id;

    private String industry;

    private AddressRequest address;

    private String websiteUrl;

    private String phoneNumber;

    private String foundedYear;

    private String email;

    @JsonProperty("isIndividual")
    private boolean isIndividual;

    private FileDto logo;
    private FileDto coverImage;
    private List<FileDto> galleryImages;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> additionalData;

}
