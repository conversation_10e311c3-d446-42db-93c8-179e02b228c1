package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)

public class AdminCompanyResponse {
    private Long id;

    private String name;

    private String industry;

    private String description;

    private AddressResponse address;

    private String websiteUrl;

    private String email;

    private String phoneNumber;

    private String foundedYear;

    @JsonProperty("isVerified")
    private boolean isVerified;

    @JsonProperty("isIndividual")
    private boolean isIndividual;

    @JsonProperty("isFavorite")
    private boolean isFavorite;

    private Map<String, Object> additionalData;



}
