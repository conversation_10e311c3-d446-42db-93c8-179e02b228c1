package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class AdminStaffRequest {
    @NotNull
    private Long companyId;

    @NotBlank
    private String position;

    @NotNull
    private Long userId;

    @NotNull
    private Long branchId;

    @JsonProperty("isManager")
    private Boolean isManager;

}
