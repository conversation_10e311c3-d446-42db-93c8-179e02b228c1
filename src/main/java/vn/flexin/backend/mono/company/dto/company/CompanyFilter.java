package vn.flexin.backend.mono.company.dto.company;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.Constant;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CompanyFilter extends BaseFilter<Company> {
    private Long userId;

    private String industry;

    private Boolean isIndividual;

    private Boolean isVerified;

    private String keyword;

    @Override
    public String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? Company.Fields.id : sortBy;
    }

    @Override
    public String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_ASC : sortOrder;
    }

    @Override
    public Specification<Company> toSpecification() {
        var condition = new Condition();

        if (industry != null) {
            condition.append(new Where(Company.Fields.industry, Operator.LIKE_IGNORE_CASE, industry));
        }

        if (isIndividual != null) {
            condition.append(new Where(Company.Fields.isIndividual, isIndividual));
        }

        if (isVerified != null) {
            condition.append(new Where(Company.Fields.isVerified, isVerified));
        }

        if (userId != null) {
                condition.append(new Join(Company.Fields.user, List.of(new Where(User.Fields.id, userId))));
        }

        if (StringUtils.isNotBlank(keyword)) {
            var subDescriptionCondition = new Condition()
                    .append(new Where(Company.Fields.description, Operator.LIKE_IGNORE_CASE, keyword));
            var subNameCondition = new Condition()
                    .append(new Where(Company.Fields.name, Operator.LIKE_IGNORE_CASE, keyword));
            condition.appendComplex(new Where(Complex.OR, List.of(subDescriptionCondition, subNameCondition)));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }

}
