package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Setter
@Getter
public class SearchAdminStaffResponse {
    private Long id;
    private Long companyId;
    private String name;
    private String photoUrl;
    private String position;
    private BasicUserInfoResponse user;
    private BranchResponse branch;

    @JsonProperty("isManager")
    private boolean isManager;

    @JsonProperty("isActive")
    private boolean isActive;

    @JsonProperty("isPending")
    private boolean isPending;
    private LocalDateTime joinDate;

}
