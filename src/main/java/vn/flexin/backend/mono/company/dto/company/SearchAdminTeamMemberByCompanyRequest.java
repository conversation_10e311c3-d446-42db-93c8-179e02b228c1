package vn.flexin.backend.mono.company.dto.company;

import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Getter
@Setter
public class SearchAdminTeamMemberByCompanyRequest {
    private String search;
    private Long branchId;
    private Boolean isManager;
    private Boolean isPending;
    private String sortBy;
    private String sortOrder;
    private Integer page = 1;
    private Integer pageSize = 10;
}
