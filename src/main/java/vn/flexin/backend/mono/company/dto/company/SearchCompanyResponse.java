package vn.flexin.backend.mono.company.dto.company;

import lombok.Data;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
public class SearchCompanyResponse {
    private Long id;
    private int userId;
    private String name;
    private String industry;
    private String description;
    private AddressResponse address;
    private FileDto logo;
    private FileDto coverImage;
    private List<FileDto> galleryImages;
    private String websiteUrl;
    private String email;
    private String phoneNumber;
    private String foundedYear;
    private boolean isVerified;
    private boolean isIndividual;
    private boolean isFavorite;
    private int activeJobPostings;
    private int totalEmployees;
    private List<BranchResponse> branches;
    private Map<String, Object> additionalData;
}
