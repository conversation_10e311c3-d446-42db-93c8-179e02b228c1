package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class UpdateAdminBranchRequest {

    private Long id;
    private Long companyId;
    private String name;
    private AddressRequest addressRequest;
    private List<FileDto> galleryImages;
    @JsonProperty("isActive")
    private boolean isActive;
    @JsonProperty("isDefault")
    private boolean isDefault;
    private Map<String, Object> additionalData;
    private String phoneNumber;
}
