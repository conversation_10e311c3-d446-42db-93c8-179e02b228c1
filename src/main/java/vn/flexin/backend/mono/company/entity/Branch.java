package vn.flexin.backend.mono.company.entity;


import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
@Table(name = "t_branches")
public class Branch extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    private String name;

    @OneToOne
    @JoinColumn(name = "address_id")
    private Address address;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "t_branch_gallery_images",
            joinColumns = @JoinColumn(name = "branch_id"),
            inverseJoinColumns = @JoinColumn(name = "file_id")
    )
    @ToString.Exclude
    private List<File> galleryImages;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "branch", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<Staff> staffs;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    private boolean isActive;

    private boolean isDefault;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> additionalData;

    @ManyToOne
    @JoinColumn(name = "manager_id")
    private User manager;

    private String phoneNumber;

    @Column(name = "staff_count")
    private Integer staffCount = 0;

    @Column(name = "active_job_postings")
    private Integer activeJobPostings = 0;

    @Column(name = "operating_hours")
    private String operatingHours;

}
