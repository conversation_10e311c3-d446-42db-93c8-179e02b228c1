package vn.flexin.backend.mono.company.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;
import vn.flexin.backend.mono.user.enums.UserStatus;

import java.util.Arrays;

public enum CompanyVerifyStatus {
    REQUESTED("requested"),
    ACCEPTED("accepted"),
    REJECTED("rejected");

    private final String value;

    CompanyVerifyStatus(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static CompanyVerifyStatus fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Verify status must be any of [" + getValues() + "]");
        }
    }
}
