package vn.flexin.backend.mono.company.service.admin;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.company.dto.branch.BranchFilter;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.StaffFilter;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;

import java.util.List;

public interface AdminCompanyService {
    Pair<List<AdminCompanyResponse>, PaginationResponse> searchCompanyForAdmin(CompanyFilter request);

    Company getById(Long id);

    Branch getBranchById(Long id);

    AdminCompanyResponse getDetailCompany(Long id);

    AdminCompanyResponse createAdminCompany(CreateAdminCompanyRequest request);

    Company save(Company company);

    AdminCompanyResponse updateAdminCompany(UpdateAdminCompanyRequest request);

    void deleteCompany(Long id);

    AdminCompanyResponse verifiesCompany(Long id);

    Pair<List<SearchAdminBranchResponse>, PaginationResponse> searchBranchesForAdmin(BranchFilter request);
    SearchAdminBranchResponse getDetailBranch(Long companyId, Long branchId);

    SearchAdminBranchResponse createAdminBranch(Long companyId,CreateAdminBranchRequest request);

    SearchAdminBranchResponse updateAdminBranch(UpdateAdminBranchRequest request);

    void deleteBranch(Long companyId, Long branchId);

    Branch saveBranch(Branch branch);

    SearchAdminBranchResponse setDefaultBranch(Long companyId, Long branchId);

    Pair<List<SearchAdminStaffResponse>, PaginationResponse> searchStaffsForAdmin(Long companyId, StaffFilter request);
    SearchAdminStaffResponse getDetailStaff(Long companyId, Long memberId);

    CreateObjectResponse createStaff(Long companyId, CreateAdminStaffRequest request);

    void updateStaff(Long companyId, Long memberId, UpdateAdminStaffRequest request);

    Staff getStaffById(Long id);

    Staff saveStaff(Staff staff);
    void deleteStaff(Long companyId, Long memberId);

    void updateStaffManagerStatus(Long companyId, Long memberId,
                                  UpdateManagerStatusRequest request);
}
