package vn.flexin.backend.mono.company.service.admin.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.dto.branch.BranchFilter;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.StaffFilter;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;
import vn.flexin.backend.mono.company.repository.BranchRepository;
import vn.flexin.backend.mono.company.repository.CompanyRepository;
import vn.flexin.backend.mono.company.repository.StaffRepository;
import vn.flexin.backend.mono.company.service.admin.AdminCompanyService;
import vn.flexin.backend.mono.file.dto.FileDto;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.service.FileService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;

import java.beans.PropertyDescriptor;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@AllArgsConstructor
@Slf4j
public class AdminCompanyServiceImpl implements AdminCompanyService {

    private final BranchRepository branchRepository;
    private final StaffRepository staffRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final FileService fileService;
    private final FileRepository fileRepository;
    private final AddressService addressService;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<AdminCompanyResponse>, PaginationResponse> searchCompanyForAdmin(CompanyFilter request) {
        Page<Company> companies = companyRepository.findAll(request);
        List<AdminCompanyResponse> adminCompanyResponse = companies.getContent().stream().map(this::toCompanyResponse).toList();
        GetListAdminCompanyResponse response = new GetListAdminCompanyResponse();
        response.setCompanies(adminCompanyResponse);
        response.setTotal(companies.getTotalElements());
        PaginationResponse paging = new PaginationResponse(request.getLimit(), request.getPage(), (int) response.getTotal());
        return Pair.of(adminCompanyResponse, paging);
    }

    private AdminCompanyResponse toCompanyResponse(Company company) {
        var response = ModelMapperUtils.toObject(company, AdminCompanyResponse.class);
        response.setAddress(addressService.toAddressResponse(company.getAddress()));
        return response;
    }

    @Override
    public Company getById(Long id) {
        return companyRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.COMPANY_NOT_FOUND));
    }

    @Override
    public Branch getBranchById(Long branchId) {
        return branchRepository.findById(branchId).orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.BRANCH_NOT_FOUND));
    }
    @Override
    @Transactional(readOnly = true)
    public AdminCompanyResponse getDetailCompany(Long id) {
        Company company = getById(id);
        return toCompanyResponse(company);
    }

    @Override
    @Transactional
    public AdminCompanyResponse createAdminCompany(CreateAdminCompanyRequest request) {

        Address address = addressService.createAddress(request.getAddress());
        Company company = ModelMapperUtils.toObject(request, Company.class);
        setCompanyFiles(company, request);
        company.setAddress(address);

        save(company);
        return toCompanyResponse(company);
    }

    @Override
    public Company save(Company company) {
        return companyRepository.save(company);
    }

    @Override
    @Transactional
    public AdminCompanyResponse updateAdminCompany(UpdateAdminCompanyRequest request) {
        Company company = getById(request.getId());

        setCompanyFields(company, request);
        setCompanyFiles(company, request);
        save(company);

        return toCompanyResponse(company);
    }

    private void setCompanyFields(Company company, UpdateAdminCompanyRequest request) {
        company.setName(request.getName());
        company.setIndustry(request.getIndustry());
        company.setDescription(request.getDescription());
        company.setWebsiteUrl(request.getWebsiteUrl());
        company.setEmail(request.getEmail());
        company.setFoundedYear(request.getFoundedYear());
        company.setPhoneNumber(request.getPhoneNumber());
        company.setIndividual(request.isIndividual());
        company.setAdditionalData(request.getAdditionalData());

        AddressRequest addressRequest = request.getAddress();
        addressRequest.setId(company.getAddress().getId());
        company.setAddress(addressService.updateAddress(addressRequest));
    }

    private void setCompanyFiles(Company company, AdminCompanyRequest request) {
        if (request.getLogo() != null) {
            File logo = fileService.getById(request.getLogo().getId(), fileRepository);
            company.setLogo(logo);
        }
        if (request.getCoverImage() != null) {
            File coverImage = fileService.getById(request.getCoverImage().getId(), fileRepository);
            company.setCoverImage(coverImage);
        }
        if (!CollectionUtils.isEmpty(request.getGalleryImages())) {
            List<Long> imageIds = request.getGalleryImages().stream().map(FileDto::getId).toList();
            List<File> galleryImages = fileService.getByIds(imageIds, fileRepository);
            company.setGalleryImages(galleryImages);
        }
    }

    private String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for(PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }

        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    @Override
    @Transactional
    public void deleteCompany(Long id) {
        Company company = getById(id);
        if(company != null) {
            branchRepository.deleteByCompanyId(id);
            companyRepository.delete(company);
        }

    }

    @Override
    @Transactional
    public AdminCompanyResponse verifiesCompany(Long id) {
        Company company = getById(id);
        company.setVerified(true);
        save(company);
        return toCompanyResponse(company);
    }

    @Override
    @Transactional(readOnly = true)
    public Pair<List<SearchAdminBranchResponse>, PaginationResponse> searchBranchesForAdmin(BranchFilter branchFilter) {
        Page<Branch> branches = branchRepository.findAll(branchFilter);
        List<SearchAdminBranchResponse> branchResponse = branches.getContent().stream().map(this::toSearchBranchResponse).toList();
        PaginationResponse paging = new PaginationResponse(branchFilter.getLimit(), branchFilter.getPage(), (int) branches.getTotalElements());

        return Pair.of(branchResponse, paging);
    }

    @Override
    @Transactional(readOnly = true)
    public SearchAdminBranchResponse getDetailBranch(Long companyId, Long branchId) {
        Company company = getById(companyId);

        Branch branch = new Branch();
        if(company != null){
            branch = getBranchById(branchId);
        }

        return toDetailBranchResponse(branch);
    }

    private SearchAdminBranchResponse toDetailBranchResponse (Branch branch) {
        SearchAdminBranchResponse response = ModelMapperUtils.toObject(branch, SearchAdminBranchResponse.class);

        AddressResponse addressResponse = addressService.toAddressResponse(branch.getAddress());
        response.setAddress(addressResponse);

        if (!CollectionUtils.isEmpty(branch.getStaffs())) {
            User manager = branch.getStaffs().stream()
                    .filter(Staff::isManager).findFirst()
                    .map(Staff::getUser).orElse(null);
            response.setManager(manager == null ? null : new BasicUserInfoResponse(manager));
        }

        return response;
    }

    @Override
    @Transactional
    public SearchAdminBranchResponse createAdminBranch(Long companyId, CreateAdminBranchRequest request) {
        Company company = getById(companyId);
        Address address = addressService.createAddress(request.getAddress());

        // Validate duplicate branch name OR address
        if (branchRepository.existsByName(request.getName())) {
            throw new vn.flexin.backend.mono.common.exception.BadRequestException("Branch name already exists");
        }
        if (branchRepository.existsByAddress_Id(address.getId())) {
            throw new vn.flexin.backend.mono.common.exception.BadRequestException("Branch address already exists");
        }

        Branch branch = ModelMapperUtils.toObject(request, Branch.class);
        branch.setAddress(address);
        branch.setCompany(company);

        saveBranch(branch);

        return toDetailBranchResponse(branch);
    }

    @Override
    public Branch saveBranch(Branch branch) {
        return branchRepository.save(branch);
    }

    @Override
    @Transactional
    public SearchAdminBranchResponse setDefaultBranch(Long companyId, Long branchId) {
        getById(companyId);

        Branch branch= getBranchById(branchId);
        branch.setDefault(true);
        saveBranch(branch);

        return toDetailBranchResponse(branch);
    }

    @Override
    @Transactional
    public SearchAdminBranchResponse updateAdminBranch(UpdateAdminBranchRequest request) {
        getById(request.getCompanyId());

        Branch branch= getBranchById(request.getId());
        setBranchFields(branch, request);
        saveBranch(branch);

        return toDetailBranchResponse(branch);
    }

    private void setBranchFields(Branch branch, UpdateAdminBranchRequest request) {
        branch.setName(request.getName());
        branch.setActive(request.isActive());
        branch.setDefault(request.isDefault());
        branch.setPhoneNumber(request.getPhoneNumber());

        if (!CollectionUtils.isEmpty(request.getGalleryImages())) {
            List<Long> imageIds = request.getGalleryImages().stream().map(FileDto::getId).toList();
            List<File> galleryImages = fileService.getByIds(imageIds, fileRepository);
            branch.setGalleryImages(galleryImages);
        } else {
            branch.setGalleryImages(Collections.emptyList());
        }
    }

    @Override
    @Transactional
    public void deleteBranch(Long companyId, Long branchId) {
        Company company = getById(companyId);

        if(company != null)
        {
            Branch branch = getBranchById(branchId);
            if(branch != null){
                branchRepository.delete(branch);
            }
        }

    }

    private SearchAdminBranchResponse toSearchBranchResponse(Branch branch) {
        SearchAdminBranchResponse response = new SearchAdminBranchResponse();
        response.setId(branch.getId());
        response.setName(branch.getName());
        response.setAddress(addressService.toAddressResponse(branch.getAddress()));
        response.setPhoneNumber(branch.getPhoneNumber());
        response.setStaffCount(branch.getStaffCount());
        response.setActiveJobPostings(branch.getActiveJobPostings());
        response.setDefault(branch.isDefault());
        response.setAdditionalData(branch.getAdditionalData());
        if(!CollectionUtils.isEmpty(branch.getGalleryImages())) {
            List<FileDto> images = branch.getGalleryImages().stream().map(FileDto::new).toList();
            response.setGalleryImages(images);
        }


        if (CollectionUtils.isEmpty(branch.getStaffs())) {
            response.setManager(null);
        }

        User manager = branch.getStaffs().stream()
                .filter(Staff::isManager).findFirst()
                .map(Staff::getUser).orElse(null);
        response.setManager(manager == null ? null : new BasicUserInfoResponse(manager));
        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public Pair<List<SearchAdminStaffResponse>, PaginationResponse> searchStaffsForAdmin(Long companyId, StaffFilter request) {
        List<Staff> staffs = staffRepository.findAllStaffByCompanyId(companyId);
        
        int total = staffs.size();
        PaginationResponse paging = new PaginationResponse(request.getLimit(), request.getPage(), total);

        List<SearchAdminStaffResponse> staffResponse = staffs.stream()
                .map(staff -> toDetailStaffResponse(companyId, staff))
                .toList();

        return Pair.of(staffResponse, paging);
    }

    @Override
    @Transactional(readOnly = true)
    public SearchAdminStaffResponse getDetailStaff(Long companyId, Long staffId) {
        Company company = getById(companyId);

        Staff staff = new Staff();
        if(company != null){
            staff = getStaffById(staffId);
        }

        return toDetailStaffResponse(companyId, staff);
    }

    @Override
    public Staff getStaffById(Long staffId) {
        return staffRepository.findById(staffId).orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.STAFF_NOT_FOUND));
    }

    private SearchAdminStaffResponse toDetailStaffResponse (Long companyId, Staff staff) {
        SearchAdminStaffResponse adminResponse = new SearchAdminStaffResponse();
        User user = staff.getUser();
        Branch branch = staff.getBranch();
        BasicUserInfoResponse userInfoResponse = new BasicUserInfoResponse(user);
        BranchResponse branchResponse = new BranchResponse(branch);
        adminResponse.setCompanyId(companyId);
        adminResponse.setBranch(branchResponse);
        adminResponse.setUser(userInfoResponse);
        adminResponse.setId(staff.getId());
        adminResponse.setActive(staff.isActive());
        adminResponse.setPending(staff.isPending());
        adminResponse.setPosition(staff.getPosition());
        adminResponse.setPhotoUrl(staff.getPhotoUrl());
        adminResponse.setJoinDate(staff.getJoinDate());
        adminResponse.setName(staff.getName());
        adminResponse.setPhotoUrl(staff.getPhotoUrl());
        adminResponse.setManager(staff.isManager());
        return adminResponse;
    }

    @Override
    @Transactional
    public CreateObjectResponse createStaff(Long companyId, CreateAdminStaffRequest request) {
        getById(companyId);

        Branch branch = getBranchById(request.getBranchId());

        User user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND));

        Staff staff = ModelMapperUtils.toObject(request, Staff.class);

        staff.setPending(false);
        staff.setActive(true);
        staff.setJoinDate(CommonUtil.getCurrentUTCTime());
        staff.setBranch(branch);
        staff.setUser(user);
        staff.setName(user.getName());
        staff.setPhotoUrl(user.getProfilePicture());
        if (request.getIsManager()) {
            staff.setManager(request.getIsManager());
            branch.setManager(user);
            saveBranch(branch);
        }

        saveStaff(staff);
        return new CreateObjectResponse(staff.getId());
    }

    @Override
    public Staff saveStaff(Staff staff) {
        return staffRepository.save(staff);
    }

    @Override
    @Transactional
    public void deleteStaff(Long companyId, Long memberId) {
        Company company = getById(companyId);

        if(company != null)
        {
            Staff staff = getStaffById(memberId);
            if(staff != null){
                staffRepository.delete(staff);
            }
        }
    }

    @Override
    @Transactional
    public void updateStaffManagerStatus(Long companyId, Long memberId, UpdateManagerStatusRequest request) {
        getById(companyId);
        Staff staff = getStaffById(memberId);
        Branch branch = staff.getBranch();

        staff.setManager(request.getIsManager());
        if (Boolean.TRUE.equals(request.getIsManager())) {
            branch.setManager(staff.getUser());
            saveBranch(branch);
        }

        saveStaff(staff);
    }

    @Override
    @Transactional
    public void updateStaff(Long companyId, Long memberId, UpdateAdminStaffRequest request) {
        getById(companyId);
        Branch branch = getBranchById(request.getBranchId());
        Staff staff = getStaffById(memberId);

        staff.setPosition(request.getPosition());
        staff.setBranch(branch);
        staff.setManager(request.getIsManager());

        staff.setBranch(branch);
        if (request.getIsManager()) {
            branch.setManager(staff.getUser());
            saveBranch(branch);
        }
        saveStaff(staff);

    }
}
