package vn.flexin.backend.mono.company.service.mobile;

import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

public interface BranchService {

    CreateObjectResponse createBranch(CreateBranchRequest request, Company company);

    void updateBranch(CreateBranchRequest request);

    BranchResponse getDetailBranch(Long branchId, Long companyId);

    void deleteBranch(Long branchId, Long companyId);

    List<BranchResponse> getListCompanyBranches(Long companyId);

    List<BranchResponse> toBranchResponseList(List<Branch> branches);

    Branch save(Branch branch);

    List<Branch> getByCompanyId(Long companyId);

    Branch getById(Long branchId);

    void updateBranchStaffStatus(Long branchId, Long companyId, String staffId, UpdateStaffRequest request);

    void removeBranchStaff(Long branchId, String staffId, Long companyId);

    Long inviteUser(Branch branch, Long companyId, User invitedUser, InviteStaffRequest request);
}
