package vn.flexin.backend.mono.company.service.mobile;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;
import vn.flexin.backend.mono.company.entity.Company;

import java.util.List;

public interface CompanyService {

    CreateObjectResponse createCompany(CreateCompanyRequest companyRequest);

    void updateCompany(CreateCompanyRequest companyRequest);

    void addCompanyToListFavorite(Long companyId);

    void removeCompanyFromListFavorite(Long companyId);

    void requestVerifyCompany(Long companyId, List<CreateVRDocumentRequest> documents);

    void deleteCompany(Long companyId);

    Company save(Company company);

    List<BranchResponse> getAllBranchesByCompany(Long companyId);

    List<StaffResponse> getAllStaffsByCompany(Long companyId);

    Company getCompanyById(Long id);

    Pair<List<SearchCompanyResponse>, PaginationResponse> searchCompany(CompanyFilter filter);

    CreateObjectResponse createBranch(CreateBranchRequest request);

    void updateBranch(CreateBranchRequest request);

    BranchResponse getDetailBranch(Long companyId, Long branchId);

    void deleteBranch(Long branchId, Long companyId);

    void updateStaffStatus(UpdateStaffRequest request, Long branchId, String staffId, Long companyId);

    void removeStaff(Long branchId, String staffId, Long companyId);

    CompanyAnalyticsResponse getCompanyAnalytics(Long companyId, String period, String branchFilter);

    void inviteUser(Long branchId, Long companyId, InviteStaffRequest request);

    DetailCompanyResponse getDetailCompany(Long companyId);
}
