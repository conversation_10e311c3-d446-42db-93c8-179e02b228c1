package vn.flexin.backend.mono.company.service.mobile;

import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.entity.Staff;

import java.util.List;

public interface StaffService {

    void deleteStaff(Staff staff);

    List<Staff> getStaffByBranchIds(List<Long> branchIds);

    StaffResponse toStaffResponse(Staff staff);

    void deleteByBranchId(Long branchId);

    Staff getById(Long staffId);

    Staff save(Staff staff);

    boolean isBranchManager(Long branchId, Long userId);

    Staff getByBranchIdAndUserUlid(Long id, String userId);
}
