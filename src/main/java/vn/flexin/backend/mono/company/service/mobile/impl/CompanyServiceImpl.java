package vn.flexin.backend.mono.company.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;
import vn.flexin.backend.mono.company.entity.VerificationRequest;
import vn.flexin.backend.mono.company.repository.CompanyRepository;
import vn.flexin.backend.mono.company.service.mobile.BranchService;
import vn.flexin.backend.mono.company.service.mobile.CompanyService;
import vn.flexin.backend.mono.company.service.mobile.VRDocumentService;
import vn.flexin.backend.mono.company.service.mobile.VerificationRequestService;
import vn.flexin.backend.mono.file.dto.FileDto;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.service.FileService;
import vn.flexin.backend.mono.notification.service.NotificationService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.List;
import java.util.Objects;

@Service
@AllArgsConstructor
public class CompanyServiceImpl implements CompanyService {
    private final UserService userService;
    private final CompanyRepository companyRepository;
    private final VerificationRequestService verificationRequestService;
    private final VRDocumentService vrDocumentService;
    private final FileService fileService;
    private final FileRepository fileRepository;
    private final BranchService branchService;
    private final StaffServiceImpl staffService;
    private final NotificationService notificationService;
    private final AddressService addressService;

    @Override
    public CreateObjectResponse createCompany(CreateCompanyRequest companyRequest) {
        User user = userService.getCurrentLoginUser();

        Company company = ModelMapperUtils.toObject(companyRequest, Company.class);
        setCompanyFiles(company, companyRequest);
        company.setUser(user);
        company.setVerified(false);
        company.setCreatedAt(CommonUtil.getCurrentUTCTime());
        company.setAddress(addressService.createAddress(companyRequest.getAddress()));
        company = save(company);
        return new CreateObjectResponse(company.getId());
    }

    @Override
    public void updateCompany(CreateCompanyRequest request) {
        User user = userService.getCurrentLoginUser();

        Company company = getCompanyById(request.getId());

        if (!Objects.equals(company.getUser(), user)) {
            throw new ForbiddenException();
        }

        company.setName(request.getName());
        company.setIndustry(request.getIndustry());
        company.setDescription(request.getDescription());
        company.setWebsiteUrl(request.getWebsiteUrl());
        company.setEmail(request.getEmail());
        company.setFoundedYear(request.getFoundedYear());
        company.setIndividual(request.isIndividual());
        company.setAdditionalData(request.getAdditionalData());
        company.setAddress(addressService.updateAddress(request.getAddress()));

        setCompanyFiles(company, request);
        save(company);
    }

    private void setCompanyFiles(Company company, CreateCompanyRequest request) {
        if (request.getLogo() != null) {
            File logo = fileService.getById(request.getLogo().getId(), fileRepository);
            company.setLogo(logo);
        }
        if (request.getCoverImage() != null) {
            File coverImage = fileService.getById(request.getCoverImage().getId(), fileRepository);
            company.setCoverImage(coverImage);
        }
        if (!CollectionUtils.isEmpty(request.getGalleryImages())) {
            List<Long> imageIds = request.getGalleryImages().stream().map(FileDto::getId).toList();
            List<File> galleryImages = fileService.getByIds(imageIds, fileRepository);
            company.setGalleryImages(galleryImages);
        }
    }

    public Company getCompanyById(Long id) {
        return companyRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Company not found."));
    }

    @Override
    public Pair<List<SearchCompanyResponse>, PaginationResponse> searchCompany(CompanyFilter filter) {
        var currentUser = userService.getCurrentLoginUser();
        filter.setUserId(currentUser.getId());
        Page<Company> page = companyRepository.findAll(filter);
        List<SearchCompanyResponse> responses = page.stream().map(this::toSearchCompanyResponse).toList();
        PaginationResponse paging = new PaginationResponse(filter.getLimit(), filter.getPage(), page.getNumberOfElements());
        return Pair.of(responses, paging);
    }

    @Override
    public CreateObjectResponse createBranch(CreateBranchRequest request) {
        Company company = getCompanyById(request.getCompanyId());
        User user = userService.getCurrentLoginUser();
        if (!Objects.equals(user.getId(), company.getUser().getId())) {
            throw new ForbiddenException();
        }
        return branchService.createBranch(request, company);
    }

    @Override
    public void updateBranch(CreateBranchRequest request) {
        Company company = getCompanyById(request.getCompanyId());
        User user = userService.getCurrentLoginUser();
        if (!Objects.equals(user.getId(), company.getUser().getId())) {
            throw new ForbiddenException();
        }
        branchService.updateBranch(request);
    }

    @Override
    public BranchResponse getDetailBranch(Long companyId, Long branchId) {
        getCompanyById(companyId);
        return branchService.getDetailBranch(branchId, companyId);
    }

    @Override
    public void deleteBranch(Long branchId, Long companyId) {
        Company company = getCompanyById(companyId);
        User user = userService.getCurrentLoginUser();
        if (!Objects.equals(user.getId(), company.getUser().getId())) {
            throw new ForbiddenException();
        }
        branchService.deleteBranch(branchId, companyId);
    }

    @Override
    public void updateStaffStatus(UpdateStaffRequest request, Long branchId, String staffId, Long companyId) {
         getCompanyById(companyId);
         branchService.updateBranchStaffStatus(branchId, companyId, staffId, request);
    }

    @Override
    public void removeStaff(Long branchId, String staffId, Long companyId) {
        Company company = getCompanyById(companyId);
        User currentUser = userService.getCurrentLoginUser();
        if (!Objects.equals(company.getUser().getId(), currentUser.getId())
            && !staffService.isBranchManager(branchId, currentUser.getId())
        ) {
            throw new ForbiddenException();
        }
        branchService.removeBranchStaff(branchId, staffId, companyId);
    }

    @Override
    public CompanyAnalyticsResponse getCompanyAnalytics(Long companyId, String period, String branchFilter) {
        return null;
    }

    @Override
    public void inviteUser(Long branchId, Long companyId, InviteStaffRequest request) {
        Company company = getCompanyById(companyId);
        Branch branch = branchService.getById(branchId);
        User invitedUser = userService.getUserByUlid(request.getUserId());
        User currentUser = userService.getCurrentLoginUser();

        if (!Objects.equals(company.getUser().getId(), currentUser.getId())
             && !staffService.isBranchManager(branchId, currentUser.getId())
        ) {
            throw new ForbiddenException();
        }

        Long staffId = branchService.inviteUser(branch, companyId, invitedUser, request);

        notificationService.sendCompanyJoiningInvitation(currentUser, invitedUser, branch, staffId);
    }

    @Override
    public DetailCompanyResponse getDetailCompany(Long companyId) {
        Company company = getCompanyById(companyId);
        return toDetailCompanyResponse(company);
    }

    private DetailCompanyResponse toDetailCompanyResponse(Company company) {
        DetailCompanyResponse response = ModelMapperUtils.toObject(company, DetailCompanyResponse.class);
        if (company.getLogo() != null) {
            response.setLogo(new FileDto(company.getLogo()));
        }
        if (company.getCoverImage() != null) {
            response.setCoverImage(new FileDto(company.getCoverImage()));
        }
        if(!CollectionUtils.isEmpty(company.getGalleryImages())) {
            List<FileDto> images = company.getGalleryImages().stream().map(FileDto::new).toList();
            response.setGalleryImages(images);
        }
        if (!CollectionUtils.isEmpty(company.getBranches())) {
            response.setBranches(branchService.toBranchResponseList(company.getBranches()));
        }
        if (company.getAddress() != null) {
            response.setAddress(new AddressResponse(company.getAddress()));
        }
        //TODO add logic set activeJobPostings in future
        return response;
    }

    private SearchCompanyResponse toSearchCompanyResponse(Company company) {
        SearchCompanyResponse response = ModelMapperUtils.toObject(company, SearchCompanyResponse.class);
        if (company.getLogo() != null) {
            response.setLogo(new FileDto(company.getLogo()));
        }
        if (company.getCoverImage() != null) {
            response.setCoverImage(new FileDto(company.getCoverImage()));
        }
        if(!CollectionUtils.isEmpty(company.getGalleryImages())) {
            List<FileDto> images = company.getGalleryImages().stream().map(FileDto::new).toList();
            response.setGalleryImages(images);
        }
        if (company.getAddress() != null) {
            response.setAddress(new AddressResponse(company.getAddress()));
        }
        if (!CollectionUtils.isEmpty(company.getBranches())) {
            response.setBranches(branchService.toBranchResponseList(company.getBranches()));
        }
        //TODO add logic set activeJobPostings in future
        return response;
    }

    @Override
    public void addCompanyToListFavorite(Long companyId) {
        User user = userService.getCurrentLoginUser();

        Company company = getCompanyById(companyId);

        List<Company> favoriteCompanies = user.getFavoriteCompanies();
        if(favoriteCompanies.contains(company)) {
            throw new BadRequestException("Company already added to list favorite.");
        }

        user.getFavoriteCompanies().add(company);
        userService.save(user);
    }

    @Override
    public void removeCompanyFromListFavorite(Long companyId) {
        User user = userService.getCurrentLoginUser();

        Company company = getCompanyById(companyId);

        List<Company> favoriteCompanies = user.getFavoriteCompanies();
        if(!favoriteCompanies.contains(company)) {
            throw new BadRequestException("Company not in list favorite.");
        }

        user.getFavoriteCompanies().remove(company);
        userService.save(user);
    }

    @Override
    public void requestVerifyCompany(Long companyId, List<CreateVRDocumentRequest> documentRequests) {
        User user = userService.getCurrentLoginUser();

        Company company = getCompanyById(companyId);

        if(!company.getUser().equals(user)) {
            throw new ForbiddenException();
        }

        if(company.isVerified()) {
            throw new BadRequestException("Company already verified.");
        }

        if(CollectionUtils.isEmpty(documentRequests)) {
            throw new BadRequestException("Documents array is required.");
        }

        VerificationRequest request = verificationRequestService.createVerificationRequest(company, user);
        vrDocumentService.createVRDocuments(documentRequests, request);
//        verificationRequestService.save(request);
    }

    @Override
    public void deleteCompany(Long companyId) {
        User user = userService.getCurrentLoginUser();

        Company company = getCompanyById(companyId);

        if(!company.getUser().equals(user)) {
            throw new ForbiddenException();
        }

        companyRepository.delete(company);
    }

    @Override
    public Company save(Company company) {
        return companyRepository.save(company);
    }

    @Override
    public List<BranchResponse> getAllBranchesByCompany(Long companyId) {
        getCompanyById(companyId);
        return branchService.getListCompanyBranches(companyId);
    }

    @Override
    public List<StaffResponse> getAllStaffsByCompany(Long companyId) {
        getCompanyById(companyId);
        List<Branch> branches = branchService.getByCompanyId(companyId);
        List<Long> branchIds = branches.stream().map(Branch::getId).toList();

        List<Staff> companyStaffs = staffService.getStaffByBranchIds(branchIds);
        return companyStaffs.stream().map(staffService::toStaffResponse).toList();
    }
}
