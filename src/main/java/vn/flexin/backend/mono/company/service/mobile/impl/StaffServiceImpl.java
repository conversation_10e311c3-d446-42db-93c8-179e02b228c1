package vn.flexin.backend.mono.company.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.dto.staff.StaffResponse;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Staff;
import vn.flexin.backend.mono.company.repository.StaffRepository;
import vn.flexin.backend.mono.company.service.mobile.StaffService;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Service
@AllArgsConstructor
public class StaffServiceImpl implements StaffService {

    private final StaffRepository staffRepository;

    // delete staff
    @Override
    public void deleteStaff(Staff staff) {
        staffRepository.delete(staff);
    }

    @Override
    public List<Staff> getStaffByBranchIds(List<Long> branchIds) {
        return staffRepository.findAllByBranchIdIn(branchIds);
    }

    @Override
    public StaffResponse toStaffResponse(Staff staff) {
        StaffResponse response = ModelMapperUtils.toObject(staff, StaffResponse.class);

        User user = staff.getUser();
        Branch branch = staff.getBranch();

        response.setId(user.getUlid());
        response.setName(user.getName());
        response.setPhotoUrl(user.getProfilePicture());

        response.setBranchId(branch.getId());
        response.setBranchName(branch.getName());

        return response;
    }

    @Override
    public void deleteByBranchId(Long branchId) {
        staffRepository.deleteAllByBranchId(branchId);
    }

    @Override
    public Staff getById(Long staffId) {
        return staffRepository.findById(staffId).orElseThrow(() -> new ResourceNotFoundException("Staff not found."));
    }

    @Override
    public Staff save(Staff staff) {
        staffRepository.save(staff);
        return staff;
    }

    public Staff getStaffByBranchIdAndUserId(Long branchId, Long userId) {
        return staffRepository.findByBranchIdAndUserId(branchId, userId).orElse(null);
    }

    @Override
    public boolean isBranchManager(Long branchId, Long userId){
        Staff staff = getStaffByBranchIdAndUserId(branchId, userId);
        return staff != null && staff.isManager();
    }

    @Override
    public Staff getByBranchIdAndUserUlid(Long branchId, String userId) {
        return staffRepository.findByBranchIdAndUserUlid(branchId, userId).orElse(null);
    }

}
