package vn.flexin.backend.mono.company.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.company.dto.company.CreateVRDocumentRequest;
import vn.flexin.backend.mono.company.entity.VerificationRequest;
import vn.flexin.backend.mono.company.entity.VerificationRequestDocument;
import vn.flexin.backend.mono.company.repository.VRDocumentRepository;
import vn.flexin.backend.mono.company.service.mobile.VRDocumentService;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.service.FileService;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class VRDocumentServiceImpl implements VRDocumentService {

    private final VRDocumentRepository vrDocumentRepository;
    private final FileRepository fileRepository;
    private final FileService fileService;

    @Override
    public void createVRDocuments(List<CreateVRDocumentRequest> documentRequests, VerificationRequest request) {
        List<Long> fileIds = documentRequests.stream()
                .filter(item -> item.getFile() != null)
                .map(item -> item.getFile().getId()).toList();
        List<File> files = fileService.getByIds(fileIds, fileRepository);
        Map<Long, File> fileMap = files.stream().collect(Collectors.toMap(File::getId, Function.identity()));
        List<VerificationRequestDocument> documents =
                documentRequests.stream().map(item -> new VerificationRequestDocument(item, request, fileMap)).toList();
        vrDocumentRepository.saveAll(documents);
//        request.setDocuments(documents);
    }
}
