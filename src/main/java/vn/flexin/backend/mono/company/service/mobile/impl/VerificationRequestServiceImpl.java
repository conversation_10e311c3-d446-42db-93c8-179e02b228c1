package vn.flexin.backend.mono.company.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.VerificationRequest;
import vn.flexin.backend.mono.company.repository.VerificationRequestRepository;
import vn.flexin.backend.mono.company.service.mobile.VerificationRequestService;
import vn.flexin.backend.mono.user.entity.User;

@Service
@AllArgsConstructor
public class VerificationRequestServiceImpl implements VerificationRequestService {

    private final VerificationRequestRepository verificationRequestRepository;

    @Override
    public VerificationRequest createVerificationRequest(Company company, User requestUser) {
        VerificationRequest request = new VerificationRequest(company, requestUser);
        request = verificationRequestRepository.save(request);

        return request;
    }

    @Override
    public VerificationRequest save(VerificationRequest request) {
        return verificationRequestRepository.save(request);
    }
}
