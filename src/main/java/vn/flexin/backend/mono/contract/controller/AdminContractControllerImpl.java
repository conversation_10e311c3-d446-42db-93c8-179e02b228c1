package vn.flexin.backend.mono.contract.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.contract.dto.*;
import vn.flexin.backend.mono.contract.service.ContractService;

import java.util.List;

@RestController
@RequestMapping("/v1/admin/contracts")
@RequiredArgsConstructor
@Slf4j
public class AdminContractControllerImpl implements AdminContractController {

    private final ContractService contractService;

    @Override
    public ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getAllContracts() {
        log.info("Admin retrieving all contracts");
        List<ContractDetailResponse> contracts = contractService.getAllContracts();
        return new ResponseEntity<>(ApiResponseDto.success(contracts), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<ContractResponse>>> searchContracts(@Valid ContractFilter filter) {
        log.info("Admin searching and filtering contracts with request: {}", filter);
        var result = contractService.searchContracts(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Contracts retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContractDetailResponse>> getContractById(Long id) {
        log.info("Admin retrieving contract with ID: {}", id);
        ContractDetailResponse contract = contractService.getContractById(id);
        return new ResponseEntity<>(ApiResponseDto.success(contract), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getContractsByEmployerId(Long employerId) {
        log.info("Admin retrieving contracts for employer ID: {}", employerId);
        List<ContractDetailResponse> contracts = contractService.getContractsByEmployerId(employerId);
        return new ResponseEntity<>(ApiResponseDto.success(contracts), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getContractsByFreelancerId(Long freelancerId) {
        log.info("Admin retrieving contracts for freelancer ID: {}", freelancerId);
        List<ContractDetailResponse> contracts = contractService.getContractsByFreelancerId(freelancerId);
        return new ResponseEntity<>(ApiResponseDto.success(contracts), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContractDetailResponse>> createContract(@Valid ContractRequet ContractRequet) {
        log.info("Admin creating new contract: {}", ContractRequet);
        ContractDetailResponse createdContract = contractService.createContract(ContractRequet);
        return new ResponseEntity<>(ApiResponseDto.success(createdContract), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContractDetailResponse>> updateContract(Long id, @Valid ContractRequet requet) {
        log.info("Admin updating contract with ID: {}", id);
        ContractDetailResponse updatedContract = contractService.updateContract(id, requet);
        return new ResponseEntity<>(ApiResponseDto.success(updatedContract), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContractDetailResponse>> updateContractStatus(Long id, String status, String adminNotes) {
        log.info("Admin updating contract status for ID: {} to status: {}", id, status);
        ContractDetailResponse updatedContract = contractService.updateContractStatus(id, status, adminNotes);
        return new ResponseEntity<>(ApiResponseDto.success(updatedContract), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteContract(Long id) {
        log.info("Admin deleting contract with ID: {}", id);
        return new ResponseEntity<>(ApiResponseDto.success(contractService.deleteContract(id)), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<TimeEntryResponse>>> searchTimeEntries(Long contractId, TimeEntryFilter filter) {
        filter.setContractId(contractId);
        log.info("Admin searching and filtering TimeEntries with request: {}", filter);
        var result = contractService.searchTimeEntry(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("TimeEntries retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<TimeEntryResponse>> createTimeEntry(TimeEntryRequest request) {
        log.info("Admin creating new timeEntry: {}", request);
        var timeEntry = contractService.createTimeEntry(request);
        return new ResponseEntity<>(ApiResponseDto.success(timeEntry), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<TimeEntryResponse>> updateTimeEntryStatus(Long contractId, Long id, String status) {
        log.info("Admin updating timeEntry status for ID: {} to status: {}", id, status);
        TimeEntryRequest request = new TimeEntryRequest();
        request.setId(id);
        request.setStatus(status);
        request.setContractId(contractId);
        var updateTimeEntryStatus = contractService.updateTimeEntryStatus(request);
        return new ResponseEntity<>(ApiResponseDto.success(updateTimeEntryStatus), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<PaymentRecordResponse>>> searchPaymentRecord(Long contractId, PaymentRecordFilter filter) {
        filter.setContractId(contractId);
        log.info("Admin searching and filtering PaymentRecord with request: {}", filter);
        var result = contractService.searchPaymentRecord(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("PaymentRecord retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<PaymentRecordResponse>> createPaymentRecord(PaymentRecordRequest request) {
        log.info("Admin creating new payment record: {}", request);
        var timeEntry = contractService.createPaymentRecord(request);
        return new ResponseEntity<>(ApiResponseDto.success(timeEntry), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<PaymentRecordResponse>> updatePaymentRecordStatus(Long contractId, Long id, PaymentRecordRequest request) {
        log.info("Admin updating payment record status for ID: {} to status: {}", id, request.getStatus());
        request.setId(id);
        request.setContractId(contractId);
        var update = contractService.updatePaymentRecordStatus(request);
        return new ResponseEntity<>(ApiResponseDto.success(update), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<ContractMessageResponse>>> searchMessage(Long contractId, ContractMessageFilter filter) {
        filter.setContractId(contractId);
        log.info("Admin searching and filtering messages with request: {}", filter);
        var result = contractService.searchMessage(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("messages retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContractMessageResponse>> updateMessageIsRead(Long contractId, Long id) {
        log.info("Admin updating payment record status for ID: {} ", id);
        ContractMessageRequest request = new ContractMessageRequest();
        request.setId(id);
        request.setContractId(contractId);
        var update = contractService.updateMessageIsRead(request);
        return new ResponseEntity<>(ApiResponseDto.success(update), HttpStatus.OK);
    }
}