package vn.flexin.backend.mono.contract.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class ContractDetailResponse {
    private Long id;

    private String title;

    private Long employerId;

    private String employerName;

    private Long jobSeekerId;

    private String jobSeekerName;

    private LocalDate startDate;

    private LocalDate endDate;

    private Double hourlyRate;

    private String paymentFrequency; // weekly, biweekly, monthly

    private Integer workingHoursPerWeek;

    private Set<String> workDays = new HashSet<>();

    private String contractType; // part-time, freelance, project

    private String status; // draft, offered, active, completed, terminated

    private LocalDateTime activatedAt;

    private LocalDateTime terminatedAt;

    private String terminationReason;

    private boolean isFeePaid;

    private String additionalTerms;

    private List<TimeEntryResponse> timeEntries;

    private Set<PaymentRecordResponse> payments;

    private List<ContractMessageResponse> messages;
} 