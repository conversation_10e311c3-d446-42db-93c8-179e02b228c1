package vn.flexin.backend.mono.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.user.entity.User;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class ContractFilter extends BaseFilter<Contract> {

    private String search;

    private String status;

    private String contractType;

    private String employerId;

    private String jobSeekerId;

        @Override
    public Specification<Contract> toSpecification() {
        var condition = new Condition()
                .append(new Where(Contract.Fields.status, status))
                .append(new Where(Contract.Fields.contractType, contractType));
            if (employerId != null) {
                var whereList = new ArrayList<Where>();
                whereList.add(new Where(User.Fields.id, employerId));
                condition.append(new Join(Contract.Fields.employer, whereList));
            }
            if (jobSeekerId != null) {
                var whereList = new ArrayList<Where>();
                whereList.add(new Where(User.Fields.id, jobSeekerId));
                condition.append(new Join(Contract.Fields.jobSeeker, whereList));
            }
            if (StringUtils.isNotBlank(search)) {
                var subCondition = new Condition()
                        .append(new Where(Contract.Fields.id, search))
                        .append(new Where(Contract.Fields.title, search));
                condition.appendComplex(new Where(Complex.OR, List.of(subCondition)));
            }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
