package vn.flexin.backend.mono.contract.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.contract.entity.ContractMessage;

import java.util.ArrayList;

@Data
@NoArgsConstructor
public class ContractMessageFilter extends BaseFilter<ContractMessage> {

    private Long contractId;

    private Boolean isRead; // pending, approved, rejected

    @Override
    public Specification<ContractMessage> toSpecification() {
        var condition = new Condition()
                .append(new Where(ContractMessage.Fields.isRead, isRead));
        if (contractId != null) {
            var whereList = new ArrayList<Where>();
            whereList.add(new Where(Contract.Fields.id, contractId));
            condition.append(new Join(ContractMessage.Fields.contract, whereList));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}