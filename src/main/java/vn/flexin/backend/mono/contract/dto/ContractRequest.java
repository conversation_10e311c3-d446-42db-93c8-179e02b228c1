package vn.flexin.backend.mono.contract.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ContractRequest {

    private Long id;

    @NotBlank(message = "PaymentMethod is required")
    private String title;

    @NotBlank(message = "PaymentMethod is required")
    private String employerId;

    @NotBlank(message = "PaymentMethod is required")
    private String employerName;

    @NotBlank(message = "PaymentMethod is required")
    private String jobSeekerId;

    private String jobSeekerName;

    private LocalDate startDate;

    private LocalDate endDate;

    private Double hourlyRate;

    private String paymentFrequency; // weekly, biweekly, monthly

    private Integer workingHoursPerWeek;

    private Set<String> workDays = new HashSet<>();

    private String contractType; // part-time, freelance, project

    private String status; // draft, offered, active, completed, terminated

    private LocalDateTime activatedAt;

    private LocalDateTime terminatedAt;

    private String terminationReason;

    private boolean isFeePaid;

    private String additionalTerms;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

} 