package vn.flexin.backend.mono.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ContractResponse {

    private Long id;

    private String title;

    private String employerId;

    private String employerName;

    private String jobSeekerId;

    private String jobSeekerName;

    private LocalDate startDate;

    private LocalDate endDate;

    private Double hourlyRate;

    private String paymentFrequency; // weekly, biweekly, monthly

    private Integer workingHoursPerWeek;

    private Set<String> workDays = new HashSet<>();

    private String contractType; // part-time, freelance, project

    private String status; // draft, offered, active, completed, terminated

    private LocalDateTime activatedAt;

    private LocalDateTime terminatedAt;

    private String terminationReason;

    private boolean isFeePaid;

    private String additionalTerms;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

} 