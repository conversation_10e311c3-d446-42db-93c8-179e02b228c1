package vn.flexin.backend.mono.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRecordResponse {

    private Long id;

    private LocalDateTime date;

    private Double amount;

    private String description;

    private String status; // pending, completed

    private String paymentMethod;

    private String transactionId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
} 