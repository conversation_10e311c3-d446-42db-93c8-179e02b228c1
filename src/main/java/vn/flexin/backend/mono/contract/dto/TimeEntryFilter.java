package vn.flexin.backend.mono.contract.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.contract.entity.TimeEntry;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;

@Data
@NoArgsConstructor
public class TimeEntryFilter extends BaseFilter<TimeEntry> {

    private Long contractId;

    private LocalDate startDate;

    private LocalDate endDate;
    
    private String status; // pending, approved, rejected

    @Override
    public Specification<TimeEntry> toSpecification() {
        var condition = new Condition()
                .append(new Where(TimeEntry.Fields.status, status))
                .append(new Where(TimeEntry.Fields.date, Operator.GREATER_THAN_OR_EQUAL, LocalDateTime.of(startDate, LocalTime.MIN)))
                .append(new Where(TimeEntry.Fields.date, Operator.LESS_THAN_OR_EQUAL, LocalDateTime.of(endDate, LocalTime.MAX)));
        if (contractId != null) {
            var whereList = new ArrayList<Where>();
            whereList.add(new Where(Contract.Fields.id, contractId));
            condition.append(new Join(TimeEntry.Fields.contract, whereList));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}