package vn.flexin.backend.mono.contract.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.contract.entity.TimeEntry;

import java.time.LocalDate;

@Data
@NoArgsConstructor
public class TimeEntryRequest {
    private Long id;

    @NotNull(message = "Date is required")
    private LocalDate date;

    @NotNull(message = "hoursWorked is required")
    @Positive(message = "hoursWorked must be positive")
    private Double hoursWorked;

    private String description;

    private String status; // pending, approved, rejected

    private String rejectionReason;

    private Long contractId;

    public TimeEntry toEntity() {
        TimeEntry timeEntry = new TimeEntry();
        timeEntry.setDate(date);
        timeEntry.setDescription(description);
        timeEntry.setStatus(status);
        timeEntry.setHours(hoursWorked);
        return timeEntry;
    }
} 