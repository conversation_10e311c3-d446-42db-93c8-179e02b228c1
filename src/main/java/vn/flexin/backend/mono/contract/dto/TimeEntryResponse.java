package vn.flexin.backend.mono.contract.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import vn.flexin.backend.mono.contract.entity.TimeEntry;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class TimeEntryResponse {
    private Long id;

    private LocalDate date;

    private Double hoursWorked;
    
    private String description;
    
    private String status; // pending, approved, rejected
    
    private String rejectionReason;

    private Long contractId;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

} 