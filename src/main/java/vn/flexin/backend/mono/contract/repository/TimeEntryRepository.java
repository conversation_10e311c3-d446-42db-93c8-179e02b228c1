package vn.flexin.backend.mono.contract.repository;

import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.contract.entity.TimeEntry;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Repository
public interface TimeEntryRepository extends JpaSpecificationRepository<TimeEntry, Long> {

} 