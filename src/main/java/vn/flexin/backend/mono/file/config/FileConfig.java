package vn.flexin.backend.mono.file.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Configuration
public class FileConfig {

    @Value("${storage.aws.access-key}")
    private String accessKeyId;

    @Value("${storage.aws.secret-access-key}")
    private String secretAccessKey;

    @Value("${storage.aws.region}")
    private String region;

    @Value("${storage.aws.avatar-bucket-name}")
    private String avatarBucket;

    @Value("${storage.aws.document-bucket-name}")
    private String documentBucket;

    @Value("${storage.aws.backup-bucket-name}")
    private String backupBucket;
}
