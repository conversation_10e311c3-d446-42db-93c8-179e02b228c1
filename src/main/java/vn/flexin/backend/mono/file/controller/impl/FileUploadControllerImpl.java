package vn.flexin.backend.mono.file.controller.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.file.controller.FileUploadController;
import vn.flexin.backend.mono.file.dto.FilePathResponse;
import vn.flexin.backend.mono.file.dto.ListFilePathResponse;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.service.FileService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@Slf4j
public class FileUploadControllerImpl implements FileUploadController {
    private final FileService fileService;

    public ResponseEntity<ApiResponse<FilePathResponse>> uploadFile(@RequestParam("file") MultipartFile file,
                                                                    @RequestParam("type") String type) {
        File filePath = fileService.uploadFile(file, type);
        if (filePath == null) {
            throw new BadRequestException("Can not upload file.");
        }

        return ResponseEntity.ok(ApiResponse.success(new FilePathResponse(filePath)));
    }

    public ResponseEntity<ApiResponse<ListFilePathResponse>> uploadMultipleFiles(@RequestParam("files") MultipartFile[] files, String type) {
        List<File> filePaths = fileService.uploadMultipleFiles(files, type);
        if (CollectionUtils.isEmpty(filePaths)) {
            throw new BadRequestException("Can not upload files.");
        }

        List<FilePathResponse> responses = new ArrayList<>();
        for (File flexinFile : filePaths) {
            FilePathResponse response = new FilePathResponse(flexinFile);
            responses.add(response);
        }

        return ResponseEntity.ok(ApiResponse.success(new ListFilePathResponse(responses)));
    }

    @Override
    public ResponseEntity<ApiResponse<Map<String, String>>> retrieveImageByName(String imageName) {
        Map<String, String> map = new HashMap<>();
        map.put("filePath", fileService.retrieveImagePathByName(imageName));
        return ResponseEntity.ok(ApiResponse.success("Successfully retrieve image", map));
    }

}

