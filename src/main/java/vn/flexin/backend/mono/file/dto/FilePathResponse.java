package vn.flexin.backend.mono.file.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import vn.flexin.backend.mono.file.entity.File;

@Data
@AllArgsConstructor
public class FilePathResponse {
    private String filePath;
    private Long id;
    private String type;
    private String fileName;

    public FilePathResponse(File file) {
        this.filePath = file.getFilePath();
        this.id = file.getId();
        this.type = file.getType().getType();
        this.fileName = file.getFileName();
    }
}
