package vn.flexin.backend.mono.file.entity;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.file.enums.FileType;

import java.io.Serial;
import java.io.Serializable;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_files")
public class File extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String filePath;

    @NotNull
    private String fileName;

    @Enumerated(EnumType.STRING)
    private FileType type;

    public File(String filePath, FileType fileType, String fileName) {
        this.filePath = filePath;
        this.type = fileType;
        this.fileName = fileName;
    }
}

