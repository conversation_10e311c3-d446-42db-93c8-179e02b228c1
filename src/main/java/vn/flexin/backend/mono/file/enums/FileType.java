package vn.flexin.backend.mono.file.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;
import vn.flexin.backend.mono.file.entity.File;

import java.util.Arrays;

@Getter
public enum FileType {
    DOCUMENT("document"),
    AVATAR("avatar");

    private final String type;

    FileType(String type) {
        this.type = type;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static FileType fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "File type must be any of [" + getValues() + "]");
        }
    }

    public static boolean isImageFile(File file) {
        FileType type = file.getType();
        return type == FileType.AVATAR;
    }
}
