package vn.flexin.backend.mono.file.service;

import org.springframework.web.multipart.MultipartFile;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.enums.FileType;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.utils.FileUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public interface FileService {

    File save(File file);

    void saveAll(List<File> files);

    void deleteByFilePath(String filePath);

    default File getFilePathByFileName(String fileName, FileRepository fileRepository) {
        return fileRepository.findFirstByFileName(fileName).orElse(null);
    }

    String retrieveImagePathByName(String name);

    default void deleteFile(Long fileId, FileRepository fileRepository) {
        Optional<File> fileOptional = fileRepository.findById(fileId);
        if(fileOptional.isEmpty()) {
            return;
        }
        File file = fileOptional.get();
        boolean deleted = handleDeleteFileOnCloudProvider(file);

        if (deleted) {
            deleteByFilePath(file.getFilePath());
        } else {
            throw new BadRequestException("Can not delete file.");
        }
    }

    boolean handleDeleteFileOnCloudProvider(File file);

    default String getFileUploadName(String originalFileName) {
        return CommonUtil.generateRandomAlphanumericString()+"-"+originalFileName;
    }

    default File uploadFile(MultipartFile file, String type) {
        validateInputFile(file);

        FileType fileType = FileType.fromString(type);
        String modifiedFileName = getFileUploadName(file.getOriginalFilename());
        String filePath = createFilePath(modifiedFileName, fileType);
        handleUploadFileToCloudProvider(file, modifiedFileName, fileType);

        File flexinFile = new File(filePath, fileType, modifiedFileName);
        save(flexinFile);

        return flexinFile;
    }

    default List<File> uploadMultipleFiles(MultipartFile[] files, String type) {
        List<File> flexinFiles = new ArrayList<>();

        FileType fileType = FileType.fromString(type);
        for (MultipartFile file : files) {
            String modifiedFileName = getFileUploadName(file.getOriginalFilename());
            String filePath = createFilePath(modifiedFileName, fileType);
            handleUploadFileToCloudProvider(file, modifiedFileName, fileType);

            File flexinFile = new File(filePath, fileType, modifiedFileName);
            flexinFiles.add(flexinFile);
        }
        saveAll(flexinFiles);

        return flexinFiles;
    }

    String createFilePath(String originalFilename, FileType fileType);

    void handleUploadFileToCloudProvider(MultipartFile file, String filePath, FileType fileType);

    default void validateInputFile(MultipartFile file) {
        if(file.getSize() >= 100_000_000) {
            throw new BadRequestException("Can not upload file larger than 100MB.");
        }
        if (!FileUtil.validateFileType(file)) {
            throw new BadRequestException("File upload type is not valid.");
        }

    }

    default File getById(Long id, FileRepository fileRepository) {
        return fileRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("File not found"));
    }

    default List<File> getByIds(List<Long> ids, FileRepository fileRepository) {
        return fileRepository.findAllById(ids);
    }
}
