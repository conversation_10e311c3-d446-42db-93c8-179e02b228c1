package vn.flexin.backend.mono.home.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SuitableCandidateDto {
    private Long id;

    @Size(max = 26)
    private String ulid;
    
    @NotBlank(message = "Name is required")
    @Size(max = 50, message = "Name must be less than 50 characters")
    private String name;

    private Set<String> skills;
    
    private String profilePicture;
    
    private boolean isActive = true;

    private LocalDateTime createdAt;

    private Double minHourlyRate;

    private String address;

    private Set<String> availableTimeSlots;

    private String description;

    public void setAvailableTimeSlots(Set<String> availableTimeSlots) {
        this.availableTimeSlots = availableTimeSlots;
    }

    private LocalDateTime lastModifiedAt;
}