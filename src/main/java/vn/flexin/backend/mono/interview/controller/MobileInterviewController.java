package vn.flexin.backend.mono.interview.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.dto.*;
import vn.flexin.backend.mono.interview.dto.request.ChangeInterviewRequest;
import vn.flexin.backend.mono.interview.dto.request.InterviewCancelledRequest;
import vn.flexin.backend.mono.interview.dto.request.RecordInterviewTimeRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewDetailResponse;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseEmployer;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseJobSeeker;

import java.util.List;

@Tag(name = "Mobile Interview APIs", description = "Mobile interview management endpoints")
public interface MobileInterviewController {
    @Operation(summary = "Search and filter list interviews", description = "Search and filter interviews with " +
            "pagination and sorting")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interviews retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("job-seeker/search/list")
    ResponseEntity<PaginationApiResponseDto<List<ListInterviewResponseJobSeeker>>> searchListInterviews(ListInterviewFilterJobSeeker filter);

    @Operation(summary = "Get interview counts by status", description = "Get count of interviews for each status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved counts"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @GetMapping("job-seeker/status/count")
    ResponseEntity<ApiResponseDto<List<InterviewStatusCount>>> getInterviewCountsByStatusJobSeeker();

    @Operation(summary = "Get interview details", description = "Get detailed information of an interview by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved interview details",
                    content = @Content(schema = @Schema(implementation = InterviewDetailResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @GetMapping("job-seeker/{id}/detail")
    ResponseEntity<ApiResponseDto<InterviewDetailResponse>> getInterviewDetail(@PathVariable Long id);

    @Operation(summary = "Cancel Interview", description = "Cancel an interview with reason")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview cancelled successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("job-seeker/cancel")
    ResponseEntity<ApiResponseDto<InterviewResponse>> cancelInterview(@Valid @RequestBody InterviewCancelledRequest request);

    @Operation(summary = "Change Interview", description = "Change interview details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview updated successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("job-seeker/change")
    ResponseEntity<ApiResponseDto<InterviewResponse>> changeInterview(@Valid @RequestBody ChangeInterviewRequest request);

    @Operation(summary = "Search and filter list interviews", description = "Search and filter interviews with " +
            "pagination and sorting")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interviews retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("employer/search/list")
    ResponseEntity<PaginationApiResponseDto<List<ListInterviewResponseEmployer>>> searchListInterviewsEmployer(ListInterviewFilterEmployer filter);

    @Operation(summary = "Get interview counts by status", description = "Get count of interviews for each status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved counts"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @GetMapping("employer/status/count")
    ResponseEntity<ApiResponseDto<List<InterviewStatusCount>>> getInterviewCountsByStatusEmployer();

    @Operation(summary = "Change Interview", description = "Change interview details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview updated successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("employer/change")
    ResponseEntity<ApiResponseDto<InterviewResponse>> changeInterviewEmployer(@Valid @RequestBody ChangeInterviewRequest request);

    @Operation(summary = "Cancel Interview", description = "Cancel an interview with reason")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview cancelled successfully",
                    content = @Content(schema = @Schema(implementation = InterviewResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("employer/cancel")
    ResponseEntity<ApiResponseDto<InterviewResponse>> cancelInterviewEmployer(@Valid @RequestBody InterviewCancelledRequest request);

    @Operation(summary = "Record interview start time", description = "Records when an interview starts")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Start time recorded successfully"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("/start-time")
    ResponseEntity<ApiResponseDto<InterviewResponse>> recordStartTime(@Valid @RequestBody RecordInterviewTimeRequest request);

    @Operation(summary = "Record interview end time", description = "Records when an interview ends")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "End time recorded successfully"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("/end-time")
    ResponseEntity<ApiResponseDto<InterviewResponse>> recordEndTime(@Valid @RequestBody RecordInterviewTimeRequest request);




}
