package vn.flexin.backend.mono.interview.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.application.dto.InterviewFilter;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.dto.admin.AdminInterviewResponse;
import vn.flexin.backend.mono.interview.dto.admin.CreateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.dto.admin.UpdateAdminInterviewRequest;

import java.util.List;

/**
 * Admin Interview Controller Interface
 * Provides REST API endpoints for administrative interview management
 */
@Tag(name = "Admin Interviews APIs", description = "Admin Interview Management")
@RequestMapping("/v1/admin/interviews")
public interface AdminInterviewController {

    @Operation(summary = "Search interviews for admin", description = "Retrieves a paginated list of interviews with filtering options for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interviews retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('interview_read')")
    ResponseEntity<PaginationApiResponseDto<List<AdminInterviewResponse>>> searchInterviewsForAdmin(
            @Valid @RequestBody InterviewFilter filter);

    @Operation(summary = "Get interview detail", description = "Get detailed interview information for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('interview_read')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse>> getInterviewDetail(@PathVariable("id") Long id);

    @Operation(summary = "Create a new interview", description = "Creates a new interview by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid interview data")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('interview_create')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse>> createInterview(
            @Valid @RequestBody CreateAdminInterviewRequest request);

    @Operation(summary = "Update interview", description = "Update interview information by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('interview_update')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse>> updateInterview(
            @PathVariable("id") Long id,
            @Valid @RequestBody UpdateAdminInterviewRequest request);

    @Operation(summary = "Delete interview", description = "Delete interview by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('interview_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteInterview(@PathVariable("id") Long id);

    @Operation(summary = "Update interview status", description = "Update interview status by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview status updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasAuthority('interview_update')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse>> updateInterviewStatus(
            @PathVariable("id") Long id,
            @RequestParam("status") String status);

    @Operation(summary = "Cancel interview", description = "Cancel interview by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview cancelled successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Interview not found")
    })
    @PatchMapping("/{id}/cancel")
    @PreAuthorize("hasAuthority('interview_update')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse>> cancelInterview(
            @PathVariable("id") Long id,
            @RequestParam("reason") String reason);

    @Operation(summary = "Get interview statistics", description = "Get interview statistics for admin dashboard")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interview statistics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials")
    })
    @GetMapping("/stats")
    @PreAuthorize("hasAuthority('interview_read')")
    ResponseEntity<ApiResponseDto<AdminInterviewResponse.InterviewStats>> getInterviewStats();
}
