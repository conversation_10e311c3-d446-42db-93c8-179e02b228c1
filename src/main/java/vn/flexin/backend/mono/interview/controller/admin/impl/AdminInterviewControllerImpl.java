package vn.flexin.backend.mono.interview.controller.admin.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.application.dto.InterviewFilter;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.controller.admin.AdminInterviewController;
import vn.flexin.backend.mono.interview.dto.admin.AdminInterviewResponse;
import vn.flexin.backend.mono.interview.dto.admin.CreateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.dto.admin.UpdateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.service.admin.AdminInterviewService;

import java.util.List;

/**
 * Admin Interview Controller Implementation
 * Handles REST API endpoints for administrative interview management
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class AdminInterviewControllerImpl implements AdminInterviewController {

    private final AdminInterviewService adminInterviewService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<AdminInterviewResponse>>> searchInterviewsForAdmin(InterviewFilter filter) {
        log.info("Admin searching interviews with filter: {}", filter);
        
        var result = adminInterviewService.searchInterviewsForAdmin(filter);
        
        return ResponseEntity.ok(PaginationApiResponseDto.success(
                "Interviews retrieved successfully", 
                result.getFirst(), 
                result.getSecond()
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse>> getInterviewDetail(Long id) {
        log.info("Admin getting interview detail for ID: {}", id);
        
        AdminInterviewResponse response = adminInterviewService.getInterviewDetailForAdmin(id);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Interview fetched successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse>> createInterview(CreateAdminInterviewRequest request) {
        log.info("Admin creating interview: {}", request);
        
        AdminInterviewResponse response = adminInterviewService.createAdminInterview(request);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Interview created successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse>> updateInterview(Long id, UpdateAdminInterviewRequest request) {
        log.info("Admin updating interview with ID: {} and request: {}", id, request);
        
        request.setId(id);
        AdminInterviewResponse response = adminInterviewService.updateAdminInterview(request);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Interview updated successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteInterview(Long id) {
        log.info("Admin deleting interview with ID: {}", id);
        
        adminInterviewService.deleteInterview(id);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                Boolean.TRUE, 
                "Interview deleted successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse>> updateInterviewStatus(Long id, String status) {
        log.info("Admin updating interview status - ID: {}, Status: {}", id, status);
        
        AdminInterviewResponse response = adminInterviewService.updateInterviewStatus(id, status);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Interview status updated successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse>> cancelInterview(Long id, String reason) {
        log.info("Admin cancelling interview - ID: {}, Reason: {}", id, reason);
        
        AdminInterviewResponse response = adminInterviewService.cancelInterview(id, reason);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Interview cancelled successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminInterviewResponse.InterviewStats>> getInterviewStats() {
        log.info("Admin getting interview statistics");
        
        AdminInterviewResponse.InterviewStats stats = adminInterviewService.getInterviewStats();
        
        return ResponseEntity.ok(ApiResponseDto.success(
                stats, 
                "Interview statistics retrieved successfully"
        ));
    }
}
