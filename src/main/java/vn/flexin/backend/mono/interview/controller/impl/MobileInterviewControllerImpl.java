package vn.flexin.backend.mono.interview.controller.impl;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.controller.MobileInterviewController;
import vn.flexin.backend.mono.interview.dto.*;
import vn.flexin.backend.mono.interview.dto.request.ChangeInterviewRequest;
import vn.flexin.backend.mono.interview.dto.request.InterviewCancelledRequest;
import vn.flexin.backend.mono.interview.dto.request.RecordInterviewTimeRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewDetailResponse;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseEmployer;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseJobSeeker;
import vn.flexin.backend.mono.interview.service.MobileInterviewService;

import java.util.List;

@RestController
@RequestMapping("/v1/mobile/interviews")
@RequiredArgsConstructor
@Slf4j
public class MobileInterviewControllerImpl implements MobileInterviewController {

    private final MobileInterviewService interviewService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<ListInterviewResponseJobSeeker>>> searchListInterviews(ListInterviewFilterJobSeeker filter) {
        var result = interviewService.searchListInterviews(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Interview retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<InterviewStatusCount>>> getInterviewCountsByStatusJobSeeker() {
        var counts = interviewService.getInterviewCountsByStatusJobSeeker();
        return ResponseEntity.ok(ApiResponseDto.success(counts));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewDetailResponse>> getInterviewDetail(Long id) {
        log.info("Getting interview detail for ID: {}", id);
        var response = interviewService.getInterviewDetail(id);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> cancelInterview(@Valid InterviewCancelledRequest request) {
        log.info("Admin cancelling interview with ID: {} and reason: {}", request.getId(), request.getReason());
        var response = interviewService.cancelInterview(request);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> changeInterview(@Valid ChangeInterviewRequest request) {
        var response = interviewService.changeInterview(request);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<ListInterviewResponseEmployer>>> searchListInterviewsEmployer(ListInterviewFilterEmployer filter) {
        var result = interviewService.searchListInterviewsEmployer(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Interview retrieved successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<InterviewStatusCount>>> getInterviewCountsByStatusEmployer() {
        var counts = interviewService.getInterviewCountsByStatusEmployer();
        return ResponseEntity.ok(ApiResponseDto.success(counts));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> changeInterviewEmployer(ChangeInterviewRequest request) {
        log.info("Admin changing interview with request: {}", request);
        var response = interviewService.changeInterviewEmployer(request);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> cancelInterviewEmployer(@Valid InterviewCancelledRequest request) {
        log.info("Admin cancelling interview with ID: {} and reason: {}", request.getId(), request.getReason());
        var response = interviewService.cancelInterviewEmployer(request);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }
    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> recordStartTime(@Valid RecordInterviewTimeRequest request) {
        var response = interviewService.recordInterviewTime(request, "start");
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }

    @Override
    public ResponseEntity<ApiResponseDto<InterviewResponse>> recordEndTime(@Valid RecordInterviewTimeRequest request) {
        var response = interviewService.recordInterviewTime(request, "end");
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }


}
