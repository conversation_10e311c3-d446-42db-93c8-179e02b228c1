package vn.flexin.backend.mono.interview.controller.impl;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.controller.MobileInterviewRequestController;
import vn.flexin.backend.mono.interview.dto.InterviewRequestFilter;
import vn.flexin.backend.mono.interview.dto.request.CreateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.request.UpdateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewRequestResponse;
import vn.flexin.backend.mono.interview.service.InterviewRequestService;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@AllArgsConstructor
public class MobileInterviewRequestControllerImpl implements MobileInterviewRequestController {

    private InterviewRequestService interviewRequestService;

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createRequest(CreateInterviewRequestRequest request) {
        CreateObjectResponse response = interviewRequestService.createRequest(request);
        return ResponseEntity.ok(ApiResponseDto.success(response, "Interview request created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateRequest(Long requestId, UpdateInterviewRequestRequest request) {
        interviewRequestService.updateRequest(requestId, request);
        return ResponseEntity.ok(ApiResponseDto.success(true, "Interview request updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> acceptRequest(Long requestId) {
        interviewRequestService.acceptRequest(requestId);
        return ResponseEntity.ok(ApiResponseDto.success(true, "Interview request accepted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> rejectRequest(Long requestId, String rejectNote) {
        interviewRequestService.rejectRequest(requestId, rejectNote);
        return ResponseEntity.ok(ApiResponseDto.success(true, "Interview request rejected successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> cancelRequest(Long requestId) {
        interviewRequestService.cancelRequest(requestId);
        return ResponseEntity.ok(ApiResponseDto.success(true, "Interview request cancelled successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> extendRequest(Long requestId, LocalDateTime newExpiredDate) {
        interviewRequestService.extendRequest(requestId, newExpiredDate);
        return ResponseEntity.ok(ApiResponseDto.success(true, "Interview request extended successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<InterviewRequestResponse>>> getRequestsByRequestUser(InterviewRequestFilter filter) {
        var result = interviewRequestService.getRequestsByUser(filter);
        return ResponseEntity.ok(PaginationApiResponseDto.success(
                "Retrieved interview requests successfully",
                result.getLeft(),
                result.getRight())
        );
    }

    @Override
    public ResponseEntity<InterviewRequestResponse> getRequestById(Long requestId) {
        InterviewRequestResponse request = interviewRequestService.getRequestById(requestId);
        return ResponseEntity.ok(request);
    }
}