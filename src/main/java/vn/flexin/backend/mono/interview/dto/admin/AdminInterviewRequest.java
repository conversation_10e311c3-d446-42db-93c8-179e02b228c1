package vn.flexin.backend.mono.interview.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Base admin interview request DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdminInterviewRequest {

    private Long id;

    private Long employerId;

    private String employerName;

    private Long jobSeekerId;

    private String jobSeekerName;

    private LocalDateTime scheduledTime;

    private Integer durationMinutes = 30;

    private String status = "scheduled"; // scheduled, in_progress, completed, cancelled

    private String meetingLink;

    private String meetingId;

    private String meetingCode;

    private String meetingPassword;

    private String notes;

    private String reason;

    private LocalDateTime startedAt;

    private LocalDateTime endedAt;

    private String cancellationReason;

    private String feedback;

    private Integer employerRating;

    private Integer jobSeekerRating;

    private Long postId;

    private String postTitle;

    private String cancelFrom; // job-seeker, employer

    @JsonProperty("isActive")
    private Boolean isActive = true;

    private Map<String, Object> additionalData;
}
