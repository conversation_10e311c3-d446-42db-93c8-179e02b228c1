package vn.flexin.backend.mono.interview.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Admin interview response DTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdminInterviewResponse {

    private Long id;

    private BasicUserInfoResponse employer;

    private BasicUserInfoResponse jobSeeker;

    private LocalDateTime scheduledTime;

    private Integer durationMinutes;

    private String status;

    private String meetingLink;

    private String meetingId;

    private String meetingCode;

    private String meetingPassword;

    private String notes;

    private String reason;

    private LocalDateTime startedAt;

    private LocalDateTime endedAt;

    private String cancellationReason;

    private String feedback;

    private Integer employerRating;

    private Integer jobSeekerRating;

    private Long postId;

    private String postTitle;

    private String cancelFrom;

    @JsonProperty("isActive")
    private Boolean isActive;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime lastModifiedAt;

    private String lastModifiedBy;

    private Map<String, Object> additionalData;

    /**
     * Interview statistics for admin dashboard
     */
    @Data
    public static class InterviewStats {
        private Long totalInterviews;
        private Long scheduledInterviews;
        private Long inProgressInterviews;
        private Long completedInterviews;
        private Long cancelledInterviews;
        private Double averageRating;
        private Long todayInterviews;
        private Long thisWeekInterviews;
        private Long thisMonthInterviews;
    }
}
