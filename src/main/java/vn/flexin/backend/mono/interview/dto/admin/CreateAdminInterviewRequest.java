package vn.flexin.backend.mono.interview.dto.admin;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Create admin interview request DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateAdminInterviewRequest extends AdminInterviewRequest {

    @NotNull(message = "Employer ID is required")
    private Long employerId;

    @NotNull(message = "Job seeker ID is required")
    private Long jobSeekerId;

    @NotNull(message = "Scheduled time is required")
    private LocalDateTime scheduledTime;

    @Positive(message = "Duration must be positive")
    private Integer durationMinutes = 30;
}
