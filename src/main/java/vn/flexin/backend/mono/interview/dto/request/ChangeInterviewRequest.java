package vn.flexin.backend.mono.interview.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.interview.entity.Interview;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ChangeInterviewRequest {
    private Long id;
    
    @NotNull(message = "Employer ID is required")
    private Long employerId;
    
    private String employerName;
    
    @NotNull(message = "Job seeker ID is required")
    private Long jobSeekerId;
    
    private String jobSeekerName;
    
    @NotNull(message = "Scheduled time is required")
    private LocalDateTime scheduledTime;
    
    @Positive(message = "Duration must be positive")
    private Integer durationMinutes = 30;

    private String reason;

    public Interview toEntity() {
        Interview interview = new Interview();
        interview.setId(id);
        interview.setScheduledTime(scheduledTime);
        interview.setDurationMinutes(durationMinutes);
        interview.setCancellationReason(reason);
        return interview;
    }
} 