package vn.flexin.backend.mono.interview.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ListInterviewResponseEmployer {
    private Long id;

    private Long jobSeekerId;

    private String jobSeekerName;

    private Long employerId;

    private String employerName;

    private LocalDateTime scheduledTime;

    private Integer durationMinutes;

    private String status;

    private LocalDateTime createdAt;

    private String createdBy;

    private String lastModifiedBy;

    private LocalDateTime lastModifiedAt = LocalDateTime.now();
} 