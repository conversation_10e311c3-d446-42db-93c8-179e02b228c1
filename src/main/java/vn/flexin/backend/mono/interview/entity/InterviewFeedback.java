package vn.flexin.backend.mono.interview.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.application.dto.InterviewFeedbackResponse;
import vn.flexin.backend.mono.application.dto.OfferDetails;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Set;

;

@Entity
@Table(name = "t_interview_feedback")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class InterviewFeedback extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Integer rating;
    private Integer communication;
    private Integer technicalSkills;
    private Integer culturalFit;
    private Integer experience;
    private String notes;
    private String decision;
    private String offerSalary;
    private LocalDateTime offerStartDate;
    private String offerNotes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "interview_id", nullable = false)
    private Interview interview;

    @ElementCollection
    @CollectionTable(
            name = "t_interview_feedback_strengths",
            joinColumns = @JoinColumn(name = "feedback_id")
    )
    @Column(name = "strength")
    private Set<String> strengths;

    @ElementCollection
    @CollectionTable(
            name = "t_interview_feedback_weaknesses",
            joinColumns = @JoinColumn(name = "feedback_id")
    )
    @Column(name = "weakness")
    private Set<String> weaknesses;

    public InterviewFeedbackResponse toResponse() {
        InterviewFeedbackResponse interviewFeedbackResponse = new InterviewFeedbackResponse();
        interviewFeedbackResponse.setRating(rating);
        interviewFeedbackResponse.setCommunication(communication);
        interviewFeedbackResponse.setTechnicalSkills(technicalSkills);
        interviewFeedbackResponse.setCulturalFit(culturalFit);
        interviewFeedbackResponse.setExperience(experience);
        interviewFeedbackResponse.setNotes(notes);
        interviewFeedbackResponse.setDecision(decision);
        interviewFeedbackResponse.setOfferDetails(OfferDetails.builder()
                .salary(offerSalary)
                .startDate(offerStartDate)
                .notes(offerNotes)
                .build());
        interviewFeedbackResponse.setStrengths(strengths);
        interviewFeedbackResponse.setWeaknesses(weaknesses);
        interviewFeedbackResponse.setInterviewId(interview.getId());

        return interviewFeedbackResponse;
    }
}