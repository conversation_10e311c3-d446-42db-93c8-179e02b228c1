package vn.flexin.backend.mono.interview.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum InterviewPermissions {
    INTERVIEW_READ("interview_read"),
    INTERVIEW_CREATE("interview_create"),
    INTERVIEW_UPDATE("interview_update"),
    INTERVIEW_DELETE("interview_delete"),
    INTERVIEW_CANCEL("interview_cancel"),
    INTERVIEW_STATISTICS("interview_statistics");

    private final String value;

    InterviewPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static InterviewPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Interview permissions type must be any of [" + getValues() + "]");
        }
    }
}
