package vn.flexin.backend.mono.interview.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.interview.dto.InterviewRequestFilter;
import vn.flexin.backend.mono.interview.dto.request.CreateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.request.UpdateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewRequestResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface InterviewRequestService {
    CreateObjectResponse createRequest(CreateInterviewRequestRequest request);
    void updateRequest(Long requestId, UpdateInterviewRequestRequest requestDto);
    void acceptRequest(Long requestId);
    void rejectRequest(Long requestId, String note);
    void cancelRequest(Long requestId);
    void extendRequest(Long requestId, LocalDateTime extendDate);
    void checkExpiredRequests();
    Pair<List<InterviewRequestResponse>, PaginationResponse> getRequestsByUser(InterviewRequestFilter filter);
    InterviewRequestResponse getRequestById(Long requestId);
}
