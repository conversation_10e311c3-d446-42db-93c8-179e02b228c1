package vn.flexin.backend.mono.interview.service;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.interview.dto.*;

import java.util.List;

public interface InterviewService {

    InterviewResponse createInterview(InterviewRequest request);

    InterviewResponse getInterviewById(Long id);

    Pair<List<InterviewResponse>, PaginationResponse> searchInterviews(InterviewFilter filters);

    InterviewResponse updateInterview(InterviewRequest updatedInterview);

    InterviewResponse updateInterviewStatus(Long id, String status);

    Boolean deleteInterview(Long id);

    InterviewFeedbackResponse createInterviewFeedback(InterviewFeedbackRequest request);

}