package vn.flexin.backend.mono.interview.service;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.interview.dto.*;
import vn.flexin.backend.mono.interview.dto.request.ChangeInterviewRequest;
import vn.flexin.backend.mono.interview.dto.request.InterviewCancelledRequest;
import vn.flexin.backend.mono.interview.dto.request.RecordInterviewTimeRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewDetailResponse;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseEmployer;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseJobSeeker;

import java.util.List;

public interface MobileInterviewService {

    Pair<List<ListInterviewResponseJobSeeker>, PaginationResponse> searchListInterviews(ListInterviewFilterJobSeeker filters);

    List<InterviewStatusCount> getInterviewCountsByStatusJobSeeker();

    InterviewDetailResponse getInterviewDetail(Long id);

    InterviewResponse cancelInterview(InterviewCancelledRequest request);

    InterviewResponse changeInterview(ChangeInterviewRequest request);

    Pair<List<ListInterviewResponseEmployer>, PaginationResponse> searchListInterviewsEmployer(ListInterviewFilterEmployer filters);

    List<InterviewStatusCount> getInterviewCountsByStatusEmployer();

    InterviewResponse changeInterviewEmployer(ChangeInterviewRequest request);

    InterviewResponse cancelInterviewEmployer(InterviewCancelledRequest request);

    InterviewResponse recordInterviewTime(RecordInterviewTimeRequest request, String type);
}