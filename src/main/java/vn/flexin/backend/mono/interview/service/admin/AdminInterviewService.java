package vn.flexin.backend.mono.interview.service.admin;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.application.dto.InterviewFilter;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.interview.dto.admin.AdminInterviewResponse;
import vn.flexin.backend.mono.interview.dto.admin.CreateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.dto.admin.UpdateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.entity.Interview;

import java.util.List;

/**
 * Admin service interface for managing interviews
 * Provides administrative operations for interview management
 */
public interface AdminInterviewService {

    /**
     * Search interviews with pagination and filtering for admin
     * @param filter Interview filter criteria
     * @return Pair of interview list and pagination response
     */
    Pair<List<AdminInterviewResponse>, PaginationResponse> searchInterviewsForAdmin(InterviewFilter filter);

    /**
     * Get interview by ID for admin view
     * @param id Interview ID
     * @return Admin interview response
     */
    AdminInterviewResponse getInterviewDetailForAdmin(Long id);

    /**
     * Create a new interview by admin
     * @param request Create interview request
     * @return Created interview response
     */
    AdminInterviewResponse createAdminInterview(CreateAdminInterviewRequest request);

    /**
     * Update an existing interview by admin
     * @param request Update interview request
     * @return Updated interview response
     */
    AdminInterviewResponse updateAdminInterview(UpdateAdminInterviewRequest request);

    /**
     * Delete an interview by admin
     * @param id Interview ID to delete
     */
    void deleteInterview(Long id);

    /**
     * Update interview status by admin
     * @param id Interview ID
     * @param status New status
     * @return Updated interview response
     */
    AdminInterviewResponse updateInterviewStatus(Long id, String status);

    /**
     * Cancel an interview by admin
     * @param id Interview ID
     * @param reason Cancellation reason
     * @return Updated interview response
     */
    AdminInterviewResponse cancelInterview(Long id, String reason);

    /**
     * Get interview entity by ID (for internal use)
     * @param id Interview ID
     * @return Interview entity
     */
    Interview getById(Long id);

    /**
     * Save interview entity
     * @param interview Interview entity to save
     * @return Saved interview entity
     */
    Interview save(Interview interview);

    /**
     * Get interview statistics for admin dashboard
     * @return Interview statistics
     */
    AdminInterviewResponse.InterviewStats getInterviewStats();
}
