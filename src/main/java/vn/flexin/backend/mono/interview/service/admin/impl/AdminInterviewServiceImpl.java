package vn.flexin.backend.mono.interview.service.admin.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.application.dto.InterviewFilter;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.interview.dto.admin.AdminInterviewResponse;
import vn.flexin.backend.mono.interview.dto.admin.CreateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.dto.admin.UpdateAdminInterviewRequest;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.interview.repository.InterviewRepository;
import vn.flexin.backend.mono.interview.service.admin.AdminInterviewService;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Admin service implementation for managing interviews
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AdminInterviewServiceImpl implements AdminInterviewService {

    private final InterviewRepository interviewRepository;
    private final UserService userService;
    private final PostRepository postRepository;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<AdminInterviewResponse>, PaginationResponse> searchInterviewsForAdmin(InterviewFilter filter) {
        log.info("Searching interviews for admin with filter: {}", filter);
        
        Page<Interview> interviews = interviewRepository.findAll(filter);
        List<AdminInterviewResponse> adminInterviewResponses = interviews.getContent()
                .stream()
                .map(this::toAdminInterviewResponse)
                .toList();
        
        PaginationResponse paging = new PaginationResponse(
                filter.getLimit(), 
                filter.getPage(), 
                (int) interviews.getTotalElements()
        );
        
        return Pair.of(adminInterviewResponses, paging);
    }

    @Override
    @Transactional(readOnly = true)
    public AdminInterviewResponse getInterviewDetailForAdmin(Long id) {
        log.info("Getting interview detail for admin with ID: {}", id);
        Interview interview = getById(id);
        return toAdminInterviewResponse(interview);
    }

    @Override
    @Transactional
    public AdminInterviewResponse createAdminInterview(CreateAdminInterviewRequest request) {
        log.info("Creating interview by admin: {}", request);
        
        Interview interview = ModelMapperUtils.toObject(request, Interview.class);
        
        // Set employer and job seeker
        interview.setEmployer(userService.getById(request.getEmployerId()));
        interview.setJobSeeker(userService.getById(request.getJobSeekerId()));
        
        // Set post if provided
        if (request.getPostId() != null) {
            interview.setPost(postRepository.findById(request.getPostId())
                    .orElseThrow(() -> new ResourceNotFoundException("Post not found")));
        }
        
        // Set default values
        if (interview.getStatus() == null) {
            interview.setStatus("scheduled");
        }
        if (interview.getDurationMinutes() == null) {
            interview.setDurationMinutes(30);
        }
        
        Interview savedInterview = save(interview);
        return toAdminInterviewResponse(savedInterview);
    }

    @Override
    @Transactional
    public AdminInterviewResponse updateAdminInterview(UpdateAdminInterviewRequest request) {
        log.info("Updating interview by admin: {}", request);
        
        Interview existingInterview = getById(request.getId());
        
        // Update fields
        ModelMapperUtils.map(request, existingInterview);
        
        // Update employer and job seeker if changed
        if (request.getEmployerId() != null && 
            !request.getEmployerId().equals(existingInterview.getEmployer().getId())) {
            existingInterview.setEmployer(userService.getById(request.getEmployerId()));
        }
        
        if (request.getJobSeekerId() != null && 
            !request.getJobSeekerId().equals(existingInterview.getJobSeeker().getId())) {
            existingInterview.setJobSeeker(userService.getById(request.getJobSeekerId()));
        }
        
        // Update post if changed
        if (request.getPostId() != null) {
            if (existingInterview.getPost() == null || 
                !request.getPostId().equals(existingInterview.getPost().getId())) {
                existingInterview.setPost(postRepository.findById(request.getPostId())
                        .orElseThrow(() -> new ResourceNotFoundException("Post not found")));
            }
        }
        
        Interview savedInterview = save(existingInterview);
        return toAdminInterviewResponse(savedInterview);
    }

    @Override
    @Transactional
    public void deleteInterview(Long id) {
        log.info("Deleting interview by admin with ID: {}", id);
        Interview interview = getById(id);
        interviewRepository.delete(interview);
    }

    @Override
    @Transactional
    public AdminInterviewResponse updateInterviewStatus(Long id, String status) {
        log.info("Updating interview status by admin - ID: {}, Status: {}", id, status);
        
        Interview interview = getById(id);
        interview.setStatus(status);
        
        // Set timestamps based on status
        LocalDateTime now = LocalDateTime.now();
        switch (status.toLowerCase()) {
            case "in_progress":
                if (interview.getStartedAt() == null) {
                    interview.setStartedAt(now);
                }
                break;
            case "completed":
                if (interview.getEndedAt() == null) {
                    interview.setEndedAt(now);
                }
                break;
            case "cancelled":
                if (interview.getEndedAt() == null) {
                    interview.setEndedAt(now);
                }
                break;
        }
        
        Interview savedInterview = save(interview);
        return toAdminInterviewResponse(savedInterview);
    }

    @Override
    @Transactional
    public AdminInterviewResponse cancelInterview(Long id, String reason) {
        log.info("Cancelling interview by admin - ID: {}, Reason: {}", id, reason);
        
        Interview interview = getById(id);
        interview.setStatus("cancelled");
        interview.setCancellationReason(reason);
        interview.setCancelFrom("admin");
        interview.setEndedAt(LocalDateTime.now());
        
        Interview savedInterview = save(interview);
        return toAdminInterviewResponse(savedInterview);
    }

    @Override
    @Transactional(readOnly = true)
    public Interview getById(Long id) {
        return interviewRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
    }

    @Override
    @Transactional
    public Interview save(Interview interview) {
        return interviewRepository.save(interview);
    }

    @Override
    @Transactional(readOnly = true)
    public AdminInterviewResponse.InterviewStats getInterviewStats() {
        log.info("Getting interview statistics for admin dashboard");
        
        // This would typically involve complex queries to get statistics
        // For now, returning a basic implementation
        AdminInterviewResponse.InterviewStats stats = new AdminInterviewResponse.InterviewStats();
        
        // You would implement actual statistics queries here
        // stats.setTotalInterviews(interviewRepository.count());
        // stats.setScheduledInterviews(interviewRepository.countByStatus("scheduled"));
        // etc.
        
        return stats;
    }

    /**
     * Convert Interview entity to AdminInterviewResponse
     */
    private AdminInterviewResponse toAdminInterviewResponse(Interview interview) {
        AdminInterviewResponse response = ModelMapperUtils.toObject(interview, AdminInterviewResponse.class);
        
        // Set employer info
        if (interview.getEmployer() != null) {
            BasicUserInfoResponse employer = ModelMapperUtils.toObject(interview.getEmployer(), BasicUserInfoResponse.class);
            response.setEmployer(employer);
        }
        
        // Set job seeker info
        if (interview.getJobSeeker() != null) {
            BasicUserInfoResponse jobSeeker = ModelMapperUtils.toObject(interview.getJobSeeker(), BasicUserInfoResponse.class);
            response.setJobSeeker(jobSeeker);
        }
        
        // Set post info
        if (interview.getPost() != null) {
            response.setPostId(interview.getPost().getId());
            response.setPostTitle(interview.getPost().getTitle());
        }
        
        return response;
    }
}
