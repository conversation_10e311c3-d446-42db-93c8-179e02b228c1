package vn.flexin.backend.mono.interview.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.interview.dto.InterviewRequestFilter;
import vn.flexin.backend.mono.interview.dto.request.CreateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.request.UpdateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewRequestResponse;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.repository.InterviewRequestRepository;
import vn.flexin.backend.mono.interview.service.InterviewRequestService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@AllArgsConstructor
public class InterviewRequestServiceImpl implements InterviewRequestService {

    private final UserService userService;

    private final InterviewRequestRepository interviewRequestRepository;
    private final UserRepository userRepository;

    @Override
    public CreateObjectResponse createRequest(CreateInterviewRequestRequest request) {
        User currentUser = userService.getCurrentLoginUser();

        User requestUser = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Request User not found"));
        User receiveUser = userRepository.findById(request.getReceiveUserId())
                .orElseThrow(() -> new ResourceNotFoundException("Receive User not found"));

        var optional =interviewRequestRepository.findByRequestUserIdAndReceiveUserIdAndPostId(
                currentUser.getId(),
                request.getReceiveUserId(),
                request.getPostId());

        if (optional.isPresent()) {
            throw new IllegalStateException("You already sent a interview request for this post and users");
        }

        InterviewRequest interviewRequest = new InterviewRequest();
        interviewRequest.setRequestUser(requestUser);
        interviewRequest.setReceiveUser(receiveUser);
        interviewRequest.setPostId(request.getPostId());
        interviewRequest.setPostType(request.getPostType());
        interviewRequest.setPostName(request.getPostName());
        interviewRequest.setExpiredDate(request.getExpiredDate());
        interviewRequest.setStatus(InterviewRequestStatus.REQUESTED);
        interviewRequest.setRequestNote(request.getRequestNote());

        interviewRequest = interviewRequestRepository.save(interviewRequest);

        return new CreateObjectResponse(interviewRequest.getId());
    }

    @Override
    public void updateRequest(Long requestId, UpdateInterviewRequestRequest requestDto) {
        User inviter = userService.getCurrentLoginUser();
        InterviewRequest request = getById(requestId);

        if (!Objects.equals(inviter.getUlid(), request.getCreatedBy())) {
            throw new ForbiddenException();
        }

        validateRequestStatus(request, InterviewRequestStatus.REQUESTED);

        // Update only the fields that are allowed to be updated
        request.setExpiredDate(requestDto.getExpiredDate());
        request.setStatus(requestDto.getStatus());
        request.setRequestNote(requestDto.getRequestNote());
        request.setRejectNote(requestDto.getRejectNote());
        request.setPostName(request.getPostName());

        interviewRequestRepository.save(request);
    }

    @Override
    public void acceptRequest(Long requestId) {
        User receiver = userService.getCurrentLoginUser();
        InterviewRequest request = getById(requestId);

        if (!Objects.equals(receiver.getUlid(), request.getCreatedBy())) {
            throw new ForbiddenException();
        }

        validateRequestStatus(request, InterviewRequestStatus.REQUESTED);
        request.setStatus(InterviewRequestStatus.ACCEPTED);
        interviewRequestRepository.save(request);
    }

    @Override
    public void rejectRequest(Long requestId, String rejectNote) {
        User receiver = userService.getCurrentLoginUser();
        InterviewRequest request = getById(requestId);

        if (!Objects.equals(receiver.getUlid(), request.getCreatedBy())) {
            throw new ForbiddenException();
        }
        validateRequestStatus(request, InterviewRequestStatus.REQUESTED);
        request.setStatus(InterviewRequestStatus.REJECTED);
        request.setRejectNote(rejectNote);
        interviewRequestRepository.save(request);
    }

    @Override
    public void cancelRequest(Long requestId) {
        User inviter = userService.getCurrentLoginUser();
        InterviewRequest request = getById(requestId);

        if (!Objects.equals(inviter.getUlid(), request.getCreatedBy())) {
            throw new ForbiddenException();
        }
        validateRequestStatus(request, InterviewRequestStatus.REQUESTED, InterviewRequestStatus.ACCEPTED);
        request.setStatus(InterviewRequestStatus.CANCELED);
        interviewRequestRepository.save(request);
    }

    @Override
    public void extendRequest(Long requestId, LocalDateTime newExpiredDate) {
        User inviter = userService.getCurrentLoginUser();
        InterviewRequest request = getById(requestId);

        if (!Objects.equals(inviter.getUlid(), request.getCreatedBy())) {
            throw new ForbiddenException();
        }
        validateRequestStatus(request, InterviewRequestStatus.REQUESTED, InterviewRequestStatus.ACCEPTED);

        if (newExpiredDate.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("New expiry date must be in the future");
        }

        request.setExpiredDate(newExpiredDate);
        interviewRequestRepository.save(request);
    }

    @Override
//    @Scheduled(cron = "0 0 * * * *")
    public void checkExpiredRequests() {
        List<InterviewRequest> expiredRequests = interviewRequestRepository
                .findByStatusAndExpiredDateBefore(InterviewRequestStatus.REQUESTED, LocalDateTime.now());

        for (InterviewRequest request : expiredRequests) {
            request.setStatus(InterviewRequestStatus.EXPIRED);
            interviewRequestRepository.save(request);
            log.info("Request {} has expired", request.getId());
        }
    }

    @Override
    public Pair<List<InterviewRequestResponse>, PaginationResponse> getRequestsByUser(InterviewRequestFilter filter) {
        Page<InterviewRequest> requests = interviewRequestRepository.findAll(filter);
        List<InterviewRequestResponse> response = requests.getContent().stream().map(this::toInterviewRequestResponse).toList();
        PaginationResponse paginationResponse = new PaginationResponse(filter.getLimit(), filter.getPage(), (int) requests.getTotalElements());
        return Pair.of(response, paginationResponse);
    }

    private InterviewRequestResponse toInterviewRequestResponse(InterviewRequest request) {
        InterviewRequestResponse response = new InterviewRequestResponse();
        response.setId(request.getId());
        response.setRequester(new BasicUserInfoResponse(request.getRequestUser()));
        response.setReceiver(new BasicUserInfoResponse(request.getReceiveUser()));
        response.setPostId(request.getPostId());
        response.setPostType(request.getPostType());
        response.setRequestNote(request.getRequestNote());
        response.setRejectNote(request.getRejectNote());
        response.setExpiredDate(request.getExpiredDate());
        response.setStatus(request.getStatus());
        response.setPostName(request.getPostName());
        return response;
    }

    @Override
    public InterviewRequestResponse getRequestById(Long requestId) {
        InterviewRequest request = getById(requestId);
        return toInterviewRequestResponse(request);
    }

    private InterviewRequest getById(Long id) {
        return interviewRequestRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Interview Request not found"));
    }

    // Helper method to validate the request status
    private void validateRequestStatus(InterviewRequest request, InterviewRequestStatus... allowedStatuses) {
        if (!Arrays.asList(allowedStatuses).contains(request.getStatus())) {
            throw new IllegalStateException("Invalid request status for this operation");
        }
    }
}
