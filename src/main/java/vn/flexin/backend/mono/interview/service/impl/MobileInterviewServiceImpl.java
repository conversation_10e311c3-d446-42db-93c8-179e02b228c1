package vn.flexin.backend.mono.interview.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.interview.dto.*;
import vn.flexin.backend.mono.interview.dto.request.ChangeInterviewRequest;
import vn.flexin.backend.mono.interview.dto.request.InterviewCancelledRequest;
import vn.flexin.backend.mono.interview.dto.request.RecordInterviewTimeRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewDetailResponse;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseEmployer;
import vn.flexin.backend.mono.interview.dto.response.ListInterviewResponseJobSeeker;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.interview.repository.InterviewRepository;
import vn.flexin.backend.mono.interview.service.MobileInterviewService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class MobileInterviewServiceImpl implements MobileInterviewService {
    private final InterviewRepository interviewRepository;
    private final UserService userService;
    private final UserKeycloakService userKeycloakService;

    @Override
    public Pair<List<ListInterviewResponseJobSeeker>, PaginationResponse> searchListInterviews(ListInterviewFilterJobSeeker filters) {
        var page = interviewRepository.findAll(filters);
        var responses = page.stream().map(interview -> toListInterviewResponse(interview)).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) page.getTotalElements());
        return Pair.of(responses, paging);
    }

    public ListInterviewResponseJobSeeker toListInterviewResponse(Interview interviewv) {
        ListInterviewResponseJobSeeker response = new ListInterviewResponseJobSeeker();
        response.setId(interviewv.getId());
        response.setJobSeekerId(interviewv.getJobSeeker().getId());
        response.setJobSeekerName(interviewv.getJobSeeker().getName());
        response.setScheduledTime(interviewv.getScheduledTime());
        response.setDurationMinutes(interviewv.getDurationMinutes());
        response.setStatus(interviewv.getStatus());
        response.setCreatedAt(interviewv.getCreatedAt());
        response.setCreatedBy(interviewv.getCreatedBy());
        response.setLastModifiedAt(interviewv.getLastModifiedAt());
        response.setLastModifiedBy(interviewv.getLastModifiedBy());
        return response;
    }

    @Override
    public List<InterviewStatusCount> getInterviewCountsByStatusJobSeeker() {
        UserRepresentation userRepresentation = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(userRepresentation.getUsername());

        return interviewRepository.getInterviewCountsByStatusAndJobSeekerId(user.getId());
    }

    @Override
    public InterviewDetailResponse getInterviewDetail(Long id) {
        var interview = interviewRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));

        return toInterviewDetailResponse(interview);
    }

    private InterviewDetailResponse toInterviewDetailResponse(Interview interview) {
        var response = new InterviewDetailResponse();
        response.setId(interview.getId());
        response.setJobSeekerId(interview.getJobSeeker().getId());
        response.setJobSeekerName(interview.getJobSeeker().getName());
        response.setScheduledTime(interview.getScheduledTime());
        response.setDurationMinutes(interview.getDurationMinutes());
        response.setStatus(interview.getStatus());
        response.setMeetingCode(interview.getMeetingCode());
        response.setNotes(interview.getNotes());
        response.setStartedAt(interview.getStartedAt());
        response.setEndedAt(interview.getEndedAt());
        response.setCreatedAt(interview.getCreatedAt());
        response.setCreatedBy(interview.getCreatedBy());
        response.setLastModifiedAt(interview.getLastModifiedAt());
        response.setLastModifiedBy(interview.getLastModifiedBy());
        return response;
    }

    @Override
    public InterviewResponse cancelInterview(InterviewCancelledRequest request) {
        var interview = interviewRepository.findById(request.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));

        interview.setStatus("cancelled");
        interview.setCancellationReason(request.getReason());
        interview.setCancelFrom("JobSeeker");

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public InterviewResponse changeInterview(ChangeInterviewRequest request) {
        var interview = interviewRepository.findById(request.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
        interview.setJobSeeker(userService.getById(request.getJobSeekerId()));
        interview.setScheduledTime(request.getScheduledTime());
        interview.setDurationMinutes(request.getDurationMinutes());
        interview.setCancellationReason(request.getReason());

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public Pair<List<ListInterviewResponseEmployer>, PaginationResponse> searchListInterviewsEmployer(ListInterviewFilterEmployer filters) {
        var page = interviewRepository.findAll(filters);
        var responses = page.stream().map(interview -> toListInterviewResponseEmployer(interview)).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) page.getTotalElements());
        return Pair.of(responses, paging);
    }

    private ListInterviewResponseEmployer toListInterviewResponseEmployer(Interview interview) {
        ListInterviewResponseEmployer response = new ListInterviewResponseEmployer();
        response.setId(interview.getId());
        response.setEmployerId(interview.getEmployer().getId());
        response.setEmployerName(interview.getEmployer().getName());
        response.setJobSeekerId(interview.getJobSeeker().getId());
        response.setJobSeekerName(interview.getJobSeeker().getName());
        response.setScheduledTime(interview.getScheduledTime());
        response.setDurationMinutes(interview.getDurationMinutes());
        response.setStatus(interview.getStatus());
        response.setCreatedAt(interview.getCreatedAt());
        response.setCreatedBy(interview.getCreatedBy());
        response.setLastModifiedAt(interview.getLastModifiedAt());
        response.setLastModifiedBy(interview.getLastModifiedBy());
        return response;
    }

    @Override
    public List<InterviewStatusCount> getInterviewCountsByStatusEmployer() {
        UserRepresentation userRepresentation = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(userRepresentation.getUsername());

        return interviewRepository.getInterviewCountsByStatusAndEmployerId(user.getId());
    }

    @Override
    public InterviewResponse changeInterviewEmployer(ChangeInterviewRequest request) {
        var interview = interviewRepository.findById(request.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));

        interview.setEmployer(userService.getById(request.getEmployerId()));
        interview.setJobSeeker(userService.getById(request.getJobSeekerId()));
        interview.setScheduledTime(request.getScheduledTime());
        interview.setDurationMinutes(request.getDurationMinutes());
        interview.setCancellationReason(request.getReason());

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public InterviewResponse cancelInterviewEmployer(InterviewCancelledRequest request) {
        var interview = interviewRepository.findById(request.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));

        interview.setStatus("cancelled");
        interview.setCancellationReason(request.getReason());
        interview.setCancelFrom("Employer");

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public InterviewResponse recordInterviewTime(RecordInterviewTimeRequest request, String type) {
        var interview = interviewRepository.findById(request.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));

        if ("start".equals(type)) {
            interview.setStartedAt(request.getTime());
            interview.setStatus("in_progress");
        } else if ("end".equals(type)) {
            interview.setEndedAt(request.getTime());
            interview.setStatus("completed");
        }

        return interviewRepository.save(interview).toResponse();
    }
}