package vn.flexin.backend.mono.job.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;

import jakarta.validation.Valid;
import java.util.List;

@Tag(name = "Admin Job APIs", description = "Admin job management endpoints")
@SecurityRequirement(name = "bearerAuth")
public interface AdminJobController {

    @Operation(summary = "Get all jobs", description = "Retrieve a list of all jobs (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> getAllJobs();

    @Operation(summary = "Search and filter jobs", description = "Search and filter jobs with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Page<JobDto>>> searchJobs(
            @Valid @RequestBody SearchJobRequest searchJobRequest);

    @Operation(summary = "Get job by ID", description = "Retrieve a job by its ID (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> getJobById(@PathVariable Long id);

    @Operation(summary = "Create job", description = "Create a new job (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Job created successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> createJob(@Valid @RequestBody JobDto jobDto);

    @Operation(summary = "Update job", description = "Update an existing job (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job updated successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> updateJob(
            @PathVariable Long id, @Valid @RequestBody JobDto jobDto);

    @Operation(summary = "Delete job", description = "Delete a job (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteJob(@PathVariable Long id);

    @Operation(summary = "Change job status", description = "Activate or deactivate a job (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job status changed successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> changeJobStatus(
            @PathVariable Long id, @RequestParam boolean active);

    @Operation(summary = "Feature job", description = "Mark a job as featured or not featured (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job featured status changed successfully",
                    content = @Content(schema = @Schema(implementation = JobDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PatchMapping("/{id}/featured")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> featureJob(
            @PathVariable Long id, @RequestParam boolean featured);
} 