package vn.flexin.backend.mono.job.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;
import vn.flexin.backend.mono.job.service.JobService;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/admin/jobs")
@RequiredArgsConstructor
@Slf4j
public class AdminJobControllerImpl implements AdminJobController {

    private final JobService jobService;

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> getAllJobs() {
        log.info("Admin retrieving all jobs");
        List<JobDto> jobs = jobService.getAllJobs();
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Page<JobDto>>> searchJobs(@Valid SearchJobRequest searchJobRequest) {
        log.info("Admin searching and filtering jobs with request: {}", searchJobRequest);
        Page<JobDto> jobs = jobService.searchJobs(searchJobRequest);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> getJobById(Long id) {
        log.info("Admin retrieving job with ID: {}", id);
        JobDto job = jobService.getJobById(id);
        return new ResponseEntity<>(ApiResponse.success("Job retrieved successfully", job), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> createJob(@Valid JobDto jobDto) {
        log.info("Admin creating new job: {}", jobDto);
        JobDto createdJob = jobService.createJob(jobDto);
        return new ResponseEntity<>(ApiResponse.success("Job created successfully", createdJob), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> updateJob(Long id, @Valid JobDto jobDto) {
        log.info("Admin updating job with ID: {}", id);
        jobDto.setId(id); // Ensure ID is set correctly
        JobDto updatedJob = jobService.updateJob(id, jobDto);
        return new ResponseEntity<>(ApiResponse.success("Job updated successfully", updatedJob), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteJob(Long id) {
        log.info("Admin deleting job with ID: {}", id);
        jobService.deleteJob(id);
        return new ResponseEntity<>(ApiResponse.success("Job deleted successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> changeJobStatus(Long id, boolean active) {
        log.info("Admin changing job status for job ID: {} to active: {}", id, active);
        JobDto job = jobService.getJobById(id);
        job.setStatus(active ? "ACTIVE" : "INACTIVE");
        JobDto updatedJob = jobService.updateJob(id, job);
        String message = active ? "Job activated successfully" : "Job deactivated successfully";
        return new ResponseEntity<>(ApiResponse.success(message, updatedJob), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> featureJob(Long id, boolean featured) {
        log.info("Admin changing featured status for job ID: {} to featured: {}", id, featured);
        JobDto job = jobService.getJobById(id);
        job.setFeatured(featured);
        JobDto updatedJob = jobService.updateJob(id, job);
        String message = featured ? "Job marked as featured successfully" : "Job unmarked as featured successfully";
        return new ResponseEntity<>(ApiResponse.success(message, updatedJob), HttpStatus.OK);
    }
} 