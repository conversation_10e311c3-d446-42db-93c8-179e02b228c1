package vn.flexin.backend.mono.job.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;

import java.util.List;

@Tag(name = "Mobile Job APIs", description = "Mobile job management endpoints")
public interface MobileJobController {
    
    @Operation(summary = "Create a new job", description = "Create a new job posting (Employer only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully created job"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    @PreAuthorize("hasRole('EMPLOYER')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> createJob(@Valid @RequestBody JobDto jobDto);
    
    @Operation(summary = "Get job by ID", description = "Get a job by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved job"),
        @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @GetMapping("/{id}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> getJobById(@PathVariable Long id);
    
    @Operation(summary = "Get all jobs", description = "Get a list of all jobs")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> getAllJobs();
    
    @Operation(summary = "Get jobs by employer ID", description = "Get a list of jobs posted by a specific employer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/employer/{employerId}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> getJobsByEmployerId(@PathVariable Long employerId);
    
    @Operation(summary = "Get jobs by status", description = "Get a list of jobs with a specific status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/status/{status}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> getJobsByStatus(@PathVariable String status);
    
    @Operation(summary = "Get jobs by employer ID and status", description = "Get a list of jobs posted by a specific employer with a specific status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/employer/{employerId}/status/{status}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> getJobsByEmployerIdAndStatus(
            @PathVariable Long employerId, @PathVariable String status);
    
    @Operation(summary = "Find jobs by location", description = "Find jobs in a specific location")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/location/{location}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> findJobsByLocation(@PathVariable String location);
    
    @Operation(summary = "Find jobs by job type", description = "Find jobs of a specific type")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/type/{jobType}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> findJobsByJobType(@PathVariable String jobType);
    
    @Operation(summary = "Find jobs by hourly rate range", description = "Find jobs within a specific hourly rate range")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs")
    })
    @GetMapping("/hourly-rate")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobDto>>> findJobsByHourlyRateRange(
            @RequestParam Double minRate, @RequestParam Double maxRate);
    
    @Operation(summary = "Update job", description = "Update a job posting (Employer only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated job"),
        @ApiResponse(responseCode = "404", description = "Job not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('EMPLOYER')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobDto>> updateJob(@PathVariable Long id, @Valid @RequestBody JobDto jobDto);
    
    @Operation(summary = "Delete job", description = "Delete a job posting (Employer or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted job"),
        @ApiResponse(responseCode = "404", description = "Job not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteJob(@PathVariable Long id);
    
    @Operation(summary = "Search and filter jobs", description = "Search and filter jobs with pagination and sorting")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved jobs"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Page<JobDto>>> searchJobs(
            @Valid @RequestBody SearchJobRequest searchJobRequest);
} 