package vn.flexin.backend.mono.job.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.user.dto.FilterOperator;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobFilter {
    @NotNull
    private JobFilterField field;
    
    private List<String> values;
    
    private FilterOperator operator;
} 