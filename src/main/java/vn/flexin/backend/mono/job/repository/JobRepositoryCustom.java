package vn.flexin.backend.mono.job.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.flexin.backend.mono.job.dto.JobFilter;
import vn.flexin.backend.mono.job.dto.JobSortField;
import vn.flexin.backend.mono.job.entity.Job;

import java.util.List;

public interface JobRepositoryCustom {
    Page<Job> searchJobs(String keyword, List<JobFilter> filters, Pageable pageable, JobSortField sortBy, Boolean ascending);
} 