package vn.flexin.backend.mono.job.service;

import org.springframework.data.domain.Page;
import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;
import vn.flexin.backend.mono.job.entity.Job;

import java.util.List;

public interface JobService {
    JobDto createJob(JobDto jobDto);
    JobDto getJobById(Long id);
    List<JobDto> getAllJobs();
    List<JobDto> getJobsByEmployerId(Long employerId);
    List<JobDto> getJobsByStatus(String status);
    List<JobDto> getJobsByEmployerIdAndStatus(Long employerId, String status);
    List<JobDto> searchJobs(String keyword);
    Page<JobDto> searchJobs(SearchJobRequest searchJobRequest);
    List<JobDto> findJobsByLocation(String location);
    List<JobDto> findJobsByJobType(String jobType);
    List<JobDto> findJobsByHourlyRateRange(Double minRate, Double maxRate);
    JobDto updateJob(Long id, JobDto jobDto);
    void deleteJob(Long id);
    Job getJobEntityById(Long id);
} 