package vn.flexin.backend.mono.lookup.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LookupFilter extends BaseFilter<Lookup> {
    private String type;
    private String displayText;
    private String displayTextEng;
    private Boolean isActive;

    @Override
    public Specification<Lookup> toSpecification() {
        var condition = new Condition();

        if (type != null) {
            condition.append(new Where(Lookup.Fields.type, type));
        }

        if (displayText != null) {
            condition.append(new Where(Lookup.Fields.displayText, Operator.LIKE_IGNORE_CASE, displayText));
        }

        if (displayTextEng != null) {
            condition.append(new Where(Lookup.Fields.displayTextEng, Operator.LIKE_IGNORE_CASE, displayTextEng));
        }

        if (isActive != null) {
            condition.append(new Where(Lookup.Fields.isActive, isActive));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
