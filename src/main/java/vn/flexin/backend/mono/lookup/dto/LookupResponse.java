package vn.flexin.backend.mono.lookup.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LookupResponse {
    private Long id;
    private String value;
    private String displayText;
    private String displayTextEng;
    private String type;
    private boolean isActive;
    private Map<Long, Object> additionalDataJson;
}
