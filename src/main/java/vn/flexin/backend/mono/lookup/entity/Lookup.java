package vn.flexin.backend.mono.lookup.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(name = "t_look_ups")
@FieldNameConstants
public class Lookup implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String value;

    private String displayText;

    private String displayTextEng;

    private String type;

    private boolean isActive;

    private String dependValue;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Map<Long, Object> additionalDataJson;

}
