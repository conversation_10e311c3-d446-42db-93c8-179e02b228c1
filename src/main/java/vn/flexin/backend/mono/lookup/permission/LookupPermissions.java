package vn.flexin.backend.mono.lookup.permission;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum LookupPermissions {
    LOOKUP_READ("lookup_read"),
    LOOKUP_UPDATE("lookup_update"),
    LOOKUP_DELETE("lookup_delete"),
    LOOKUP_CREATE("lookup_create");

    private final String value;

    LookupPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static LookupPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "User permissions type must be any of [" + getValues() + "]");
        }
    }
}
