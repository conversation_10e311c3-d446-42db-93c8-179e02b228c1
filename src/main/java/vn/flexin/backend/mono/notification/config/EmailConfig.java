package vn.flexin.backend.mono.notification.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class EmailConfig {

//    @Value("${mail.from-email}")
    private String fromEmail;

//    @Value("${spring.security.oauth2.client.registration.google.client-id}")
    private String clientId;

//    @Value("${spring.security.oauth2.client.registration.google.client-secret}")
    private String clientSecret;

//    @Value("${mail.uri.forget-password}")
    private String forgotPassWordUrl;

//    @Value("${mail.server-url}")
    private String serverUrl;

//    @Value("${server.port}")
    private Integer port;

    public String getForgotPasswordUrl() {
        return this.serverUrl + forgotPassWordUrl;
    }

}
