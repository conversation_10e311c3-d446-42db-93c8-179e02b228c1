package vn.flexin.backend.mono.notification.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@AllArgsConstructor
@Configuration
public class FirebaseConfig {

    private NotificationConfig notificationConfig;

//    @Bean
    public FirebaseApp firebaseApp() throws IOException {
        Resource resource = new ClassPathResource(notificationConfig.getCredentialFilePath());
        InputStream inputStream = resource.getInputStream();
        GoogleCredentials credentials = GoogleCredentials.fromStream(inputStream);
        FirebaseOptions options = FirebaseOptions.builder().setCredentials(credentials).build();

        return FirebaseApp.initializeApp(options);
    }
}
