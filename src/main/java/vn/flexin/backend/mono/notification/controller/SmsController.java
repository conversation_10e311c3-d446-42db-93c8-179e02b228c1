package vn.flexin.backend.mono.notification.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.notification.dto.request.SmsWebHookRequest;
import vn.flexin.backend.mono.notification.service.SmsService;

import java.security.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/sms")
@Tag(name = "Sms APIs", description = "Sms End points")
public class SmsController {
    private final SmsService smsService;

    @PostMapping("/webhook")
    public void receiveResult(@RequestParam("channel") String channel,
                              @RequestParam("tranId") String tranId,
                              @RequestParam("delivery") int delivery,
                              @RequestParam("phone") String phone,
                              @RequestParam("delivery_time") String deliveryTime) {
        LocalDateTime time = CommonUtil.convertTimestampToLocalDateTime(deliveryTime);
        SmsWebHookRequest request = new SmsWebHookRequest(channel, tranId, delivery, time, phone);
        smsService.webhook(request);
    }

    @GetMapping("/account")
    public ResponseEntity<ApiResponseDto<Map<String, Object>>> getUserInfo() {
        return ResponseEntity.ok(ApiResponseDto.success(smsService.getUserInfo()));
    }
}
