package vn.flexin.backend.mono.notification.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.notification.enums.NotificationStatus;
import vn.flexin.backend.mono.notification.enums.NotificationType;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_notifications")
public class Notification extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "receiver_id")
    private User receiver;

    private String data;

    private String title;

    private String type;

    private String status;

    private boolean isResend;

    private LocalDateTime schedule;

    private Integer sendFailedCount;

    public void setType(NotificationType type) {
        this.type = type.value();
    }

    public void setStatus(NotificationStatus status) {
        this.status = status.value();
    }

}
