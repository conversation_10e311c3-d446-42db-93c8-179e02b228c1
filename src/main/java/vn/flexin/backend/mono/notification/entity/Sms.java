package vn.flexin.backend.mono.notification.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.common.util.CommonUtil;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_smses")
public class Sms extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String receiverNumber;

    private String formattedReceiverNumber;

    private String content;

    private boolean success;

    private LocalDateTime sendTime;

    private int failCount = 0;

    public Sms(String receiverNumber, String content, LocalDateTime sendTime) {
        this.receiverNumber = receiverNumber;
        this.formattedReceiverNumber = CommonUtil.formatPhoneNumber(receiverNumber);
        this.content = content;
        this.sendTime = sendTime;
        this.success = false;
        this.failCount = 0;
    }

}
