package vn.flexin.backend.mono.notification.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum NotificationType {
    COMPANY_JOINING_INVITATION("company_joining_invitation"),
    COMPANY_FOLLOW("company_follow"),
    FOLLOW_POST("follow_post");

    private final String value;

    NotificationType(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static NotificationType fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "NotificationData type must be any of [" + getValues() + "]");
        }
    }
}
