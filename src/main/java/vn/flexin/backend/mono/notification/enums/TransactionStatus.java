package vn.flexin.backend.mono.notification.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum TransactionStatus {
    SUCCESS("success"),
    SENT("sent"),
    FAILED("failed");

    public final String value;

    TransactionStatus(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static TransactionStatus fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Transaction status must be any of [" + getValues() + "]");
        }
    }
}
