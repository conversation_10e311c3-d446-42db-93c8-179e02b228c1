package vn.flexin.backend.mono.notification.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.notification.entity.SmsTransactionLog;

public interface SmsTransactionLogRepository extends JpaRepository<SmsTransactionLog, Long> {

    @Query("""
        SELECT log
        FROM SmsTransactionLog log
        WHERE log.transaction.id = :tranId
        AND log.sms.formattedReceiverNumber = :phone
    """)
    SmsTransactionLog findByTranIdAndSmsReceivePhone(String tranId, String phone);
}
