package vn.flexin.backend.mono.notification.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.notification.entity.SmsTransaction;

public interface SmsTransactionRepository extends JpaRepository<SmsTransaction, Long> {

    @Query(value = """
        SELECT tran
        FROM SmsTransaction tran
        JOIN FETCH tran.logs
        WHERE tran.tranId = :tranId
    """)
    SmsTransaction findByTranId(Long tranId);
}
