package vn.flexin.backend.mono.notification.service;

import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.notification.config.EmailConfig;
import vn.flexin.backend.mono.notification.dto.request.SendMailDataRequest;
import vn.flexin.backend.mono.user.entity.User;

import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Locale;
import java.util.Properties;

@Service
@Slf4j
@AllArgsConstructor
public class EmailService {

//    private final Gmail gmailService;

    private final EmailConfig emailConfig;

    private final SpringTemplateEngine templateEngine;

    private final MessageSource messageSource;

    private static final Locale defaultLocale = Locale.US;


    public void sendForgotPasswordEmail(User user, String token) {
        String url = emailConfig.getForgotPasswordUrl() + "/?token=" + token;
        SendMailDataRequest request = new SendMailDataRequest();
        request.setToEmail(user.getEmail());
        request.setTitle("RESET_PASSWORD_TITLE");
        request.setTemplateName("mail/passwordResetEmail");
        Context context = new Context(defaultLocale);
        context.setVariable("url", url);
        context.setVariable("name", user.getName());
        request.setContext(context);

        sendEmail(request);
    }

    public void sendEmail(SendMailDataRequest request) {
        try {
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props, null);
            MimeMessage email = new MimeMessage(session);

            String htmlContent = processTemplate(request.getTemplateName(), request.getContext());

            email.setFrom(new InternetAddress(emailConfig.getFromEmail()));
            email.addRecipient(jakarta.mail.Message.RecipientType.TO, new InternetAddress(request.getToEmail()));
            email.setSubject(messageSource.getMessage(request.getTitle(), null, defaultLocale));
            email.setContent(htmlContent, "text/html; charset=utf-8");

            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            email.writeTo(buffer);
            byte[] rawMessageBytes = buffer.toByteArray();
            String encodedEmail = Base64.getUrlEncoder().encodeToString(rawMessageBytes);
//            Message message = new Message();
//            message.setRaw(encodedEmail);

//            gmailService.users().messages().send("me", message).execute();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
    }

    private String processTemplate(String templateName, Context context) {
        try {
            return templateEngine.process(templateName, context);
        } catch (Exception e) {
            throw new RuntimeException("Error processing template: " + templateName, e);
        }

    }
}
