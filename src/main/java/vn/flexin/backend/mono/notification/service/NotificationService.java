package vn.flexin.backend.mono.notification.service;

import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.notification.dto.NotificationData;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

public interface NotificationService {
    void pushToDevices(List<String> deviceTokens, NotificationData notificationData);

    void pushToTopic(String topic, NotificationData notificationData);

    void subscribeTopic(String topic, List<String> tokens);

    void unSubscribeTopic(String topic, List<String> tokens);

    void sendCompanyJoiningInvitation(User sender, User receiver, Branch branch, Long staffId);
}
