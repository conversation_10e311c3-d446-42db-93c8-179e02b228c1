package vn.flexin.backend.mono.notification.service;

import com.google.firebase.messaging.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.notification.dto.NotificationBody;
import vn.flexin.backend.mono.notification.dto.NotificationData;
import vn.flexin.backend.mono.notification.enums.NotificationType;
import vn.flexin.backend.mono.notification.enums.NotificationTitleTemplate;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;
import vn.flexin.backend.mono.user.service.UserDeviceService;

import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class NotificationServiceImpl implements NotificationService {

    private final UserDeviceService userDeviceService;

    public void sendPushNotificationMultiTokens(List<String> tokens, String title, String body) {
        Notification notification = Notification.builder()
                .setTitle(title)
                .setBody(body)
                .build();

        MulticastMessage message = MulticastMessage.builder()
                .addAllTokens(tokens)
                .setNotification(notification)
                .build();

        try {
            FirebaseMessaging.getInstance().sendMulticast(message);
            log.info("Send notification successfully");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void sendPushNotificationToTopic(String topic, String title, String body) {
        Message message = Message.builder()
                .setTopic(topic)
                .setNotification(
                    Notification.builder()
                        .setTitle(title)
                        .setBody(body)
                        .build()
                ).build();

        try {
            FirebaseMessaging.getInstance().send(message);
        } catch (Exception e) {
            log.error(e.getMessage());

        }
    }

    public void subscribeToTopic(List<String> tokens, String topic) {
        try {
            TopicManagementResponse response = FirebaseMessaging.getInstance().subscribeToTopic(tokens, topic);
            log.info("Number of Success device sent: " + response.getSuccessCount());
            log.info("Number of Fail device sent: " + response.getFailureCount());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void unsubscribeFromTopic(List<String> tokens, String topic) {
        try {
            TopicManagementResponse response = FirebaseMessaging.getInstance().unsubscribeFromTopic(tokens, topic);
            log.info("Number of Success device sent: " + response.getSuccessCount());
            log.info("Number of Fail device sent: " + response.getFailureCount());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public void pushToDevices(List<String> deviceTokens, NotificationData notificationData) {
        String body = notificationData.getBody().toString();
        String title = notificationData.getTitle();
        sendPushNotificationMultiTokens(deviceTokens, title, body);
    }

    @Override
    public void pushToTopic(String topic, NotificationData notificationData) {
        String body = notificationData.getBody().toString();
        String title = notificationData.getTitle();
        sendPushNotificationToTopic(topic, title, body);
    }

    @Override
    public void subscribeTopic(String topic, List<String> tokens) {
        subscribeToTopic(tokens, topic);
    }

    @Override
    public void unSubscribeTopic(String topic, List<String> tokens) {
        unsubscribeFromTopic(tokens, topic);
    }

    @Override
    public void sendCompanyJoiningInvitation(User sender, User receiver, Branch branch, Long staffId) {
        List<UserDevice> receiverDevices = userDeviceService.getByUserId(receiver.getId()).stream()
                .filter(UserDevice::isDeviceVerified)
                .filter(device -> !device.isSignUpComplete())
                .filter(device -> !device.isLogout()).toList();

        List<String> deviceTokens = receiverDevices.stream().map(UserDevice::getDeviceToken).toList();
        if (CollectionUtils.isEmpty(deviceTokens)) {
            return;
        }

        NotificationData<Long> notificationData = getLongNotification(sender, branch, staffId);
        pushToDevices(deviceTokens, notificationData);
    }

    private NotificationData<Long> getLongNotification(User sender, Branch branch, Long staffId) {
        NotificationData<Long> notificationData = new NotificationData<>();
        notificationData.setTitle(
                NotificationTitleTemplate.getInviteJoinCompanyTitle(sender.getName(), branch.getName(), branch.getCompany().getName())
        );

        NotificationBody<Long> body = new NotificationBody<>();
        body.setNotificationType(NotificationType.COMPANY_JOINING_INVITATION.value());
        body.setMessage(notificationData.getTitle());
        body.setData(staffId);

        notificationData.setBody(body);
        return notificationData;
    }
}
