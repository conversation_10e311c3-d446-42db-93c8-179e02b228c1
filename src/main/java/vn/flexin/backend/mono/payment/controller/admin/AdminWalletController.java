package vn.flexin.backend.mono.payment.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.payment.dto.request.AdminWalletFilter;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.response.AdminWalletResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;

import java.util.List;

/**
 * Admin Wallet Controller Interface
 * Provides REST API endpoints for administrative wallet management
 */
@Tag(name = "Admin Wallet APIs", description = "Admin Wallet Management")
@RequestMapping("/v1/admin/wallets")
public interface AdminWalletController {

    @Operation(summary = "Search wallets for admin", description = "Retrieves a paginated list of wallets with filtering options for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallets retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('wallet_read')")
    ResponseEntity<PaginationApiResponseDto<List<AdminWalletResponse>>> searchWalletsForAdmin(
            @Valid @RequestBody AdminWalletFilter filter);

    @Operation(summary = "Get wallet detail", description = "Get detailed wallet information for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallet fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Wallet not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('wallet_read')")
    ResponseEntity<ApiResponseDto<AdminWalletResponse>> getWalletDetail(@PathVariable("id") Long id);

    @Operation(summary = "Get wallet by user ID", description = "Get wallet information by user ID for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallet fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Wallet not found")
    })
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('wallet_read')")
    ResponseEntity<ApiResponseDto<AdminWalletResponse>> getWalletByUserId(@PathVariable("userId") Long userId);

    @Operation(summary = "Get wallet transactions", description = "Get wallet transactions for admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Transactions retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Wallet not found")
    })
    @PostMapping("/{id}/transactions")
    @PreAuthorize("hasAuthority('wallet_read')")
    ResponseEntity<PaginationApiResponseDto<List<PersonalTransactionResponse>>> getWalletTransactions(
            @PathVariable("id") Long id,
            @Valid @RequestBody PersonalTransactionFilter filter);

    @Operation(summary = "Adjust wallet points", description = "Adjust wallet points by admin")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallet points adjusted successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Wallet not found")
    })
    @PatchMapping("/{id}/adjust")
    @PreAuthorize("hasAuthority('wallet_update')")
    ResponseEntity<ApiResponseDto<AdminWalletResponse>> adjustWalletPoints(
            @PathVariable("id") Long id,
            @RequestParam("points") Long points,
            @RequestParam("reason") String reason);

    @Operation(summary = "Get wallet statistics", description = "Get wallet statistics for admin dashboard")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallet statistics retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials")
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('wallet_read')")
    ResponseEntity<ApiResponseDto<AdminWalletResponse.WalletStatistics>> getWalletStatistics();
}
