package vn.flexin.backend.mono.payment.controller.admin.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.payment.controller.admin.AdminWalletController;
import vn.flexin.backend.mono.payment.dto.request.AdminWalletFilter;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.response.AdminWalletResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.service.admin.AdminWalletService;

import java.util.List;

/**
 * Admin Wallet Controller Implementation
 * Handles REST API endpoints for administrative wallet management
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class AdminWalletControllerImpl implements AdminWalletController {

    private final AdminWalletService adminWalletService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<AdminWalletResponse>>> searchWalletsForAdmin(AdminWalletFilter filter) {
        log.info("Admin searching wallets with filter: {}", filter);
        
        var result = adminWalletService.searchWalletsForAdmin(filter);
        
        return ResponseEntity.ok(PaginationApiResponseDto.success(
                "Wallets retrieved successfully", 
                result.getFirst(), 
                result.getSecond()
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminWalletResponse>> getWalletDetail(Long id) {
        log.info("Admin getting wallet detail for ID: {}", id);
        
        AdminWalletResponse response = adminWalletService.getWalletDetailForAdmin(id);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Wallet fetched successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminWalletResponse>> getWalletByUserId(Long userId) {
        log.info("Admin getting wallet by user ID: {}", userId);
        
        AdminWalletResponse response = adminWalletService.getWalletByUserIdForAdmin(userId);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Wallet fetched successfully"
        ));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<PersonalTransactionResponse>>> getWalletTransactions(Long id, PersonalTransactionFilter filter) {
        log.info("Admin getting wallet transactions for wallet ID: {} with filter: {}", id, filter);
        
        var result = adminWalletService.getWalletTransactionsForAdmin(id, filter);
        
        return ResponseEntity.ok(PaginationApiResponseDto.success(
                "Wallet transactions retrieved successfully", 
                result.getFirst(), 
                result.getSecond()
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminWalletResponse>> adjustWalletPoints(Long id, Long points, String reason) {
        log.info("Admin adjusting wallet points - ID: {}, Points: {}, Reason: {}", id, points, reason);
        
        AdminWalletResponse response = adminWalletService.adjustWalletPoints(id, points, reason);
        
        return ResponseEntity.ok(ApiResponseDto.success(
                response, 
                "Wallet points adjusted successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<AdminWalletResponse.WalletStatistics>> getWalletStatistics() {
        log.info("Admin getting wallet statistics");
        
        AdminWalletResponse.WalletStatistics stats = adminWalletService.getWalletStatisticsSummary();
        
        return ResponseEntity.ok(ApiResponseDto.success(
                stats, 
                "Wallet statistics retrieved successfully"
        ));
    }
}
