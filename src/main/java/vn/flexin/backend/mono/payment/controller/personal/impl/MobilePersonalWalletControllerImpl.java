package vn.flexin.backend.mono.payment.controller.personal.impl;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.payment.controller.personal.MobilePersonalWalletController;
import vn.flexin.backend.mono.payment.dto.request.PaymentTransactionRequest;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.request.RedeemPointRequest;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalWalletResponse;
import vn.flexin.backend.mono.payment.service.personal.MobilePersonalWalletService;

import java.util.List;

@RestController
@AllArgsConstructor
public class MobilePersonalWalletControllerImpl implements MobilePersonalWalletController {

    private MobilePersonalWalletService walletService;

    @Override
    public ResponseEntity<ApiResponseDto<PersonalWalletResponse>> getWalletInfo() {
        PersonalWalletResponse wallet = walletService.getWallet();
        return ResponseEntity.ok(ApiResponseDto.success(wallet, "Wallet information retrieved successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> redeemPoints(RedeemPointRequest request) {
        walletService.redeemPoints(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Points redeemed successfully"));

    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> requestPayment(PaymentTransactionRequest request) {
        walletService.createPaymentRequest(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Payment request created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updatePaymentStatus(String thirdPartyTransactionId, String status) {
        walletService.updatePaymentStatus(thirdPartyTransactionId, status);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Payment status updated successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<PersonalTransactionResponse>>> getListTransaction(PersonalTransactionFilter request) {
        Pair<List<PersonalTransactionResponse>, PaginationResponse> result = walletService.getPersonalTransactions(request);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Get list transactions successfully", result.getLeft(), result.getRight()));
    }
}
