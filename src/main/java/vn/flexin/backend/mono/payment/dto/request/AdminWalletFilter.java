package vn.flexin.backend.mono.payment.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AdminWalletFilter extends BaseFilter<PersonalWallet> {

    private Long userId;
    private String userName;
    private String userEmail;
    private Long minPoints;
    private Long maxPoints;
    private LocalDateTime createdFrom;
    private LocalDateTime createdTo;
    private LocalDateTime lastModifiedFrom;
    private LocalDateTime lastModifiedTo;
    private List<String> createdBy;

    @Override
    public Specification<PersonalWallet> toSpecification() {
        var condition = new Condition();

        // Filter by user ID
        if (userId != null) {
            condition.append(new Join(PersonalWallet.Fields.user, List.of(new Where(User.Fields.id, userId))));
        }

        // Filter by user name (contains)
        if (userName != null && !userName.trim().isEmpty()) {
            condition.append(new Join(PersonalWallet.Fields.user, List.of(new Where(User.Fields.name, Operator.LIKE, "%" + userName.trim() + "%"))));
        }

        // Filter by user email (contains)
        if (userEmail != null && !userEmail.trim().isEmpty()) {
            condition.append(new Join(PersonalWallet.Fields.user, List.of(new Where(User.Fields.email, Operator.LIKE, "%" + userEmail.trim() + "%"))));
        }

        // Filter by point range
        if (minPoints != null) {
            condition.append(new Where(PersonalWallet.Fields.point, Operator.GREATER_THAN_OR_EQUAL, minPoints));
        }

        if (maxPoints != null) {
            condition.append(new Where(PersonalWallet.Fields.point, Operator.LESS_THAN_OR_EQUAL, maxPoints));
        }

        // Filter by creation date range
        if (createdFrom != null) {
            condition.append(new Where("createdAt", Operator.GREATER_THAN_OR_EQUAL, createdFrom));
        }

        if (createdTo != null) {
            condition.append(new Where("createdAt", Operator.LESS_THAN_OR_EQUAL, createdTo));
        }

        // Filter by last modified date range
        if (lastModifiedFrom != null) {
            condition.append(new Where("lastModifiedAt", Operator.GREATER_THAN_OR_EQUAL, lastModifiedFrom));
        }

        if (lastModifiedTo != null) {
            condition.append(new Where("lastModifiedAt", Operator.LESS_THAN_OR_EQUAL, lastModifiedTo));
        }

        // Filter by created by
        if (createdBy != null && !createdBy.isEmpty()) {
            condition.append(new Where("createdBy", Operator.IN, createdBy));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
