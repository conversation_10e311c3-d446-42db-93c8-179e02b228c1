package vn.flexin.backend.mono.payment.dto.response;

import lombok.Data;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AdminWalletResponse {
    private Long id;
    private BasicUserInfoResponse user;
    private Long point;
    private LocalDateTime createdAt;
    private LocalDateTime lastModifiedAt;
    private String createdBy;
    private String lastModifiedBy;
    
    // Thông tin thống kê chi tiết
    private WalletStatistics statistics;
    
    @Data
    public static class WalletStatistics {
        private Long totalTransactions;
        private Long totalPointsEarned;
        private Long totalPointsSpent;
        private Long totalPaymentRequests;
        private BigDecimal totalAmountPaid;
        private LocalDateTime lastTransactionDate;
        private LocalDateTime lastPaymentDate;
        private String mostUsedTransactionType;
        private Long pendingPaymentRequests;
        private Long successfulPaymentRequests;
        private Long failedPaymentRequests;
    }

    public AdminWalletResponse(PersonalWallet wallet) {
        this.id = wallet.getId();
        this.user = new BasicUserInfoResponse(wallet.getUser());
        this.point = wallet.getPoint();
        this.createdAt = wallet.getCreatedAt();
        this.lastModifiedAt = wallet.getLastModifiedAt();
        this.createdBy = wallet.getCreatedBy();
        this.lastModifiedBy = wallet.getLastModifiedBy();
        this.statistics = new WalletStatistics();
    }
}
