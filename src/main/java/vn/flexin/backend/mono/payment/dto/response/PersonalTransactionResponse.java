package vn.flexin.backend.mono.payment.dto.response;

import lombok.Data;
import vn.flexin.backend.mono.payment.entity.personal.PersonalTransaction;
import vn.flexin.backend.mono.payment.enums.TransactionStatus;
import vn.flexin.backend.mono.payment.enums.TransactionType;

@Data
public class PersonalTransactionResponse {
    private Long id;
    private PersonalWalletResponse wallet;
    private Long point;
    private TransactionType type;

    private TransactionStatus status;

    public PersonalTransactionResponse(PersonalTransaction transaction) {
        this.id = transaction.getId();
        this.wallet = new PersonalWalletResponse(transaction.getWallet());
        this.point = transaction.getPoint();
        this.type = transaction.getType();
        this.status = transaction.getStatus();
    }
}
