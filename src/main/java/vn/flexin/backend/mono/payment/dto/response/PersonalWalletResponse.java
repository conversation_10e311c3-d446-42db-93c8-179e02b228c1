package vn.flexin.backend.mono.payment.dto.response;

import lombok.Data;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

@Data
public class PersonalWalletResponse {
    private Long id;
    private BasicUserInfoResponse user;
    private Long point;

    public PersonalWalletResponse (PersonalWallet wallet) {
        this.id = wallet.getId();
        this.user = new BasicUserInfoResponse(wallet.getUser());
        this.point = wallet.getPoint();
    }
}