package vn.flexin.backend.mono.payment.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum WalletPermissions {
    WALLET_READ("wallet_read"),
    WALLET_UPDATE("wallet_update"),
    WALLET_STATISTICS("wallet_statistics");

    private final String value;

    WalletPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static WalletPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Wallet permissions type must be any of [" + getValues() + "]");
        }
    }
}
