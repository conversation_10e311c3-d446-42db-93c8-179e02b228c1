package vn.flexin.backend.mono.payment.repository.personal;

import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.payment.entity.personal.PersonalPaymentRequest;

import java.util.Optional;

@Repository
public interface PersonalPaymentRequestRepository extends JpaSpecificationRepository<PersonalPaymentRequest, Long> {
    Optional<PersonalPaymentRequest> findByThirdPartyTransactionId(String thirdPartyTransactionId);

}
