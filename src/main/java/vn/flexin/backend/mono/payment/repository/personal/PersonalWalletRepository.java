package vn.flexin.backend.mono.payment.repository.personal;

import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;

import java.util.Optional;
@Repository
public interface PersonalWalletRepository extends JpaSpecificationRepository<PersonalWallet, Long> {

    Optional<PersonalWallet> findByUser_Id(Long userId);
}
