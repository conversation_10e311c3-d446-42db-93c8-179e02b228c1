package vn.flexin.backend.mono.payment.service.admin;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.payment.dto.request.AdminWalletFilter;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.response.AdminWalletResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;

import java.util.List;

/**
 * Admin service interface for managing wallets
 * Provides administrative operations for wallet management
 */
public interface AdminWalletService {

    /**
     * Search wallets with pagination and filtering for admin
     * @param filter Wallet filter criteria
     * @return Pair of wallet list and pagination response
     */
    Pair<List<AdminWalletResponse>, PaginationResponse> searchWalletsForAdmin(AdminWalletFilter filter);

    /**
     * Get wallet detail by ID for admin view
     * @param walletId Wallet ID
     * @return Admin wallet response with detailed statistics
     */
    AdminWalletResponse getWalletDetailForAdmin(Long walletId);

    /**
     * Get wallet detail by user ID for admin view
     * @param userId User ID
     * @return Admin wallet response with detailed statistics
     */
    AdminWalletResponse getWalletByUserIdForAdmin(Long userId);

    /**
     * Get wallet transactions for admin
     * @param walletId Wallet ID
     * @param filter Transaction filter criteria
     * @return Pair of transaction list and pagination response
     */
    Pair<List<PersonalTransactionResponse>, PaginationResponse> getWalletTransactionsForAdmin(Long walletId, PersonalTransactionFilter filter);

    /**
     * Adjust wallet points by admin
     * @param walletId Wallet ID
     * @param points Points to add (positive) or subtract (negative)
     * @param reason Reason for adjustment
     * @return Updated wallet response
     */
    AdminWalletResponse adjustWalletPoints(Long walletId, Long points, String reason);

    /**
     * Get wallet statistics summary for admin dashboard
     * @return Wallet statistics summary
     */
    AdminWalletResponse.WalletStatistics getWalletStatisticsSummary();
}
