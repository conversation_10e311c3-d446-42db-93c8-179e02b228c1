package vn.flexin.backend.mono.payment.service.admin.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.payment.dto.request.AdminWalletFilter;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.response.AdminWalletResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.entity.personal.PersonalPaymentRequest;
import vn.flexin.backend.mono.payment.entity.personal.PersonalTransaction;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.payment.enums.TransactionStatus;
import vn.flexin.backend.mono.payment.enums.TransactionType;
import vn.flexin.backend.mono.payment.repository.personal.PersonalPaymentRequestRepository;
import vn.flexin.backend.mono.payment.repository.personal.PersonalTransactionRepository;
import vn.flexin.backend.mono.payment.repository.personal.PersonalWalletRepository;
import vn.flexin.backend.mono.payment.service.admin.AdminWalletService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Admin service implementation for managing wallets
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AdminWalletServiceImpl implements AdminWalletService {

    private final PersonalWalletRepository walletRepository;
    private final PersonalTransactionRepository transactionRepository;
    private final PersonalPaymentRequestRepository paymentRequestRepository;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<AdminWalletResponse>, PaginationResponse> searchWalletsForAdmin(AdminWalletFilter filter) {
        log.info("Searching wallets for admin with filter: {}", filter);
        
        Page<PersonalWallet> wallets = walletRepository.findAll(filter);
        List<AdminWalletResponse> adminWalletResponses = wallets.getContent()
                .stream()
                .map(this::toAdminWalletResponse)
                .toList();
        
        PaginationResponse paging = new PaginationResponse(
                filter.getLimit(), 
                filter.getPage(), 
                (int) wallets.getTotalElements()
        );
        
        return Pair.of(adminWalletResponses, paging);
    }

    @Override
    @Transactional(readOnly = true)
    public AdminWalletResponse getWalletDetailForAdmin(Long walletId) {
        log.info("Getting wallet detail for admin - ID: {}", walletId);
        
        PersonalWallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new ResourceNotFoundException("Wallet not found with ID: " + walletId));
        
        return toAdminWalletResponseWithStatistics(wallet);
    }

    @Override
    @Transactional(readOnly = true)
    public AdminWalletResponse getWalletByUserIdForAdmin(Long userId) {
        log.info("Getting wallet by user ID for admin - User ID: {}", userId);
        
        PersonalWallet wallet = walletRepository.findByUser_Id(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Wallet not found for user ID: " + userId));
        
        return toAdminWalletResponseWithStatistics(wallet);
    }

    @Override
    @Transactional(readOnly = true)
    public Pair<List<PersonalTransactionResponse>, PaginationResponse> getWalletTransactionsForAdmin(Long walletId, PersonalTransactionFilter filter) {
        log.info("Getting wallet transactions for admin - Wallet ID: {}", walletId);
        
        // Verify wallet exists
        walletRepository.findById(walletId)
                .orElseThrow(() -> new ResourceNotFoundException("Wallet not found with ID: " + walletId));
        
        filter.setWalletId(walletId);
        Page<PersonalTransaction> transactions = transactionRepository.findAll(filter);
        List<PersonalTransactionResponse> responses = transactions.getContent()
                .stream()
                .map(PersonalTransactionResponse::new)
                .toList();
        
        PaginationResponse paging = new PaginationResponse(
                filter.getLimit(), 
                filter.getPage(), 
                (int) transactions.getTotalElements()
        );
        
        return Pair.of(responses, paging);
    }

    @Override
    @Transactional
    public AdminWalletResponse adjustWalletPoints(Long walletId, Long points, String reason) {
        log.info("Adjusting wallet points - Wallet ID: {}, Points: {}, Reason: {}", walletId, points, reason);
        
        PersonalWallet wallet = walletRepository.findById(walletId)
                .orElseThrow(() -> new ResourceNotFoundException("Wallet not found with ID: " + walletId));
        
        Long oldPoints = wallet.getPoint();
        wallet.setPoint(oldPoints + points);
        
        PersonalWallet savedWallet = walletRepository.save(wallet);
        
        // Create transaction record
        createAdminAdjustmentTransaction(savedWallet, points, reason);
        
        log.info("Wallet points adjusted successfully - Old: {}, New: {}", oldPoints, savedWallet.getPoint());
        
        return toAdminWalletResponseWithStatistics(savedWallet);
    }

    @Override
    @Transactional(readOnly = true)
    public AdminWalletResponse.WalletStatistics getWalletStatisticsSummary() {
        log.info("Getting wallet statistics summary for admin dashboard");
        
        AdminWalletResponse.WalletStatistics stats = new AdminWalletResponse.WalletStatistics();

        // Get transaction statistics
        List<PersonalTransaction> allTransactions = transactionRepository.findAll();
        stats.setTotalTransactions((long) allTransactions.size());
        
        // Calculate points statistics
        Long totalPointsEarned = allTransactions.stream()
                .filter(t -> isEarnTransaction(t.getType()))
                .mapToLong(PersonalTransaction::getPoint)
                .sum();
        stats.setTotalPointsEarned(totalPointsEarned);
        
        Long totalPointsSpent = allTransactions.stream()
                .filter(t -> isSpendTransaction(t.getType()))
                .mapToLong(PersonalTransaction::getPoint)
                .sum();
        stats.setTotalPointsSpent(totalPointsSpent);
        
        // Get payment request statistics
        List<PersonalPaymentRequest> allPaymentRequests = paymentRequestRepository.findAll();
        stats.setTotalPaymentRequests((long) allPaymentRequests.size());
        
        BigDecimal totalAmountPaid = allPaymentRequests.stream()
                .filter(p -> p.getStatus() == TransactionStatus.SUCCESS)
                .map(PersonalPaymentRequest::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalAmountPaid(totalAmountPaid);
        
        // Get status counts
        Map<TransactionStatus, Long> statusCounts = allPaymentRequests.stream()
                .collect(Collectors.groupingBy(PersonalPaymentRequest::getStatus, Collectors.counting()));
        
        stats.setPendingPaymentRequests(statusCounts.getOrDefault(TransactionStatus.IN_PROGRESS, 0L));
        stats.setSuccessfulPaymentRequests(statusCounts.getOrDefault(TransactionStatus.SUCCESS, 0L));
        stats.setFailedPaymentRequests(statusCounts.getOrDefault(TransactionStatus.FAILED, 0L));
        
        // Get most used transaction type
        Map<TransactionType, Long> typeCounts = allTransactions.stream()
                .collect(Collectors.groupingBy(PersonalTransaction::getType, Collectors.counting()));
        
        String mostUsedType = typeCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> entry.getKey().toString())
                .orElse("N/A");
        stats.setMostUsedTransactionType(mostUsedType);
        
        // Get last transaction and payment dates
        allTransactions.stream()
                .map(PersonalTransaction::getCreatedAt)
                .max(LocalDateTime::compareTo)
                .ifPresent(stats::setLastTransactionDate);
        
        allPaymentRequests.stream()
                .map(PersonalPaymentRequest::getCreatedAt)
                .max(LocalDateTime::compareTo)
                .ifPresent(stats::setLastPaymentDate);
        
        return stats;
    }

    private AdminWalletResponse toAdminWalletResponse(PersonalWallet wallet) {
        return new AdminWalletResponse(wallet);
    }

    private AdminWalletResponse toAdminWalletResponseWithStatistics(PersonalWallet wallet) {
        AdminWalletResponse response = new AdminWalletResponse(wallet);
        response.setStatistics(calculateWalletStatistics(wallet));
        return response;
    }

    private AdminWalletResponse.WalletStatistics calculateWalletStatistics(PersonalWallet wallet) {
        AdminWalletResponse.WalletStatistics stats = new AdminWalletResponse.WalletStatistics();

        // Get transactions for this wallet
        PersonalTransactionFilter filter = new PersonalTransactionFilter();
        filter.setWalletId(wallet.getId());
        Page<PersonalTransaction> transactionPage = transactionRepository.findAll(filter.toSpecification(), filter.toPageable());
        List<PersonalTransaction> transactions = transactionPage.getContent();
        
        stats.setTotalTransactions((long) transactions.size());
        
        // Calculate points earned and spent
        Long pointsEarned = transactions.stream()
                .filter(t -> isEarnTransaction(t.getType()))
                .mapToLong(PersonalTransaction::getPoint)
                .sum();
        stats.setTotalPointsEarned(pointsEarned);
        
        Long pointsSpent = transactions.stream()
                .filter(t -> isSpendTransaction(t.getType()))
                .mapToLong(PersonalTransaction::getPoint)
                .sum();
        stats.setTotalPointsSpent(pointsSpent);
        
        // Get payment requests for this wallet
        // Note: You might need to add a method to find payment requests by wallet
        // For now, we'll set basic values
        stats.setTotalPaymentRequests(0L);
        stats.setTotalAmountPaid(BigDecimal.ZERO);
        stats.setPendingPaymentRequests(0L);
        stats.setSuccessfulPaymentRequests(0L);
        stats.setFailedPaymentRequests(0L);
        
        // Get last transaction date
        transactions.stream()
                .map(PersonalTransaction::getCreatedAt)
                .max(LocalDateTime::compareTo)
                .ifPresent(stats::setLastTransactionDate);
        
        // Get most used transaction type
        Map<TransactionType, Long> typeCounts = transactions.stream()
                .collect(Collectors.groupingBy(PersonalTransaction::getType, Collectors.counting()));
        
        String mostUsedType = typeCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> entry.getKey().toString())
                .orElse("N/A");
        stats.setMostUsedTransactionType(mostUsedType);
        
        return stats;
    }

    private void createAdminAdjustmentTransaction(PersonalWallet wallet, Long points, String reason) {
        PersonalTransaction transaction = new PersonalTransaction();
        transaction.setWallet(wallet);
        transaction.setPoint(Math.abs(points));
        transaction.setType(points > 0 ? TransactionType.ADMIN_ADJUSTMENT_ADD : TransactionType.ADMIN_ADJUSTMENT_SUBTRACT);
        transaction.setStatus(TransactionStatus.SUCCESS);
        
        transactionRepository.save(transaction);
    }

    private boolean isEarnTransaction(TransactionType type) {
        return type == TransactionType.ADD_MONEY_TO_WALLET || 
               type == TransactionType.ADMIN_ADJUSTMENT_ADD ||
               type == TransactionType.REWARD;
    }

    private boolean isSpendTransaction(TransactionType type) {
        return type == TransactionType.REDEEM_POINTS || 
               type == TransactionType.ADMIN_ADJUSTMENT_SUBTRACT ||
               type == TransactionType.PURCHASE;
    }
}
