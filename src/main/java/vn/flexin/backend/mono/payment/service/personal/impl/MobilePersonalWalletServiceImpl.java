package vn.flexin.backend.mono.payment.service.personal.impl;

import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.payment.dto.request.PaymentTransactionRequest;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.request.RedeemPointRequest;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalWalletResponse;
import vn.flexin.backend.mono.payment.entity.personal.PersonalPaymentRequest;
import vn.flexin.backend.mono.payment.entity.personal.PersonalTransaction;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.payment.enums.TransactionStatus;
import vn.flexin.backend.mono.payment.enums.TransactionType;
import vn.flexin.backend.mono.payment.repository.personal.PersonalPaymentRequestRepository;
import vn.flexin.backend.mono.payment.repository.personal.PersonalTransactionRepository;
import vn.flexin.backend.mono.payment.repository.personal.PersonalWalletRepository;
import vn.flexin.backend.mono.payment.service.personal.MobilePersonalWalletService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.math.BigDecimal;
import java.util.List;

@Service
@AllArgsConstructor
public class MobilePersonalWalletServiceImpl implements MobilePersonalWalletService {

    private final UserService userService;

    private PersonalWalletRepository walletRepository;
    private PersonalTransactionRepository transactionRepository;
    private PersonalPaymentRequestRepository paymentRequestRepository;

    public PersonalWallet getWallet(Long userId) {
        return walletRepository.findByUser_Id(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Wallet not found for user: " + userId));
    }

    @Override
    public PersonalWalletResponse getWallet() {
        User currentUser = userService.getCurrentLoginUser();
        PersonalWallet userWallet = walletRepository.findByUser_Id(currentUser.getId()).orElse(null);
        if (userWallet == null) {
            userWallet = createWalletForUser(currentUser);
        }
        return new PersonalWalletResponse(userWallet);
    }

    private PersonalWallet createWalletForUser(User user) {
        PersonalWallet wallet = new PersonalWallet();
        wallet.setUser(user);
        wallet.setPoint(500L);

        return walletRepository.save(wallet);
    }

    @Override
    @Transactional
    public void redeemPoints(RedeemPointRequest request) {
        User currentUser = userService.getCurrentLoginUser();

        PersonalWallet wallet = getWallet(currentUser.getId());
        if (wallet.getPoint() < request.getPoint()) {
            throw new BadRequestException("Not enough points in wallet");
        }

        wallet.setPoint(wallet.getPoint() - request.getPoint());
        walletRepository.save(wallet);

        createTransaction(wallet, request.getPoint(), request.getType());
    }

    @Override
    @Transactional
    public void createPaymentRequest(PaymentTransactionRequest dto) {
        User currentUser = userService.getCurrentLoginUser();

        PersonalWallet wallet = getWallet(currentUser.getId());

        PersonalPaymentRequest request = new PersonalPaymentRequest();
        request.setWallet(wallet);
        request.setAmount(dto.getAmount());
        request.setStatus(TransactionStatus.IN_PROGRESS);
        //TODO implement logic call to third party system to execute transaction
        request.setThirdPartyTransactionId("GENERATED_ID_" + System.currentTimeMillis());
        paymentRequestRepository.save(request);
        // temporary flow fake data add point to wallet

        Long pointsToAdd = request.getAmount().divide(BigDecimal.valueOf(1000)).longValue();
        wallet.setPoint(wallet.getPoint() + pointsToAdd);
        walletRepository.save(wallet);

        request.setStatus(TransactionStatus.SUCCESS);
        paymentRequestRepository.save(request);
        createTransaction(wallet, pointsToAdd, TransactionType.ADD_MONEY_TO_WALLET);

    }

    @Override
    @Transactional
    public void updatePaymentStatus(String thirdPartyTransactionId, String status) {
        PersonalPaymentRequest request = paymentRequestRepository.findByThirdPartyTransactionId(thirdPartyTransactionId)
                .orElseThrow(() -> new RuntimeException("Payment request not found"));

        request.setStatus(TransactionStatus.fromUpdatePaymentStatus(status));
        paymentRequestRepository.save(request);

        if ("SUCCESS".equals(status)) {
            PersonalWallet wallet = request.getWallet();
            // Assume 1 unit of currency = 100 points
            try {
                Long pointsToAdd = request.getAmount().divide(BigDecimal.valueOf(1000)).longValue();
                wallet.setPoint(wallet.getPoint() + pointsToAdd);
                walletRepository.save(wallet);

                createTransaction(wallet, pointsToAdd, TransactionType.ADD_MONEY_TO_WALLET);
            } catch (Exception e) {
                throw new  BadRequestException("");
            }
        }
    }

    private void createTransaction(PersonalWallet wallet, Long point, TransactionType type) {
        PersonalTransaction transaction = new PersonalTransaction();
        transaction.setWallet(wallet);
        transaction.setPoint(point);
        transaction.setType(type);
        transaction.setStatus(TransactionStatus.SUCCESS);
        transactionRepository.save(transaction);
    }

    @Override
    public Pair<List<PersonalTransactionResponse>, PaginationResponse> getPersonalTransactions(PersonalTransactionFilter request) {
        User currentUser = userService.getCurrentLoginUser();

        PersonalWallet wallet = getWallet(currentUser.getId());
        request.setWalletId(wallet.getId());
        Page<PersonalTransaction> transactions = transactionRepository.findAll(request);
        List<PersonalTransactionResponse> responses = transactions.getContent().stream().map(PersonalTransactionResponse::new).toList();
        PaginationResponse paginationResponse = new PaginationResponse(request.getLimit(), request.getPage(), transactions.getNumberOfElements());

        return Pair.of(responses, paginationResponse);
    }
}
