package vn.flexin.backend.mono.permission.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.permission.dto.PermissionFilter;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;

import java.util.List;

@RequestMapping("/v1/admin/permissions")
@Tag(name = "Admin Permissions Mapping APIs", description = "Manage mapping roles and permissions")
public interface AdminPermissionController {
    @Operation(summary = "Get all permissions", description = "Get a list of all permissions")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved permissions"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('permission_read')")
    ResponseEntity<PaginationApiResponseDto<List<PermissionResponse>>> searchPermissions(@RequestBody PermissionFilter permissionFilter);

    @Operation(summary = "Get permission by ID", description = "Get a permission by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved permission"),
            @ApiResponse(responseCode = "404", description = "Permission not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('permission_read')")
    ResponseEntity<ApiResponseDto<PermissionResponse>> getPermissionById(@PathVariable Long id);
}
