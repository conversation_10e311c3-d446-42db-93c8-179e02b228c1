package vn.flexin.backend.mono.permission.repository;

import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.permission.entity.Permission;

import java.util.Optional;

public interface PermissionRepository extends JpaSpecificationRepository<Permission, Long> {

    boolean existsByName(String name);

    Optional<Permission> findByName(String name);
}
