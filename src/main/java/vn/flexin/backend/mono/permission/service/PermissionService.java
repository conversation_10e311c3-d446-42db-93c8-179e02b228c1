package vn.flexin.backend.mono.permission.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.permission.dto.PermissionFilter;
import vn.flexin.backend.mono.permission.dto.request.CreatePermissionRequest;
import vn.flexin.backend.mono.permission.dto.request.UpdatePermissionRequest;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;
import vn.flexin.backend.mono.permission.entity.Permission;

import java.util.List;

public interface PermissionService {

    /**
     * Tạo một permission mới
     * @param request Thông tin permission cần tạo
     * @return PermissionResponse của permission đã tạo
     */
    CreateObjectResponse createPermission(CreatePermissionRequest request);

    /**
     * <PERSON><PERSON><PERSON> nhật thông tin của một permission
     * @param request Thông tin mới của permission
     * @return PermissionResponse của permission đã cập nhật
     */
    void updatePermission(UpdatePermissionRequest request);

    /**
     * Xóa một permission
     * @param id ID của permission cần xóa
     */
    void deletePermission(Long id);

    void deletePermissions(List<Long> ids);

    /**
     * Lấy danh sách tất cả permission
     * @return Danh sách PermissionResponse
     */
    List<PermissionResponse> getAllPermissions();

    /**
     * Tìm kiếm permission theo từ khóa và phân trang
     * @param baseFilter  keyword Từ khóa tìm kiếm
     * @return Page<PermissionResponse> Kết quả tìm kiếm đã phân trang
     */
    Pair<List<PermissionResponse>, PaginationResponse> searchPermissions(PermissionFilter baseFilter);

    /**
     * Lấy chi tiết của một permission
     * @param id ID của permission
     * @return PermissionResponse chi tiết của permission
     */
    PermissionResponse getPermissionDetail(Long id);

    /**
     * Kiểm tra xem permission có đang được sử dụng bởi bất kỳ role nào không
     * @param permissionId ID của permission cần kiểm tra
     * @return true nếu permission đang được sử dụng, ngược lại false
     */
    boolean isPermissionUsedByAnyRole(Long permissionId);

    /**
     * Lấy danh sách permission của một role cụ thể
     * @param roleId ID của role
     * @return Danh sách PermissionResponse của role
     */
    List<PermissionResponse> getPermissionsByRoleId(Long roleId);

    Permission findById(Long id);

}
