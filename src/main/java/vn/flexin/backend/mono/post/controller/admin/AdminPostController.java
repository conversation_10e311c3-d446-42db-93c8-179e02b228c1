package vn.flexin.backend.mono.post.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostFilter;
import vn.flexin.backend.mono.post.dto.request.employer.*;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchAdminPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticAdminPostResponse;

import java.util.List;

@Tag(name = "Admin Posts APIs", description = "Admin Post Management")
@RequestMapping("/v1/admin/posts")
public interface AdminPostController {
    @Operation(summary = "Search list posts of admin", description = "Retrieves a paginated list of job posts with " +
            "filtering options.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    ResponseEntity<PaginationApiResponseDto<List<SearchAdminPostResponse>>> searchPostForAdmin(@Valid @RequestBody PostFilter filters);

    @Operation(summary = "Get detail post", description = "Get detail post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @GetMapping("/{id}")
    ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> getDetailPost(@PathVariable("id") Long id);

    @Operation(summary = "Creates a new job post", description = "Creates a new job post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid post data")
    })
    @PostMapping
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(@RequestBody CreateAdminPostRequest request);

    @Operation(summary = "Update post", description = "Update post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @PutMapping("/{id}")
    ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> updatePost(@PathVariable("id") Long id,
                                                    @RequestBody UpdateAdminPostRequest request);

    @Operation(summary = "Delete post", description = "Delete post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post Delete successfully"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> deletePost(@PathVariable("id") Long id);

    @Operation(summary = "Approves a post that is in draft status.", description = "Approves a post that is in draft status.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post approved successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @PatchMapping("/{id}/approve")
    ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> approvePost(@PathVariable("id") Long id);

    @Operation(summary = "Rejects a post with a reason.", description = "Rejects a post with a reason.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post rejected successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @PatchMapping("/{id}/reject")
    ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> rejectPost(@PathVariable("id") Long id,
                                                                    @RequestBody RejectPostRequest request);

    @Operation(summary = "Retrieves statistics about posts for dashboard display.", description = "Retrieves statistics about posts for dashboard display.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin post statistics fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @GetMapping("/statistics")
    ResponseEntity<ApiResponseDto<StatisticAdminPostResponse>> getStatisticsPost();

    @Operation(summary = "Search post application", description = "Search post applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/application/search")
    ResponseEntity<PaginationApiResponseDto<List<PostApplicationResponse>>> searchPostApplications(@Valid @RequestBody PostApplicationFilter filters);

    @Operation(summary = "Get post application by post id", description = "Get post applications by post id")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{postId}/application")
    ResponseEntity<ApiResponseDto<List<PostApplicationResponse>>> getPostApplicationsByPostId(@PathVariable("postId") Long postId);

    
    @Operation(summary = "Create post application", description = "Create post applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create post application successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{postId}/application")
    ResponseEntity<ApiResponseDto<Long>> createPostApplication(@PathVariable("postId") Long postId, @RequestBody CreatePostApplicationRequest request);

    @Operation(summary = "Update post application", description = "Update post applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Update post application successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PutMapping("/application/{applicationId}")
    ResponseEntity<ApiResponseDto<Boolean>> updatePostApplication(@PathVariable("applicationId") Long applicationId,
                                                                                            @RequestBody UpdatePostApplicationRequest request);

    @Operation(summary = "Delete post application", description = "Delete post applications")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Delete post application successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @DeleteMapping("/application/{applicationId}")
    ResponseEntity<ApiResponseDto<Boolean>> deletePostApplication(@PathVariable("applicationId") Long applicationId);

    @Operation(summary = "Matching all posts", description = "Matching all posts")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Matching done successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/matching/all")
    ResponseEntity<ApiResponseDto<Boolean>> matchingPosts();
}
