package vn.flexin.backend.mono.post.controller.admin.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.controller.admin.AdminJobSeekerPostController;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobOpportunityResponse;
import vn.flexin.backend.mono.post.service.admin.AdminJobSeekerPostService;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerDetailResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.SearchJobSeekerPostResponse;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class AdminJobSeekerPostControllerImpl implements AdminJobSeekerPostController {

    private final AdminJobSeekerPostService adminJobSeekerPostService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchJobSeekerPostResponse>>> searchPosts(JobSeekerPostFilter filters) {
        var data = adminJobSeekerPostService.searchJobSeekerPost(filters);
        // Pagination info is not available in admin service, so just wrap as list for now
        return ResponseEntity.ok(PaginationApiResponseDto.success("Fetch Job seeker post successfully", data, null));
    }

    @Override
    public ResponseEntity<ApiResponseDto<JobSeekerDetailResponse>> getDetailPost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(adminJobSeekerPostService.getDetailPost(id), "Get detail Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(CreateJobSeekerPostRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(adminJobSeekerPostService.createPost(request), "Created Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updatePost(Long id, UpdateJobSeekerPostRequest request) {
        request.setId(id);
        adminJobSeekerPostService.updatePost(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Update Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deletePost(Long id) {
        adminJobSeekerPostService.deletePost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Delete Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<JobSeekerStatisticPostResponse>> getStatistics() {
        return ResponseEntity.ok(ApiResponseDto.success(adminJobSeekerPostService.getStatistics(), "Statistics fetched successfully"));
    }
} 