package vn.flexin.backend.mono.post.controller.admin.impl;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.controller.admin.AdminPostController;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostFilter;
import vn.flexin.backend.mono.post.dto.request.employer.*;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchAdminPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticAdminPostResponse;
import vn.flexin.backend.mono.post.service.admin.AdminPostApplicationService;
import vn.flexin.backend.mono.post.service.admin.AdminPostService;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

@RestController
@AllArgsConstructor
public class AdminPostControllerImpl implements AdminPostController {

    private final AdminPostService postService;
    private final AdminPostApplicationService adminPostApplicationService;

    @Override
    @PreAuthorize("hasAuthority('post_read')")
    public ResponseEntity<PaginationApiResponseDto<List<SearchAdminPostResponse>>> searchPostForAdmin(PostFilter request) {
        var result = postService.searchPostForAdmin(request);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Post retrieved successfully", result.getFirst(),
                result.getSecond()));
    }

    @Override
    @PreAuthorize("hasAuthority('post_read')")
    public ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> getDetailPost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.getDetailPost(id),"Post fetched successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_create')")
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(CreateAdminPostRequest request) {
        Long id = postService.createAdminPost(request);
        return ResponseEntity.ok(ApiResponseDto.success(new CreateObjectResponse(id),"Post created successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_update')")
    public ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> updatePost(Long id, UpdateAdminPostRequest request) {
        request.setId(id);
        return ResponseEntity.ok(ApiResponseDto.success(postService.updateAdminPost(request),"Post updated successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_delete')")
    public ResponseEntity<ApiResponseDto<Boolean>> deletePost(Long id) {
        postService.deletePost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Post deleted successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_approve')")
    public ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> approvePost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.approvePost(id),"Post approved successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_reject')")
    public ResponseEntity<ApiResponseDto<SearchAdminPostResponse>> rejectPost(Long id, RejectPostRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.rejectPost(id, request),"Post reject " +
                "successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_statistics')")
    public ResponseEntity<ApiResponseDto<StatisticAdminPostResponse>> getStatisticsPost() {
        return ResponseEntity.ok(ApiResponseDto.success(postService.getStatisticsPost(),"Post statistics successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_application_read')")
    public ResponseEntity<PaginationApiResponseDto<List<PostApplicationResponse>>> searchPostApplications(PostApplicationFilter filters) {
        var result = adminPostApplicationService.searchPostApplications(filters);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Post application retrieved successfully", result.getFirst(),
                result.getSecond()));
    }

    @Override
    @PreAuthorize("hasAuthority('post_application_update')")
    public ResponseEntity<ApiResponseDto<Boolean>> updatePostApplication(Long applicationId, UpdatePostApplicationRequest request) {
        adminPostApplicationService.updatePostApplication(applicationId, request.getResumeId());
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Post application updated successfully"));
    }

    @Override
    @PreAuthorize("hasAuthority('post_application_delete')")
    public ResponseEntity<ApiResponseDto<Boolean>> deletePostApplication(Long applicationId) {
        adminPostApplicationService.deletePostApplication(applicationId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Post application deleted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Long>> createPostApplication(Long postId, CreatePostApplicationRequest request) {
        Long id = adminPostApplicationService.createPostApplication(postId, request.getResumeId());
        return ResponseEntity.ok(ApiResponseDto.success(id,"Post application created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<PostApplicationResponse>>> getPostApplicationsByPostId(Long postId) {
        List<PostApplicationResponse> applications = adminPostApplicationService.getPostApplicationsByPostId(postId);
        return ResponseEntity.ok(ApiResponseDto.success(applications,"Post applications fetched successfully"));
    }

}
