package vn.flexin.backend.mono.post.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostInterestFilter;
import vn.flexin.backend.mono.post.dto.filters.SearchPostFilter;
import vn.flexin.backend.mono.post.dto.request.CreatePostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.CreatePostApplicationRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostApplicationRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostRequest;
import vn.flexin.backend.mono.post.dto.response.employer.DetailPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticPostResponse;

import java.util.List;

@Tag(name = "Mobile Posts APIs", description = "Mobile post endpoints")
@RequestMapping("/v1/mobile/employer-posts")
public interface MobilePostController {

    @Operation(summary = "Search list posts of employer", description = "Search list posts of employer")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer posts fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> searchPostForEmployer(@Valid @RequestBody SearchPostFilter filters);

    @Operation(summary = "Create new post", description = "Create new post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(@RequestBody CreatePostRequest request);

    @Operation(summary = "Update post", description = "Update post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PutMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> updatePost(@PathVariable("id") Long id,
                                                    @RequestBody UpdatePostRequest request);

    @Operation(summary = "Get detail post", description = "Get detail post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{id}")
    ResponseEntity<ApiResponseDto<DetailPostResponse>> getDetailPost(@PathVariable("id") Long id);

    @Operation(summary = "Delete post", description = "Delete post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponseDto<Boolean>> deletePost(@PathVariable("id") Long id);

    @Operation(summary = "Toggle post", description = "Toggle post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post toggled successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/toggle-featured")
    ResponseEntity<ApiResponseDto<Boolean>> togglePost(@PathVariable("id") Long id);

    @Operation(summary = "Close post", description = "Close post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post closed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/close")
    ResponseEntity<ApiResponseDto<Boolean>> closePost(@PathVariable("id") Long id);

    @Operation(summary = "Reopen post", description = "Reopen post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post reopen successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/reopen")
    ResponseEntity<ApiResponseDto<Boolean>> reopenPost(@PathVariable("id") Long id);

    @Operation(summary = "Duplicate post", description = "Duplicate post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post duplicate successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @PostMapping("/{id}/duplicate")
    ResponseEntity<ApiResponseDto<DetailPostResponse>> duplicatePost(@PathVariable("id") Long id);

    @Operation(summary = "Get post statistic", description = "Get post statistic")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Employer post statistics fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{id}/statistics")
    ResponseEntity<ApiResponseDto<StatisticPostResponse>> getPostStatistic(@PathVariable("id") Long id);

    @Operation(summary = "Mark post as interested", description = "User marks a specific post as interested")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post marked as interested successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/{id}/interest")
    ResponseEntity<ApiResponseDto<Boolean>> createPostInterest(@PathVariable("id") Long id);

    @Operation(summary = "Delete post interest", description = "User removes their interest from a specific post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post interest removed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @DeleteMapping("/{id}/interest")
    ResponseEntity<ApiResponseDto<Boolean>> deletePostInterest(@PathVariable("id") Long id);

    @Operation(summary = "Get interested posts", description = "Get interested posts of an user.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Interested posts fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/interested")
    ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> getInterestedPosts(@Valid @RequestBody PostInterestFilter filters);

    @Operation(summary = "Create a post application", description = "Job seeker apply for a post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Apply successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/{id}/application")
    ResponseEntity<ApiResponseDto<Boolean>> createPostApplication(@PathVariable("id") Long id, @Valid @RequestBody CreatePostApplicationRequest request);

    @Operation(summary = "Update a post application", description = "Job seeker update a post application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Update successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PutMapping("/application/{applicationId}")
    ResponseEntity<ApiResponseDto<Boolean>> updatePostApplication(@PathVariable("applicationId") Long applicationId, @Valid @RequestBody UpdatePostApplicationRequest request);

    @Operation(summary = "Delete post application", description = "User removes their application from a specific post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post application removed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @DeleteMapping("/application/{applicationId}")
    ResponseEntity<ApiResponseDto<Boolean>> deletePostApplication(@PathVariable("applicationId") Long id);

    @Operation(summary = "Get applied posts", description = "Get applied posts of an user.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Posts fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/appliedPosts")
    ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> getAppliedPosts(@Valid @RequestBody PostApplicationFilter filters);

    @Operation(summary = "Get post application", description = "Get applications of a post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Application fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Not found"),
    })
    @GetMapping("/{id}/application")
    ResponseEntity<PaginationApiResponseDto<List<PostApplicationResponse>>> getPostApplications(@PathVariable("id") Long id, @Valid @RequestBody PostApplicationFilter filters);

    @Operation(summary = "Approve a post application", description = "Employer approve a post application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Approve successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/application/{applicationId}/approve")
    ResponseEntity<ApiResponseDto<Boolean>> approvePostApplication(@PathVariable("applicationId") Long applicationId);


    @Operation(summary = "Reject a post application", description = "Employer reject a post application")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Reject successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @DeleteMapping("/application/{applicationId}/reject")
    ResponseEntity<ApiResponseDto<Boolean>> rejectPostApplication(@PathVariable("applicationId") Long applicationId);

}
