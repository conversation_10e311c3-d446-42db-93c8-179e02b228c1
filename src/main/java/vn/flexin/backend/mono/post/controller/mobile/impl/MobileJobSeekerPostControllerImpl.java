package vn.flexin.backend.mono.post.controller.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.controller.mobile.MobileJobSeekerPostController;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.*;
import vn.flexin.backend.mono.post.service.mobile.impl.JobSeekerPostServiceImpl;

import java.util.List;

@Service
@RestController
@AllArgsConstructor
public class MobileJobSeekerPostControllerImpl implements MobileJobSeekerPostController {

    private final JobSeekerPostServiceImpl jobSeekerPostService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchJobSeekerPostResponse>>> searchPostForJobSeeker(JobSeekerPostFilter filters) {
        var dataResponse = jobSeekerPostService.searchJobSeekerPost(filters);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Fetch Job seeker post successfully", dataResponse.getLeft(), dataResponse.getRight()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(CreateJobSeekerPostRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.createPost(request), "Created Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updatePost(Long id, UpdateJobSeekerPostRequest request) {
        request.setId(id);
        jobSeekerPostService.updatePost(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Update Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<JobSeekerDetailResponse>> getDetailPost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.getDetailPost(id), "Get detail Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deletePost(Long id) {
        jobSeekerPostService.deletePost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Delete Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> togglePost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Toggle Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateStatus(Long id, String status) {
        jobSeekerPostService.updateStatusPost(id, status);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Update status Job seeker post successfully"));
    }


    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> duplicatePost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.duplicatePost(id), "Duplicate Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<JobSeekerStatisticPostResponse>> getPostStatistic(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.getPostStatistic(id), "Get statistic Job seeker post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<JobSeekerPostOverViewResponse>> getPostOverview() {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.getPostOverview(), "Get post overview of current user"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> viewPost(Long id) {
        jobSeekerPostService.viewPost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "View Job seeker post successfully"));
    }


    @Override
    public ResponseEntity<ApiResponseDto<List<JobOpportunityResponse>>> getListJobOpportunities(Long jobSeekerPostId) {
        return ResponseEntity.ok(ApiResponseDto.success(jobSeekerPostService.getJobOpportunities(jobSeekerPostId), "Job opportunities fetched successfully"));
    }
}
