package vn.flexin.backend.mono.post.controller.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.controller.mobile.MobilePostController;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostInterestFilter;
import vn.flexin.backend.mono.post.dto.filters.SearchPostFilter;
import vn.flexin.backend.mono.post.dto.request.CreatePostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.CreatePostApplicationRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostApplicationRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostRequest;
import vn.flexin.backend.mono.post.dto.response.employer.DetailPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticPostResponse;
import vn.flexin.backend.mono.post.enums.PostApplicationStatus;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.post.service.mobile.PostApplicationService;
import vn.flexin.backend.mono.post.service.mobile.PostService;
import vn.flexin.backend.mono.post.service.mobile.PostInterestService;

import java.util.List;

@RestController
@AllArgsConstructor
public class MobilePostControllerImpl implements MobilePostController {

    private final PostService postService;
    private final PostInterestService postInterestService;
    private final PostApplicationService postApplicationService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> searchPostForEmployer(SearchPostFilter filters) {

        Pair<List<SearchPostResponse>, PaginationResponse> result = postService.searchPostForEmployer(filters);

        return ResponseEntity.ok(PaginationApiResponseDto.success("Employer posts fetched successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(CreatePostRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.createEmployerPost(request),"Employer post created successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updatePost(Long id, UpdatePostRequest request) {
        request.setId(id);
        postService.updateEmployerPost(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Employer post updated successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<DetailPostResponse>> getDetailPost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.getDetailPost(id),"Employer post fetched successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deletePost(Long id) {
        postService.deletePost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Employer post deleted successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> togglePost(Long id) {
        postService.togglePost(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Employer post toggled successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> closePost(Long id) {
        postService.updateStatusPost(id, PostStatus.CLOSED);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Employer post closed successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> reopenPost(Long id) {
        postService.updateStatusPost(id, PostStatus.REOPEN);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Employer post reopened successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<DetailPostResponse>> duplicatePost(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.duplicatePost(id),"Employer post duplicate successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<StatisticPostResponse>> getPostStatistic(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(postService.getPostStatistic(id),"Employer post statistics fetched successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> createPostInterest(Long id) {
        postInterestService.createPostInterest(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Interest post successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deletePostInterest(Long id) {
        postInterestService.deletePostInterest(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Delete post interest successfully"));    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> getInterestedPosts(PostInterestFilter filters) {
        Pair<List<SearchPostResponse>, PaginationResponse> result = postService.getInterestedPosts(filters);

        return ResponseEntity.ok(PaginationApiResponseDto.success("Employer posts fetched successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> createPostApplication(Long id, CreatePostApplicationRequest request) {
        postApplicationService.createPostApplication(id, request.getResumeId());
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Apply successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updatePostApplication(Long applicationId, UpdatePostApplicationRequest request) {
        postApplicationService.updatePostApplicationResume(applicationId, request.getResumeId());
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Update application successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deletePostApplication(Long postApplicationId) {
        postApplicationService.deletePostApplication(postApplicationId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Delete application successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchPostResponse>>> getAppliedPosts(PostApplicationFilter filters) {
        Pair<List<SearchPostResponse>, PaginationResponse> result = postService.getAppliedPosts(filters);

        return ResponseEntity.ok(PaginationApiResponseDto.success("Employer posts fetched successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<PostApplicationResponse>>> getPostApplications(Long postId, PostApplicationFilter filters) {
        Pair<List<PostApplicationResponse>, PaginationResponse> result = postService.getPostApplications(postId, filters);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Employer posts application fetched successfully", result.getFirst(), result.getSecond()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> approvePostApplication(Long applicationId) {
        postApplicationService.updatePostApplicationStatus(applicationId, PostApplicationStatus.SHORT_LIST.getValue());
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Approve successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> rejectPostApplication(Long applicationId) {
        postApplicationService.updatePostApplicationStatus(applicationId, PostApplicationStatus.REJECT.getValue());
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Reject successfully"));
    }

}
