package vn.flexin.backend.mono.post.dto;

import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ExperienceDto {
    @PositiveOrZero
    private Integer min;
    @PositiveOrZero
    private Integer max;

    public ExperienceDto(Map<String, Object> experience) {
        this.max = (Integer) experience.get("max");
        this.min = (Integer) experience.get("min");
    }
}
