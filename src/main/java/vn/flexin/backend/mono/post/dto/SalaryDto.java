package vn.flexin.backend.mono.post.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.post.enums.SalaryPeriod;
import vn.flexin.backend.mono.post.entity.employer.Salary;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SalaryDto {
    private Long id;
    @PositiveOrZero
    private Integer min;
    @PositiveOrZero
    private Integer max;
    @NotBlank
    private String currency = "VND";
    @NotBlank
    private SalaryPeriod period;

    public SalaryDto(Salary salary) {
        this.max = salary.getMax();
        this.min = salary.getMin();
        this.currency = salary.getCurrency();
        this.period = salary.getPeriod();
    }
}
