package vn.flexin.backend.mono.post.dto.filters;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.Constant;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.post.entity.employer.Post;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class PostFilter extends BaseFilter<Post> {
    private String keyword;

    private String status;

    private String workType;

    private String jobType;

    @JsonProperty("isFeatureJob")
    private Boolean isFeatureJob;

    @JsonProperty("urgentHiring")
    private Boolean urgentHiring;

    private List<LocalDate> dateRange;

    @Override
    public String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? Branch.Fields.id : sortBy;
    }

    @Override
    public String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_ASC : sortOrder;
    }

    @Override
    public Specification<Post> toSpecification() {
        var condition = new Condition();

        if (keyword != null && !keyword.isEmpty()) {
            var searchTitleCondition = new Condition()
                    .append(new Where(Post.Fields.title, Operator.LIKE, keyword));
            var searchDescriptionCondition = new Condition()
                    .append(new Where(Post.Fields.description, Operator.LIKE, keyword));
            var searchBranchCondition = new Condition()
                    .append(new Join(Post.Fields.branch, List.of(new Where(Branch.Fields.name, Operator.LIKE, keyword))));
            condition.appendComplex(new Where(Complex.OR, List.of(searchTitleCondition, searchDescriptionCondition, searchBranchCondition)));
        }

        if (status != null && !status.isEmpty()) {
            condition.append(new Where(Post.Fields.status, Operator.LIKE, status));
        }

        if (workType != null && !workType.isEmpty()) {
            //TODO handle filter by workType
            //condition.append(new JsonbAnyExists("workingInformation", "workType", workType));
        }

        if (jobType != null && !jobType.isEmpty()) {
            condition.append(new Where(Post.Fields.jobType, Operator.LIKE, jobType));
        }

        // Only filter by boolean fields if they are explicitly set
        if (isFeatureJob != null) {
            condition.append(new Where(Post.Fields.isFeatureJob, Operator.EQUAL, isFeatureJob));
        }

        if (urgentHiring != null) {
            condition.append(new Where(Post.Fields.urgentHiring, Operator.EQUAL, urgentHiring));
        }

        if (dateRange != null && dateRange.size() == 2) {
            //TODO handle date range filtering
            /*LocalDate startDate = dateRange.get(0);
            LocalDate endDate = dateRange.get(1);

            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            condition.append(new BetweenAnd<>(Post.Fields.postDate, startDateTime, endDateTime));*/
        }

        return SpecificationUtil.bySearchQuery(condition);
    }

    public class BetweenAnd<T> extends Where {
        public BetweenAnd(String field, T start, T end) {
            super(field + " between ? and ?", String.valueOf(start) + "," + String.valueOf(end));
        }
    }

}
