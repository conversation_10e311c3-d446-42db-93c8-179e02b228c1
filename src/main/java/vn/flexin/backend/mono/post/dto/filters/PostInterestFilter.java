package vn.flexin.backend.mono.post.dto.filters;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.post.entity.employer.PostInterest;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@SuperBuilder
public class PostInterestFilter extends BaseFilter<PostInterest> {

    @Override
    public Specification<PostInterest> toSpecification() {
        return null;
    }
}