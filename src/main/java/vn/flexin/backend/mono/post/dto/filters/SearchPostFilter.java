package vn.flexin.backend.mono.post.dto.filters;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.employer.Salary;
import vn.flexin.backend.mono.user.entity.User;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SearchPostFilter extends BaseFilter<Post> {
    private Long employerId;
    private String search;
    private List<String> jobType;
    private Boolean isRemote;
    private SalaryDto salary;
    private List<String> skills;

    @Override
    public Specification<Post> toSpecification() {
        var condition = new Condition();

        if(employerId != null){
            condition.append(new Join(Post.Fields.employer, List.of(new Where(User.Fields.id, employerId))));
        }

        if(jobType != null){
            condition.append(new Where(Post.Fields.jobType, Operator.IN, jobType));
        }

        if(skills != null){
            condition.append(new Where(Post.Fields.skills, Operator.IN, skills));
        }

        if (salary != null) {
            var whereList = new ArrayList<Where>();
            whereList.add(new Where(Salary.Fields.min, Operator.GREATER_THAN_OR_EQUAL, salary.getMin()));
            whereList.add(new Where(Salary.Fields.max, Operator.LESS_THAN_OR_EQUAL, salary.getMax()));
            condition.append(new Join(Post.Fields.salary, whereList));
        }
        if (StringUtils.isNotBlank(search)) {
            var subCondition = new Condition()
                    .append(new Where(Post.Fields.description, Operator.LIKE_IGNORE_CASE, search))
                    .append(new Where(Post.Fields.title, Operator.LIKE_IGNORE_CASE, search));
            condition.appendComplex(new Where(Complex.OR, List.of(subCondition)));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
