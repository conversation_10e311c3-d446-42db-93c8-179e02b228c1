package vn.flexin.backend.mono.post.dto.request.employer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.post.dto.ExperienceDto;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.dto.PostWorkingInformation;
import vn.flexin.backend.mono.post.dto.SalaryDto;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class AdminPostRequest {

    @NotBlank
    private String title;

    private String description;

    private String location;

    @NotNull
    private Long branchId;

    @Valid
    @NotNull
    private SalaryDto salary;

    private String status;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime postDate;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime expireDate;

    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;

    @JsonProperty("urgentHiring")
    private boolean urgentHiring;

    @NotNull
    private String jobType;

    @Valid
    private ExperienceDto experience;

    private String positions;

    private List<String> skills;

    private List<String> benefits;

    private List<PostRequiredDocument> requiredDocuments;

    private String rejectionReason;

    private String experienceLevel;

    private List<PostWorkingInformation> workingInformation;

    private String workType;

}
