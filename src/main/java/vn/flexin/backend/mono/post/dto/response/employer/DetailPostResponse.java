package vn.flexin.backend.mono.post.dto.response.employer;

import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.post.enums.JobType;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.enums.WorkType;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class DetailPostResponse {
    private Long id;
    private BasicUserInfoResponse employer;
    private BranchResponse branch;
    private String title;
    private WorkType workType;
    private String description;
    private SalaryDto salary;
    private Integer positions;
    private Integer minExperience;
    private Integer maxExperience;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer workingHourPerDay;
    private Set<PostWorkDay> workingDays;
    private Set<PostWorkShift> workingShifts;
    private Set<String> skills;
    private List<String> benefits;
    private List<PostRequiredDocument> requiredDocuments;
    private boolean isFeatureJob;
    private String featureDuration;
    private boolean urgentHiring;
    private boolean receiveNotifyNewApplication;
    private boolean showContactInformation;
    private boolean autoApproveApplication;
    private LocalDateTime activeDate;
    private JobType jobType;
    private PostStatus status;
    private Long viewCount;
    private Long applicationCount;
}
