package vn.flexin.backend.mono.post.dto.response.employer;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import vn.flexin.backend.mono.post.entity.employer.PostApplication;
import vn.flexin.backend.mono.resume.entity.Education;
import vn.flexin.backend.mono.resume.entity.Language;
import vn.flexin.backend.mono.resume.entity.WorkExperience;
import vn.flexin.backend.mono.user.entity.ContactInfo;
import vn.flexin.backend.mono.user.entity.PartTimePreference;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Data
public class PostApplicationResponse {
    private Long id;

    private String status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    //Resume information
    private Long resumeId;
    private String resumeName;
    private String resumeDescription;
    private Long resumeUserId;
    private String resumeUlId;
    private boolean resumeIsActive;
    private LocalDateTime resumeLastUpdateAt;
    private Set<String> resumeSkills;
    private List<WorkExperience> resumeWorkExperiences;
    private List<Education> resumeEducations;
    private ContactInfo resumeContactInfo;
    private PartTimePreference resumePartTimePreference;
    private List<Language> resumeLanguages;
    private BasicUserInfoResponse userInfo;

    // Post information
    private Long postId;

    public PostApplicationResponse(PostApplication postApplication){
        this.id = postApplication.getId();
        this.status = postApplication.getStatus();
        this.createdAt = postApplication.getCreatedAt();

        this.postId = postApplication.getPost().getId();

        this.resumeId = postApplication.getResume().getId();
        this.resumeName = postApplication.getResume().getName();
        this.resumeDescription = postApplication.getResume().getDescription();
        this.resumeUserId = postApplication.getResume().getUser().getId();
        this.resumeUlId = postApplication.getResume().getUser().getUlid();
        this.resumeIsActive = postApplication.getResume().isActive();
        this.resumeLastUpdateAt = postApplication.getResume().getLastModifiedAt();
        this.resumeSkills = postApplication.getResume().getSkills();
        this.resumeWorkExperiences = postApplication.getResume().getWorkExperiences();
        this.resumeEducations = postApplication.getResume().getEducations();
        this.resumeContactInfo = postApplication.getResume().getContactInfo();
        this.resumePartTimePreference = postApplication.getResume().getPartTimePreference();
        this.resumeLanguages = postApplication.getResume().getLanguages();
        this.userInfo = new BasicUserInfoResponse(postApplication.getResume().getUser());
    }
}
