package vn.flexin.backend.mono.post.dto.response.employer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.post.enums.JobType;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.enums.WorkType;
import vn.flexin.backend.mono.post.dto.ExperienceDto;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.dto.SalaryDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)

public class SearchAdminPostResponse {
    private Long id;
    private String title;
    private String description;
    private String location;
    private String companyName;
    private SalaryDto salary;
    private String status;
    private LocalDateTime postDate;
    private LocalDateTime expireDate;
    private int applicationCount;
    private int newApplications;
    private Set<PostWorkDay> workingDays;
    private Set<PostWorkShift> workingShifts;
    private Integer workingHourPerDay;
    private WorkType workType;
    private ExperienceDto experience;
    private String positions;
    @JsonProperty("urgentHiring")
    private boolean urgentHiring;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    private JobType jobType;
    private List<String> skills;
    private long viewCount;
    private List<String> benefits;
    private List<PostRequiredDocument> requiredDocuments;
    private Long companyId;
    private Long branchId;
    private String branchName;
}
