package vn.flexin.backend.mono.post.dto.response.employer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.post.enums.JobType;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.enums.WorkType;
import vn.flexin.backend.mono.post.dto.ExperienceDto;
import vn.flexin.backend.mono.post.dto.SalaryDto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)

public class SearchPostResponse {
    private Long id;
    private Long employerId;
    private String title;
    private String description;
    private String location;
    private SalaryDto salary;
    private JobType jobType;
    private List<String> skills;
    private ExperienceDto experience;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    private Integer featureDuration;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime activeDate;
    private long applicantCount;
    private long viewCount;
    private Set<PostWorkDay> workingDays;
    private Set<PostWorkShift> workingShifts;
    private Integer workingHourPerDay;
    private WorkType workType;
}
