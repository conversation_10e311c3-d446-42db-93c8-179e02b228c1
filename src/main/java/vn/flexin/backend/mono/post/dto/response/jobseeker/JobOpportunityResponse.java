package vn.flexin.backend.mono.post.dto.response.jobseeker;

import lombok.Data;
import vn.flexin.backend.mono.post.enums.SalaryPeriod;

import java.time.LocalDateTime;

@Data
public class JobOpportunityResponse {
    private Long id;
    private String companyLogo;
    private String postName;
    private String companyName;
    private String status;
    private double matchingPercentage;
    private LocalDateTime lastModifiedAt;
    private Integer salaryAmount;
    private SalaryPeriod salaryPeriod;
    private boolean newPost;
}
