package vn.flexin.backend.mono.post.dto.response.jobseeker;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.response.SimpleAddressResponse;
import vn.flexin.backend.mono.post.dto.JobSeekerExperienceDto;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.enums.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobSeekerDetailResponse {
    private Long id;
    private String title;
    private String industry;
    private String description;
    private Long jobSeekerId;
    private SalaryDto salary;
    private Set<String> skills;
    private Set<String> languages;
    private String educationLevel;
    private String educationDetail;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer workingHourPerDay;
    private Set<PostWorkDay> workingDays;
    private Set<PostWorkShift> workingShifts;
    private List<JobSeekerExperienceDto> experiences;
    private JobType jobType;
    private ContractType contractType;
    private WorkType workType;
    private SimpleAddressResponse location;
    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;
    private String featureDuration;
    @JsonProperty("isEnableEmailNotification")
    private boolean isEnableEmailNotification;
    @JsonProperty("isEnableInformation")
    private boolean isEnableInformation;
    @JsonProperty("isAutoAcceptInterviewInvitation")
    private boolean isAutoAcceptInterviewInvitation;
    @JsonProperty("isReady")
    private boolean isReady;
    private LocalDateTime activeDate;
    private String status;
}
