package vn.flexin.backend.mono.post.entity.jobseeker;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.dto.JobSeekerExperienceDto;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_job_seeker_post_experiences")
public class JobSeekerExperience extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String industry;

    private Integer yearOfExperience;

    @ManyToOne
    @JoinColumn(name = "post_id")
    private JobSeekerPost post;

    public JobSeekerExperience(JobSeekerExperienceDto dto, JobSeekerPost post) {
        this.industry = dto.getIndustry();
        this.yearOfExperience = dto.getYearOfExperience();
        this.post = post;
    }
}
