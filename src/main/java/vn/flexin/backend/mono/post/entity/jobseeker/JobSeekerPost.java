package vn.flexin.backend.mono.post.entity.jobseeker;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.enums.*;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_job_seeker_posts")
public class JobSeekerPost extends AbstractAuditingEntity<Long>  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String title;

    private String industry;

    private String description;

    @OneToOne
    @JoinColumn(name = "address_id")
    private Address location;

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "job_seeker_id")
    private User jobSeeker;

    @OneToOne(mappedBy = "post", cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "salary_id")
    private JobSeekerSalary salary;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<String> skills = new HashSet<>();

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<String> languages = new HashSet<>();

    private String educationLevel;

    private String educationDetail;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer workingHourPerDay;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<PostWorkDay> workingDays;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<PostWorkShift> workingShifts;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<JobSeekerExperience> experiences;

    @Enumerated(EnumType.STRING)
    private JobType jobType;

    @Enumerated(EnumType.STRING)
    private ContractType contractType;

    @Enumerated(EnumType.STRING)
    private WorkType workType;

    private boolean isFeatureJob;

    @Enumerated(EnumType.STRING)
    private FeatureDuration featureDuration;

    private boolean isEnableEmailNotification;

    private boolean isEnableInformation;

    private boolean isAutoAcceptInterviewInvitation;

    private boolean isReady;

    private LocalDateTime activeDate;

    @Enumerated(EnumType.STRING)
    private PostStatus postStatus;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<JobSeekerPostView> postViews;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "postId",cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @Where(clause = "post_type = 'JOB_SEEKER_POST'")
    private List<InterviewRequest> interviewRequests;

    public List<JobSeekerPostView> getPostViews() {
        return this.postViews == null ? new ArrayList<>() : this.postViews;
    }

    public List<InterviewRequest> getInterviewRequests() {
        return this.interviewRequests == null ? new ArrayList<>() : this.interviewRequests;
    }
}
