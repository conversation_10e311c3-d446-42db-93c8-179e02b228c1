package vn.flexin.backend.mono.post.entity.jobseeker;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.enums.SalaryPeriod;
import vn.flexin.backend.mono.post.dto.SalaryDto;

import java.io.Serial;
import java.io.Serializable;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_job_seeker_salaries")
public class JobSeekerSalary extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Integer min;

    private Integer max;

    @NotNull
    private String currency;

    @NotNull
    @Enumerated(EnumType.STRING)
    private SalaryPeriod period;

    @OneToOne
    @JoinColumn(name = "post_id")
    private JobSeekerPost post;

    public JobSeekerSalary(SalaryDto dto, JobSeekerPost post) {
        this.min = dto.getMin();
        this.max = dto.getMax();
        this.currency = dto.getCurrency();
        this.period = dto.getPeriod();
        this.post = post;
    }
}
