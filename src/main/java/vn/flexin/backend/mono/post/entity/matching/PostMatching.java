package vn.flexin.backend.mono.post.entity.matching;

import jakarta.persistence.*;
import lombok.Data;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;
import vn.flexin.backend.mono.post.entity.employer.Post;

@Data
@Entity
@Table(name = "t_post_matchings")
public class PostMatching extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "employer_post_id")
    private Post employerPost;

    @ManyToOne
    @JoinColumn(name = "job_seeker_post_id")
    private JobSeekerPost jobSeekerPost;

    private Double matchingPercentage;

}
