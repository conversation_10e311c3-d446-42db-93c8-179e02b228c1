package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum ContractType {
    LONG_TERM,
    SHORT_TERM,
    INTERNSHIP,
    SEASON;

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static ContractType fromString(String value) {
        try {
            return valueOf(value);
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Contract type must be any of [" + getValues() +
                    "]");
        }
    }
}
