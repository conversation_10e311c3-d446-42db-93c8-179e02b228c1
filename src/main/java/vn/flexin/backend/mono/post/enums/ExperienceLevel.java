package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum ExperienceLevel {
    NOT_REQUIRED("Not_Required"),
    LESS_THAN_ONE_YEAR("Less_than_1_year"),
    ONE_TO_TWO_YEARS("1_2_years"),
    TWO_TO_FIVE_YEARS("2_5_years"),
    MORE_THAN_FIVE_YEARS("More_than_5_years");

    private final String value;

    ExperienceLevel(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static ExperienceLevel fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Experience Level type must be any of [" + getValues() + "]");
        }
    }
}
