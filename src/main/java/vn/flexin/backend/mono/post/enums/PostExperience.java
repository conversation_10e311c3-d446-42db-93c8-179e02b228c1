package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum PostExperience {
    NO_EXPERIENCE,
    LESS_THAN_ONE_YEAR,
    ONE_TO_TWO_YEAR,
    TWO_TO_THREE_YEAR,
    THREE_TO_FIVE_YEAR,
    MORE_THAN_FIVE_YEAR;

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static PostExperience fromString(String value) {
        try {
            return valueOf(value);
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Experience must be any of [" + getValues() +
                    "]");
        }
    }

}
