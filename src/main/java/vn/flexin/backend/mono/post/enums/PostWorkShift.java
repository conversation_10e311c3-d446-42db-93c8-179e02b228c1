package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum PostWorkShift {
    MORNING,
    AFTERNOON,
    EVENING,
    NIGHT,
    FLEXIBLE;

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static PostWorkShift fromString(String value) {
        try {
            return valueOf(value);
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Work shift must be any of [" + getValues() + "]");
        }
    }
}
