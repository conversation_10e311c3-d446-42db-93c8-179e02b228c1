package vn.flexin.backend.mono.post.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum PostPermissions {
    POST_READ("post_read"),
    POST_CREATE("post_create"),
    POST_UPDATE("post_update"),
    POST_DELETE("post_delete"),
    POST_APPROVE("post_approve"),
    POST_REJECT("post_reject"),
    POST_STATISTICS("post_statistics"),
    POST_APPLICATION_READ("post_application_read"),
    POST_APPLICATION_UPDATE("post_application_update"),
    POST_APPLICATION_DELETE("post_application_delete");

    private final String value;

    PostPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static PostPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Post permissions type must be any of [" + getValues() + "]");
        }
    }
} 