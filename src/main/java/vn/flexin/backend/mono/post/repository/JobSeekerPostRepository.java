package vn.flexin.backend.mono.post.repository;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;

import java.time.LocalDateTime;
import java.util.List;

@Repository
@Transactional
public interface JobSeekerPostRepository extends JpaSpecificationRepository<JobSeekerPost, Long> {
    List<JobSeekerPost> findByLastModifiedAtAfter(LocalDateTime oneHourAgo);
}
