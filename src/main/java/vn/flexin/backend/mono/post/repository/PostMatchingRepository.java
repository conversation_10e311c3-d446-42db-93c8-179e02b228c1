package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.post.entity.matching.PostMatching;

import java.util.List;
import java.util.Optional;

@Repository
public interface PostMatchingRepository extends JpaSpecificationRepository<PostMatching, Long> {
    Optional<PostMatching> findByEmployerPostIdAndJobSeekerPostId(Long employerPostId, Long jobSeekerPostId);

    @Query("""
        SELECT matching
        FROM PostMatching matching
        JOIN FETCH matching.jobSeekerPost
        JOIN FETCH matching.employerPost
        WHERE matching.jobSeekerPost.id = :jobSeekerPostId
    """)
    List<PostMatching> findAllByJobSeekerPostId(Long jobSeekerPostId);
}
