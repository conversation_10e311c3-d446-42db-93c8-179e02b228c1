package vn.flexin.backend.mono.post.repository;

import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.post.entity.matching.PostMatching;
@Repository
public interface PostMatchingRepository extends JpaSpecificationRepository<PostMatching, Long> {
//    ScopedValue<Object> findByEmployerPostIdAndJobSeekerPostId(Long id, Long id1);
}
