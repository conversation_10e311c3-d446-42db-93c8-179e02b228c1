package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.post.entity.employer.Post;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface PostRepository extends JpaSpecificationRepository<Post, Long> {
    @Query(value = """
        SELECT post
        FROM Post post
        JOIN FETCH post.postViews
        JOIN FETCH post.postApplication
        WHERE post.id = :id
    """)
    Optional<Post> findByIdForStatistic(@Param("id") Long id);

    @Query("SELECT MONTH(p.createdAt) as month, p.status, COUNT(p) as postCount, " +
            "COUNT(pa) as applicationCount " +
            "FROM Post p " +
            "LEFT JOIN p.postApplication pa " +
            "WHERE YEAR(p.createdAt) = :year " +
            "GROUP BY MONTH(p.createdAt), p.status ")
    List<Object[]> countPostsByMonthAndStatusAndYear(@Param("year") int year);

    @Query(value = """
    SELECT p
    FROM Post p
    JOIN p.postInterests pi
    WHERE pi.user.id = :userId
    ORDER BY pi.createdAt DESC
    """)
    List<Post> findPostsInterestedByUserId(@Param("userId") Long userId);

    @Query(value = """
    SELECT p
    FROM Post p
    JOIN p.postApplication pa
    WHERE pa.resume.user.id = :userId
    ORDER BY pa.createdAt DESC
    """)
    List<Post> findPostsApplicationByUserId(@Param("userId") Long userId);

    @Query("SELECT p FROM Post p WHERE p.branch.id = :branchId")
    List<Post> findByBranchId(@Param("branchId") Long branchId);

    List<Post> findByLastModifiedAtAfter(LocalDateTime oneHourAgo);
}
