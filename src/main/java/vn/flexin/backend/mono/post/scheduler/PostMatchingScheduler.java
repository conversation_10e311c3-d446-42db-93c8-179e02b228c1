package vn.flexin.backend.mono.post.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import vn.flexin.backend.mono.post.service.matching.PostMatchingService;

@Component
@Slf4j
public class PostMatchingScheduler {
    private PostMatchingService postMatchingService;

    @Scheduled(fixedRate = 3600000) // Run every hour (3600000 milliseconds)
    public void schedulePostMatching() {
        log.info("Starting scheduled post matching job");
        postMatchingService.matchPosts();
        log.info("Completed scheduled post matching job");
    }
}
