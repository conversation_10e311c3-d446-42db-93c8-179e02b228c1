package vn.flexin.backend.mono.post.service.admin;

import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerDetailResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.SearchJobSeekerPostResponse;

import java.util.List;

public interface AdminJobSeekerPostService {
    CreateObjectResponse createPost(CreateJobSeekerPostRequest request);
    void updatePost(UpdateJobSeekerPostRequest request);
    JobSeekerDetailResponse getDetailPost(Long id);
    void deletePost(Long id);
    JobSeekerStatisticPostResponse getStatistics();
    List<SearchJobSeekerPostResponse> searchJobSeekerPost(JobSeekerPostFilter filters);
} 