package vn.flexin.backend.mono.post.service.admin;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;

import java.util.List;

public interface AdminPostApplicationService {
    Pair<List<PostApplicationResponse>, PaginationResponse> searchPostApplications(PostApplicationFilter filters);

    Long createPostApplication(Long postId, Long resumeId);

    List<PostApplicationResponse> getPostApplicationsByPostId(Long postId);

    void updatePostApplication(Long applicationId, Long resumeId);

    void deletePostApplication(Long applicationId);
}
