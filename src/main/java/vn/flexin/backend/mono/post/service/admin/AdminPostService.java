package vn.flexin.backend.mono.post.service.admin;

import org.springframework.data.util.Pair;

import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.dto.filters.PostFilter;
import vn.flexin.backend.mono.post.dto.request.employer.CreateAdminPostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.RejectPostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdateAdminPostRequest;
import vn.flexin.backend.mono.post.dto.response.employer.SearchAdminPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticAdminPostResponse;
import vn.flexin.backend.mono.post.entity.employer.Post;

import java.util.List;

public interface AdminPostService {
    Pair<List<SearchAdminPostResponse>, PaginationResponse> searchPostForAdmin(PostFilter request);

    Post getById(Long id);

    SearchAdminPostResponse getDetailPost(Long id);

    Long createAdminPost(CreateAdminPostRequest request);

    Post save(Post post);

    SearchAdminPostResponse updateAdminPost(UpdateAdminPostRequest request);

    void deletePost(Long id);

    SearchAdminPostResponse approvePost(Long id);

    SearchAdminPostResponse rejectPost(Long id, RejectPostRequest request);

    StatisticAdminPostResponse getStatisticsPost();


}
