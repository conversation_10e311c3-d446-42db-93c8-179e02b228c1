package vn.flexin.backend.mono.post.service.matching;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.employer.Salary;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerSalary;
import vn.flexin.backend.mono.post.enums.JobType;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.enums.WorkType;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Slf4j
public class PostMatchingAlgorithm {

    private static final Map<String, Map<String, Double>> INDUSTRY_RELATIONS = new HashMap<>();

    @PostConstruct
    public static void initializeIndustryRelations() {
        addRelation("restaurant_coffee", "restaurant_drinks", 90.0);
        addRelation("restaurant_coffee", "restaurant_cuisine", 80.0);
        addRelation("restaurant_coffee", "retail_convenience", 40.0);
        addRelation("restaurant_coffee", "travel_hotel", 50.0);
        addRelation("restaurant_coffee", "retail_supermarket", 30.0);

        addRelation("retail_supermarket", "retail_convenience", 80.0);
        addRelation("retail_supermarket", "restaurant_coffee", 30.0);
        addRelation("retail_supermarket", "restaurant_cuisine", 20.0);

        addRelation("restaurant_drinks", "restaurant_coffee", 90.0);
        addRelation("restaurant_drinks", "restaurant_cuisine", 70.0);
        addRelation("restaurant_drinks", "entertainment_events", 50.0);
        addRelation("restaurant_drinks", "travel_hotel", 40.0);

        addRelation("restaurant_cuisine", "restaurant_coffee", 80.0);
        addRelation("restaurant_cuisine", "restaurant_drinks", 70.0);
        addRelation("restaurant_cuisine", "travel_hotel", 60.0);
        addRelation("restaurant_cuisine", "retail_supermarket", 20.0);

        addRelation("retail_convenience", "retail_supermarket", 80.0);
        addRelation("retail_convenience", "restaurant_coffee", 40.0);
        addRelation("retail_convenience", "restaurant_drinks", 30.0);

        addRelation("education_training", "technology_it", 30.0);
        addRelation("education_training", "financial_banking", 20.0);
        addRelation("education_training", "entertainment_events", 20.0);

        addRelation("technology_it", "financial_banking", 40.0);
        addRelation("technology_it", "education_training", 30.0);
        addRelation("technology_it", "entertainment_events", 20.0);

        addRelation("financial_banking", "technology_it", 40.0);
        addRelation("financial_banking", "education_training", 20.0);
        addRelation("financial_banking", "retail_supermarket", 10.0);

        addRelation("travel_hotel", "restaurant_cuisine", 60.0);
        addRelation("travel_hotel", "restaurant_coffee", 50.0);
        addRelation("travel_hotel", "restaurant_drinks", 40.0);
        addRelation("travel_hotel", "entertainment_events", 40.0);

        addRelation("entertainment_events", "restaurant_drinks", 50.0);
        addRelation("entertainment_events", "travel_hotel", 40.0);
        addRelation("entertainment_events", "technology_it", 20.0);
        addRelation("entertainment_events", "education_training", 20.0);
    }

    private static void addRelation(String industry1, String industry2, Double percentage) {
        INDUSTRY_RELATIONS.computeIfAbsent(industry1, k -> new HashMap<>()).put(industry2, percentage);
        INDUSTRY_RELATIONS.computeIfAbsent(industry2, k -> new HashMap<>()).put(industry1, percentage);
    }

    public static Map<String, Map<String, Double>> getIndustryRelations() {
        return INDUSTRY_RELATIONS;
    }


    public double calculateMatchingPercentage(Post employerPost, JobSeekerPost jobSeekerPost) {
        double score = 0;
        double totalWeight = 0;

        // Industry matching (highest weight)
        Branch branch = employerPost.getBranch();
        String industry = branch.getCompany().getIndustry();
        double industryWeight = 0.2; // Adjust as needed
        double industryScore = calculateIndustryMatchScore(industry, jobSeekerPost.getIndustry());
        score += industryWeight * industryScore;
        totalWeight += industryWeight;

        // Work type matching
        double workTypeWeight = 0.05;
        double workTypeScore = calculateWorkTypeMatch(employerPost.getWorkType(), jobSeekerPost.getWorkType());
        score += workTypeWeight * workTypeScore;
        totalWeight += workTypeWeight;

        // Job Type Matching
        double jobTypeWeight = 0.05;
        double jobTypeScore = calculateJobTypeMatch(employerPost.getJobType(), jobSeekerPost.getJobType());
        score += jobTypeWeight * jobTypeScore;
        totalWeight += jobTypeWeight;

        // Working Hours Matching
        double workingHoursWeight = 0.1;
        double workingHoursScore = calculateWorkingHourMatch(employerPost.getWorkingHourPerDay(), jobSeekerPost.getWorkingHourPerDay());
        score += workingHoursWeight * workingHoursScore;
        totalWeight += workingHoursWeight;

        // Location matching
        double locationWeight = (employerPost.getWorkType() == WorkType.ONSITE) ? 0.2 : 0.05;
        double locationScore = calculateLocationMatchScore(employerPost, jobSeekerPost);
        score += locationWeight * locationScore;
        totalWeight += locationWeight;

        // Skills matching
        double skillsWeight = 0.1;
        Set<String> commonSkills = new HashSet<>(employerPost.getSkills());
        commonSkills.retainAll(jobSeekerPost.getSkills());
        score += skillsWeight * (commonSkills.size() / (double) employerPost.getSkills().size());
        totalWeight += skillsWeight;

        // Salary matching
        double salaryWeight = (employerPost.getWorkType() == WorkType.ONSITE) ? 0.1 : 0.15;
        score += salaryWeight * calculateSalaryMatchScore(employerPost.getSalary(), jobSeekerPost.getSalary());
        totalWeight += salaryWeight;

        // Shift Type Matching
        double shiftTypeWeight = (employerPost.getWorkType() == WorkType.ONSITE) ? 0.1 : 0.15;
        double shiftTypeScore = calculateShiftTypeMatch(employerPost.getWorkingShifts(), jobSeekerPost.getWorkingShifts());
        score += shiftTypeWeight * shiftTypeScore;
        totalWeight += shiftTypeWeight;

        // Working days matching
        double workingDaysWeight = 0.1;
        Set<PostWorkDay> commonWorkDays = new HashSet<>(employerPost.getWorkingDays());
        commonWorkDays.retainAll(jobSeekerPost.getWorkingDays());
        score += workingDaysWeight * (commonWorkDays.size() / (double) employerPost.getWorkingDays().size());
        totalWeight += workingDaysWeight;
        // Normalize the score
        return (score / totalWeight) * 100;
    }

    private double calculateShiftTypeMatch(Set<PostWorkShift> employerShifts, Set<PostWorkShift> jobSeekerShifts) {
        try {
            if (employerShifts.isEmpty() || jobSeekerShifts.isEmpty()) {
                return 0.5; // If either side has no shift preference, give a moderate score
            }

            if (employerShifts.contains(PostWorkShift.FLEXIBLE) || jobSeekerShifts.contains(PostWorkShift.FLEXIBLE)) {
                return 1.0; // If either side is flexible, it's a perfect match
            }

            double matchingShifts = 0;
            for (PostWorkShift shift : employerShifts) {
                if (jobSeekerShifts.contains(shift)) {
                    matchingShifts++;
                }
            }

            double matchRatio = matchingShifts / Math.max(employerShifts.size(), jobSeekerShifts.size());

            if (matchRatio == 1.0) {
                return 1.0; // Perfect match
            } else if (matchRatio >= 0.75) {
                return 0.9; // Excellent match
            } else if (matchRatio >= 0.5) {
                return 0.7; // Good match
            } else if (matchRatio >= 0.25) {
                return 0.5; // Moderate match
            } else {
                return 0.3; // Poor match
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateWorkTypeMatch(WorkType employerType, WorkType jobSeekerType) {
        try {
            if (employerType == jobSeekerType) {
                return 1.0; // Perfect match
            } else if (employerType == WorkType.HYBRID) {
                return 0.7; // Hybrid can accommodate both ONSITE and REMOTE to some extent
            } else if (jobSeekerType == WorkType.HYBRID) {
                return 0.8; // Job seeker willing to do hybrid can adapt to either ONSITE or REMOTE
            } else {
                return 0.3; // ONSITE vs REMOTE mismatch, but still give some points
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateWorkingHourMatch(int employerHours, int jobSeekerHours) {
        try {
            int difference = Math.abs(employerHours - jobSeekerHours);

            if (difference == 0) {
                return 1.0; // Perfect match
            } else if (difference <= 1) {
                return 0.9; // Very close match
            } else if (difference <= 2) {
                return 0.7; // Good match
            } else if (difference <= 3) {
                return 0.5; // Moderate match
            } else if (difference <= 4) {
                return 0.3; // Poor match
            } else {
                return 0.1; // Significant mismatch, but still give some points
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateJobTypeMatch(JobType employerType, JobType jobSeekerType) {
        try {
            if (employerType == jobSeekerType) {
                return 1.0; // Perfect match
            } else if (employerType == JobType.PART_TIME && jobSeekerType == JobType.FULL_TIME) {
                return 0.8; // Job seeker willing to do full-time can do part-time
            } else {
                return 0.2; // Part-time job seeker for full-time job is not ideal, but give some points
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateIndustryMatchScore(String employerIndustry, String jobSeekerIndustry) {
        try {
            if (employerIndustry.equals(jobSeekerIndustry)) {
                return 1.0; // Perfect match
            }

            Map<String, Double> relatedIndustries = getIndustryRelations().get(employerIndustry);
            if (relatedIndustries != null && relatedIndustries.containsKey(jobSeekerIndustry)) {
                return relatedIndustries.get(jobSeekerIndustry) / 100.0; // Convert percentage to decimal
            }

            return 0.0; // No relation found
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateLocationMatchScore(Post employerPost, JobSeekerPost jobSeekerPost) {
        try {
            if (employerPost.getWorkType() != WorkType.ONSITE) {
                return 1.0; // For non-onsite jobs, location doesn't matter
            }

            Address employerLocation = employerPost.getBranch().getAddress();
            Address jobSeekerLocation = jobSeekerPost.getLocation();

            // Calculate distance between locations (you'll need to implement this method)
            double distance = calculateDistance(employerLocation, jobSeekerLocation);

            // Define distance thresholds (in km) and corresponding scores
            if (distance <= 10) return 1.0;  // Excellent match
            if (distance <= 30) return 0.8;  // Very good match
            if (distance <= 50) return 0.6;  // Good match
            if (distance <= 100) return 0.4; // Moderate match
            if (distance <= 200) return 0.2; // Poor match
            return 0.0; // No match if distance is greater than 200km
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }
    }

    private double calculateDistance(Address loc1, Address loc2) {
        if (Objects.equals(loc1.getWard(), loc2.getWard())) {
            return 0;
        } else if (Objects.equals(loc1.getDistrict(), loc2.getDistrict())) {
            return 20;
        } else if (Objects.equals(loc1.getProvince(), loc2.getProvince())) {
            return 90;
        }
        return 1000;
    }

    private double calculateSalaryMatchScore(Salary employerSalary, JobSeekerSalary jobSeekerSalary) {
        try {
            // Check if currencies match
            if (!employerSalary.getCurrency().equals(jobSeekerSalary.getCurrency())) {
                return 0; // Different currencies, no match
            }

            // Check if periods match
            if (employerSalary.getPeriod() != jobSeekerSalary.getPeriod()) {
                return 0; // Different salary periods, no match
            }

            // Compare salary ranges
            int employerMin = employerSalary.getMin() != null ? employerSalary.getMin() : 0;
            int employerMax = employerSalary.getMax() != null ? employerSalary.getMax() : Integer.MAX_VALUE;
            int jobSeekerMin = jobSeekerSalary.getMin() != null ? jobSeekerSalary.getMin() : 0;
            int jobSeekerMax = jobSeekerSalary.getMax() != null ? jobSeekerSalary.getMax() : Integer.MAX_VALUE;

            // Check for overlap in salary ranges
            if (jobSeekerMin <= employerMax && employerMin <= jobSeekerMax) {
                // Calculate the overlap
                int overlapStart = Math.max(employerMin, jobSeekerMin);
                int overlapEnd = Math.min(employerMax, jobSeekerMax);
                int overlapRange = overlapEnd - overlapStart;

                // Calculate the total range
                int totalRange = Math.max(employerMax, jobSeekerMax) - Math.min(employerMin, jobSeekerMin);

                // Return the proportion of overlap
                return (double) overlapRange / totalRange;
            }

            return 0; // No overlap in salary ranges
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0.0;
        }

    }
}
