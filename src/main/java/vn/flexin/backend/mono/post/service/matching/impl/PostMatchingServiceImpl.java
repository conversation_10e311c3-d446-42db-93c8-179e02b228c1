package vn.flexin.backend.mono.post.service.matching.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;
import vn.flexin.backend.mono.post.entity.matching.PostMatching;
import vn.flexin.backend.mono.post.repository.JobSeekerPostRepository;
import vn.flexin.backend.mono.post.repository.PostMatchingRepository;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.service.matching.PostMatchingAlgorithm;
import vn.flexin.backend.mono.post.service.matching.PostMatchingService;

import java.time.LocalDateTime;
import java.util.*;

@Service
@AllArgsConstructor
public class PostMatchingServiceImpl implements PostMatchingService {
    private PostRepository postRepository;
    private JobSeekerPostRepository jobSeekerPostRepository;
    private PostMatchingRepository postMatchingRepository;

    private PostMatchingAlgorithm postMatchingAlgorithm;

    public void matchPosts() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        List<Post> employerPosts = postRepository.findAllActivePosts();
        List<JobSeekerPost> jobSeekerPosts = jobSeekerPostRepository.findAllActivePosts();
        List<JobSeekerPost> latestJobSeekerPosts = jobSeekerPosts.stream()
                .filter(post -> post.getLastModifiedAt().isAfter(oneHourAgo)).toList();

        for (Post employerPost : employerPosts) {
            if (employerPost.getLastModifiedAt().isAfter(oneHourAgo)) {
                for (JobSeekerPost jobSeekerPost : jobSeekerPosts) {
                    double matchingPercentage = postMatchingAlgorithm.calculateMatchingPercentage(employerPost, jobSeekerPost);
                    saveOrUpdateMatching(employerPost, jobSeekerPost, matchingPercentage);
                }
            } else {
                for (JobSeekerPost jobSeekerPost : latestJobSeekerPosts) {
                    double matchingPercentage = postMatchingAlgorithm.calculateMatchingPercentage(employerPost, jobSeekerPost);
                    saveOrUpdateMatching(employerPost, jobSeekerPost, matchingPercentage);
                }
            }
        }
    }

    public void matchAllPosts() {
        List<Post> employerPosts = postRepository.findAllActivePosts();
        List<JobSeekerPost> jobSeekerPosts = jobSeekerPostRepository.findAllActivePosts();

        for (Post employerPost : employerPosts) {
            for (JobSeekerPost jobSeekerPost : jobSeekerPosts) {
                double matchingPercentage = postMatchingAlgorithm.calculateMatchingPercentage(employerPost, jobSeekerPost);
                saveOrUpdateMatching(employerPost, jobSeekerPost, matchingPercentage);
            }
        }
    }

    private void saveOrUpdateMatching(Post employerPost, JobSeekerPost jobSeekerPost, double matchingPercentage) {
        PostMatching postMatching = postMatchingRepository.findByEmployerPostIdAndJobSeekerPostId(employerPost.getId(), jobSeekerPost.getId())
                .orElse(new PostMatching());

        postMatching.setEmployerPost(employerPost);
        postMatching.setJobSeekerPost(jobSeekerPost);
        postMatching.setMatchingPercentage(matchingPercentage);

        postMatchingRepository.save(postMatching);
    }
}
