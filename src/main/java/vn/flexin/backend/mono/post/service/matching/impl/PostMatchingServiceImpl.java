package vn.flexin.backend.mono.post.service.matching.impl;

import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;
import vn.flexin.backend.mono.post.repository.JobSeekerPostRepository;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.repository.PostMatchingRepository;
import vn.flexin.backend.mono.post.service.matching.PostMatchingService;

import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class PostMatchingServiceImpl implements PostMatchingService {
    private PostRepository postRepository;
    private JobSeekerPostRepository jobSeekerPostRepository;
    private PostMatchingRepository postMatchingRepository;

    public double calculateMatchingPercentage(Post employerPost, JobSeekerPost jobSeekerPost) {
        int totalCriteria = 0;
        int matchedCriteria = 0;


        /*
        * industries trc -> tim cac industries lien quan den nhau thi chon, k thi bo qua luon
        *
        *
        * */

        // Compare job title
        totalCriteria++;
        if (employerPost.getTitle().toLowerCase().contains(jobSeekerPost.getTitle().toLowerCase()) ||
                jobSeekerPost.getTitle().toLowerCase().contains(employerPost.getTitle().toLowerCase())) {
            matchedCriteria++;
        }

        // Compare skills
        totalCriteria++;
//        Set<String> employerSkills = employerPost.getSkills().stream()
//                .map(skill -> skill.getValue().toLowerCase())
//                .collect(Collectors.toSet());
//        Set<String> jobSeekerSkills = jobSeekerPost.getSkills().stream()
//                .map(String::toLowerCase)
//                .collect(Collectors.toSet());
//        if (!Collections.disjoint(employerSkills, jobSeekerSkills)) {
//            matchedCriteria++;
//        }

        // Compare job type
//        totalCriteria++;
//        if (employerPost.getJobType().equalsIgnoreCase(jobSeekerPost.getJobType())) {
//            matchedCriteria++;
//        }
//
//        // Compare work type
//        totalCriteria++;
//        if (employerPost.getWorkType().equalsIgnoreCase(jobSeekerPost.getWorkType())) {
//            matchedCriteria++;
//        }

        // Compare location
//        totalCriteria++;
//        if (employerPost.getLocation().equalsIgnoreCase(jobSeekerPost.getLocation().getProvince())) {
//            matchedCriteria++;
//        }
//
//        // Compare salary range
//        totalCriteria++;
//        if (isSalaryInRange(employerPost.getSalary(), jobSeekerPost.getSalary())) {
//            matchedCriteria++;
//        }

        // Compare education level
//        totalCriteria++;
//        if (employerPost.getEducation().equalsIgnoreCase(jobSeekerPost.getEducationLevel())) {
//            matchedCriteria++;
//        }

        // Calculate percentage
        return (double) matchedCriteria / totalCriteria * 100;
    }

//    private boolean isSalaryInRange(Salary employerSalary, JobSeekerSalary jobSeekerSalary) {
//        // Implement logic to check if the salary ranges overlap
//        // This is a simplified example, adjust according to your salary structure
//        return (employerSalary.getMinSalary() <= jobSeekerSalary.getMaxSalary() &&
//                employerSalary.getMaxSalary() >= jobSeekerSalary.getMinSalary());
//    }

    @Transactional
    public void updateMatchingPercentages() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);

        List<Post> recentEmployerPosts = postRepository.findByLastModifiedAtAfter(oneHourAgo);
        List<JobSeekerPost> recentJobSeekerPosts = jobSeekerPostRepository.findByLastModifiedAtAfter(oneHourAgo);

        for (Post employerPost : recentEmployerPosts) {
            for (JobSeekerPost jobSeekerPost : jobSeekerPostRepository.findAll()) {
                updateMatchingPercentage(employerPost, jobSeekerPost);
            }
        }

        for (JobSeekerPost jobSeekerPost : recentJobSeekerPosts) {
            for (Post employerPost : postRepository.findAll()) {
                updateMatchingPercentage(employerPost, jobSeekerPost);
            }
        }
    }

    private void updateMatchingPercentage(Post employerPost, JobSeekerPost jobSeekerPost) {
//        double percentage = calculateMatchingPercentage(employerPost, jobSeekerPost);
//
//        PostMatching matchingPercentage = postMatchingRepository
//                .findByEmployerPostIdAndJobSeekerPostId(employerPost.getId(), jobSeekerPost.getId())
//                .orElse(new PostMatchingPercentage());
//
//        matchingPercentage.setEmployerPostId(employerPost.getId());
//        matchingPercentage.setJobSeekerPostId(jobSeekerPost.getId());
//        matchingPercentage.setMatchingPercentage(percentage);
//        matchingPercentage.setLastUpdated(LocalDateTime.now());
//
//        matchingPercentageRepository.save(matchingPercentage);
    }

}
