package vn.flexin.backend.mono.post.service.mobile;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.*;

import java.util.List;

public interface JobSeekerPostService {
    CreateObjectResponse createPost(CreateJobSeekerPostRequest request);

    void updatePost(UpdateJobSeekerPostRequest request);

    JobSeekerDetailResponse getDetailPost(Long id);

    void deletePost(Long id);

    void updateStatusPost(Long id, String status);

    void viewPost(Long id);

    CreateObjectResponse duplicatePost(Long id);

    JobSeekerStatisticPostResponse getPostStatistic(Long id);

    Pair<List<SearchJobSeekerPostResponse>, PaginationResponse> searchJobSeekerPost(JobSeekerPostFilter filters) ;

    JobSeekerPostOverViewResponse getPostOverview();

    List<JobOpportunityResponse> getJobOpportunities(Long jobSeekerPostId);
}
