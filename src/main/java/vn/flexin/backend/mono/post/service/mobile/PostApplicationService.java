package vn.flexin.backend.mono.post.service.mobile;

import vn.flexin.backend.mono.post.entity.employer.PostApplication;

public interface PostApplicationService {
        PostApplication getPostApplicationById(Long id);
        void createPostApplication(Long postId, Long resumeId);
        void deletePostApplication(Long postApplicationId);
        void updatePostApplicationStatus(Long id, String status);
        void updatePostApplicationResume(Long id, Long resumeId);
}
