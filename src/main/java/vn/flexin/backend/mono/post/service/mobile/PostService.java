package vn.flexin.backend.mono.post.service.mobile;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostInterestFilter;
import vn.flexin.backend.mono.post.dto.filters.SearchPostFilter;
import vn.flexin.backend.mono.post.dto.request.CreatePostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostRequest;
import vn.flexin.backend.mono.post.dto.response.employer.DetailPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticPostResponse;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.enums.PostStatus;

import java.util.List;

public interface PostService {
    Pair<List<SearchPostResponse>, PaginationResponse> searchPostForEmployer(SearchPostFilter filters);

    CreateObjectResponse createEmployerPost(CreatePostRequest request);

    Post save(Post post);

    void updateEmployerPost(UpdatePostRequest request);

    Post getById(Long id);

    DetailPostResponse getDetailPost(Long id);

    void deletePost(Long id);

    void togglePost(Long id);

    void updateStatusPost(Long id, PostStatus postStatus);

    DetailPostResponse duplicatePost(Long id);

    StatisticPostResponse getPostStatistic(Long id);

    Pair<List<SearchPostResponse>, PaginationResponse> getInterestedPosts(PostInterestFilter filters);

    Pair<List<SearchPostResponse>, PaginationResponse> getAppliedPosts(PostApplicationFilter filters);

    Pair<List<PostApplicationResponse>, PaginationResponse> getPostApplications(Long postId, PostApplicationFilter filters);

}
