package vn.flexin.backend.mono.post.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.interview.enums.PostType;
import vn.flexin.backend.mono.interview.repository.InterviewRequestRepository;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.payment.repository.personal.PersonalWalletRepository;
import vn.flexin.backend.mono.post.dto.JobSeekerExperienceDto;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerDetailResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerPostOverViewResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.SearchJobSeekerPostResponse;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerExperience;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPost;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPostView;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerSalary;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.mapper.JobSeekerPostMapper;
import vn.flexin.backend.mono.post.repository.JobSeekerExperienceRepository;
import vn.flexin.backend.mono.post.repository.JobSeekerPostRepository;
import vn.flexin.backend.mono.post.repository.JobSeekerPostViewRepository;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.post.service.mobile.JobSeekerPostService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.impl.UserServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class JobSeekerPostServiceImpl implements JobSeekerPostService {

    private final UserServiceImpl userService;
    private final AddressService addressService;

    private final UserRepository userRepository;
    private final JobSeekerPostRepository jobSeekerPostRepository;
    private final JobSeekerExperienceRepository jobSeekerExperienceRepository;
    private final JobSeekerPostViewRepository jobSeekerPostViewRepository;
    private final InterviewRequestRepository interviewRequestRepository;
    private final PersonalWalletRepository personalWalletRepository;

    private JobSeekerPostMapper jobSeekerPostMapper;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<SearchJobSeekerPostResponse>, PaginationResponse> searchJobSeekerPost(JobSeekerPostFilter filters) {
        // Implement search logic using filters
        Page<JobSeekerPost> posts = jobSeekerPostRepository.findAll(filters);
        List<SearchJobSeekerPostResponse> responses = posts.getContent().stream()
                .map(jobSeekerPostMapper::toSearchJobSeekerPostResponse)
                .collect(Collectors.toList());

        PaginationResponse paginationResponse = new PaginationResponse(filters.getLimit(), filters.getPage(), posts.getNumberOfElements());

        return Pair.of(responses, paginationResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public JobSeekerPostOverViewResponse getPostOverview() {
        List<JobSeekerPost> allPosts = jobSeekerPostRepository.findAll();
        List<Long> postIds = allPosts.stream().map(JobSeekerPost::getId).toList();
        int postViewCount = jobSeekerPostViewRepository.countAllByPost_IdIn(postIds);
        int opportunityCount = 0;
        int postActiveCount = 0;
        int postDraftCount = 0;
        for (JobSeekerPost post : allPosts) {
            if (post.getPostStatus() == PostStatus.ACTIVE) {
                postActiveCount++;
            }
            if (post.getPostStatus() == PostStatus.DRAFT) {
                postDraftCount++;
            }
        }

        return new JobSeekerPostOverViewResponse(postViewCount, postActiveCount, postDraftCount, opportunityCount);
    }

    @Override
    @Transactional
    public CreateObjectResponse createPost(CreateJobSeekerPostRequest request) {
        JobSeekerPost post = jobSeekerPostMapper.toEntity(request);
        post.setJobSeeker(userRepository.findById(request.getJobSeekerId())
                .orElseThrow(() -> new ResourceNotFoundException("User not found"))
        );

        post.setPostStatus(request.getStatus());
        if (Objects.equals(request.getStatus(), PostStatus.ACTIVE)) {
            post.setActiveDate(LocalDateTime.now());
        } else {
            post.setActiveDate(null);
        }

        jobSeekerPostRepository.save(post);

        if (post.isFeatureJob()) {
            PersonalWallet personalWallet = personalWalletRepository.findByUser_Id(request.getJobSeekerId())
                    .orElseThrow(() -> new ResourceNotFoundException("Wallet Information not found"));
            Integer point = post.getFeatureDuration().getPoint();
            if (personalWallet.getPoint() < point) {
                throw new BadRequestException("Not enough points in wallet");
            }
            personalWallet.setPoint(personalWallet.getPoint() - point);

            personalWalletRepository.save(personalWallet);
        }

        if (!CollectionUtils.isEmpty(request.getExperiences())) {
            List<JobSeekerExperience> experiences = new ArrayList<>();
            for (JobSeekerExperienceDto dto : request.getExperiences()) {
                JobSeekerExperience experience = new JobSeekerExperience(dto, post);
                experiences.add(experience);
            }
            jobSeekerExperienceRepository.saveAll(experiences);
        }
        return new CreateObjectResponse(post.getId());
    }

    @Override
    @Transactional
    public void updatePost(UpdateJobSeekerPostRequest request) {
        JobSeekerPost post = jobSeekerPostRepository.findById(request.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        jobSeekerPostMapper.updateEntityFromDto(request, post);

        if (!CollectionUtils.isEmpty(request.getExperiences())) {
            List<JobSeekerExperience> existingExperiences = post.getExperiences();

            Map<Long, JobSeekerExperience> existingExperiencesMap = existingExperiences.stream()
                    .collect(Collectors.toMap(JobSeekerExperience::getId, Function.identity()));

            for (JobSeekerExperienceDto experienceDto : request.getExperiences()) {
                if (existingExperiencesMap.containsKey(experienceDto.getId())) {
                    JobSeekerExperience existingExperience = existingExperiencesMap.get(experienceDto.getId());
                    existingExperience.setIndustry(experienceDto.getIndustry());
                    existingExperience.setYearOfExperience(experienceDto.getYearOfExperience());
                    jobSeekerExperienceRepository.save(existingExperience);
                    existingExperiencesMap.remove(existingExperience.getId());
                } else {
                    JobSeekerExperience newExperience = new JobSeekerExperience();
                    newExperience.setPost(post);
                    newExperience.setIndustry(experienceDto.getIndustry());
                    newExperience.setYearOfExperience(experienceDto.getYearOfExperience());
                    post.getExperiences().add(newExperience);
                    jobSeekerExperienceRepository.save(newExperience);
                }
            }
            post.getExperiences().removeAll(existingExperiencesMap.values());
            jobSeekerExperienceRepository.deleteAll(existingExperiencesMap.values());
            jobSeekerPostRepository.save(post);
        }
    }

    @Override
    public JobSeekerDetailResponse getDetailPost(Long id) {
        JobSeekerPost post = jobSeekerPostRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        return jobSeekerPostMapper.toDetailPostResponse(post);
    }

    @Override
    @Transactional
    public void deletePost(Long id) {
        jobSeekerPostRepository.findById(id).orElseThrow(()-> new ResourceNotFoundException("Post not found"));

        jobSeekerExperienceRepository.deleteAllByPost_Id(id);
        jobSeekerPostViewRepository.deleteAllByPost_Id(id);
        interviewRequestRepository.deleteAllByPostIdAndPostType(id, PostType.JOB_SEEKER_POST);
        jobSeekerPostRepository.deleteById(id);
    }

//    @Override
//    public ResponseEntity<ApiResponseDto<Boolean>> togglePost(Long id) {
//        JobSeekerPost post = jobSeekerPostRepository.findById(id)
//                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));
//
//        post.setPostStatus(post.getPostStatus() == PostStatus.ACTIVE ? PostStatus.INACTIVE : PostStatus.ACTIVE);
//        jobSeekerPostRepository.save(post);
//
//        return ResponseEntity.ok(new ApiResponseDto<>(true));
//    }

    @Override
    @Transactional
    public void updateStatusPost(Long id, String postStatus) {
        JobSeekerPost post = jobSeekerPostRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        PostStatus status = PostStatus.fromString(postStatus);
        post.setPostStatus(status);
        jobSeekerPostRepository.save(post);
    }

    @Override
    @Transactional(readOnly = true)
    public void viewPost(Long id) {
        JobSeekerPost post = jobSeekerPostRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        User user = userService.getCurrentLoginUser();

        JobSeekerPostView view = jobSeekerPostViewRepository.findByViewer_IdAndPost_Id(user.getId(), post.getId());
        if (view != null) {
            return;
        }
        JobSeekerPostView jobSeekerPostView = new JobSeekerPostView();
        jobSeekerPostView.setPost(post);
        jobSeekerPostView.setViewer(user);

        jobSeekerPostViewRepository.save(jobSeekerPostView);
    }


    @Override
    @Transactional
    public CreateObjectResponse duplicatePost(Long id) {
        JobSeekerPost originalPost = jobSeekerPostRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        JobSeekerPost duplicatedPost = new JobSeekerPost();
        BeanUtils.copyProperties(originalPost, duplicatedPost, "id", "createdDate", "lastModifiedDate");

        if (originalPost.getSalary() != null) {
            JobSeekerSalary newSalary = new JobSeekerSalary();
            ModelMapperUtils.map(originalPost.getSalary(), newSalary);
            newSalary.setPost(duplicatedPost);
            newSalary.setId(null);
            duplicatedPost.setSalary(newSalary);
        }

        if (originalPost.getLocation() != null) {
            Address origin = originalPost.getLocation();
            Address cloneLocation = addressService.duplicateAddress(origin);
            duplicatedPost.setLocation(cloneLocation);
        }

        List<JobSeekerExperience> newExperiences = new ArrayList<>();
        for (JobSeekerExperience experience : originalPost.getExperiences()) {
            JobSeekerExperience newExperience = new JobSeekerExperience();
            ModelMapperUtils.map(experience, newExperience);
            newExperience.setId(null);
            newExperience.setPost(duplicatedPost);

            newExperiences.add(newExperience);
        }
        Set<String> duplicateLanguages = new HashSet<>(originalPost.getLanguages());
        Set<PostWorkDay> duplicateWorkingDays = new HashSet<>(originalPost.getWorkingDays());
        Set<PostWorkShift> duplicateWorkingShifts = new HashSet<>(originalPost.getWorkingShifts());
        Set<String> duplicateSkills = new HashSet<>(originalPost.getSkills());

        duplicatedPost.setId(null);
        duplicatedPost.setTitle(originalPost.getTitle() + " (Copy)");
        duplicatedPost.setPostStatus(PostStatus.DRAFT);
        duplicatedPost.setActiveDate(null);
        duplicatedPost.setPostViews(Collections.emptyList());
        duplicatedPost.setInterviewRequests(new ArrayList<>());
        duplicatedPost.setExperiences(newExperiences);
        duplicatedPost.setLanguages(duplicateLanguages);
        duplicatedPost.setWorkingDays(duplicateWorkingDays);
        duplicatedPost.setWorkingShifts(duplicateWorkingShifts);
        duplicatedPost.setSkills(duplicateSkills);

        duplicatedPost = jobSeekerPostRepository.save(duplicatedPost);

        return new CreateObjectResponse(duplicatedPost.getId());
    }

    @Override
    @Transactional(readOnly = true)
    public JobSeekerStatisticPostResponse getPostStatistic(Long id) {
        JobSeekerPost post = jobSeekerPostRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        // Implement logic to gather statistics
        JobSeekerStatisticPostResponse statistics = new JobSeekerStatisticPostResponse();

        // Fill in the statistics data
        List<JobSeekerPostView> views = post.getPostViews();
        List<InterviewRequest> interviewRequests = post.getInterviewRequests();
        statistics.setViewCount(views.size());
        statistics.setInterviewRequestCount(interviewRequests.size());
        statistics.setJobOpportunityCount(0);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate today = LocalDate.now();

        // Create a map with last 7 days initialized to 0
        Map<String, Integer> viewCounts = new LinkedHashMap<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            viewCounts.put(date.format(formatter), 0);
        }

        for (JobSeekerPostView view : views) {
            if (view.getCreatedAt() != null) {
                LocalDate viewDate = view.getCreatedAt().toLocalDate();
                String dateStr = viewDate.format(formatter);
                if (viewCounts.containsKey(dateStr)) {
                    viewCounts.put(dateStr, viewCounts.get(dateStr) + 1);
                }
            }
        }

        statistics.setLastSevenDaysViewCount(viewCounts);

        return statistics;
    }
}