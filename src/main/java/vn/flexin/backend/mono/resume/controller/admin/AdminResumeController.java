package vn.flexin.backend.mono.resume.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.resume.dto.*;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import java.util.List;

@Tag(name = "Admin Resumes APIs", description = "Admin resume management endpoints")
@SecurityRequirement(name = "bearerAuth")
public interface AdminResumeController {

    @Operation(summary = "Get all resumes", description = "Retrieve a list of all resumes (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resumes retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResumeDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping
    @PreAuthorize("hasAuthority('resume_read')")
    ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getAllResumes();

    @Operation(summary = "Search and filter resumes", description = "Search and filter resumes with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resumes retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('resume_read')")
    ResponseEntity<PaginationApiResponseDto<List<SearchResumeResponse>>> searchResumes(@Valid @RequestBody ResumeFilter resumeFilter);

    @Operation(summary = "Get resume by ID", description = "Retrieve a resume by its ID (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResumeDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('resume_read')")
    ResponseEntity<ApiResponseDto<ResumeDto>> getResumeById(@PathVariable Long id);

    @Operation(summary = "Get resumes by user ID", description = "Retrieve resumes for a specific user (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resumes retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResumeDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('resume_read')")
    ResponseEntity<ApiResponseDto<List<ResumeDto>>> getResumesByUserId(@PathVariable Long userId);

    @Operation(summary = "Create resume", description = "Create a new resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Resume created successfully",
                    content = @Content(schema = @Schema(implementation = ResumeDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('resume_create')")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createResume(@Valid @RequestBody ResumeDto resumeDto);

    @Operation(summary = "Update resume", description = "Update an existing resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume updated successfully",
                    content = @Content(schema = @Schema(implementation = ResumeDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<ResumeDto>> updateResume(
            @PathVariable Long id, @Valid @RequestBody ResumeDto resumeDto);

    @Operation(summary = "Delete resume", description = "Delete a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Resume deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('resume_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteResume(@PathVariable Long id);

    @Operation(summary = "Update isActive", description = "Update isActive property of a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "isActive updated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PatchMapping("/{id}/is-active")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<Boolean>> updateIsActive(@PathVariable Long id, @RequestParam boolean isActive);

    // Work Experience Management
    @Operation(summary = "Add work experience", description = "Add work experience to a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Work experience added successfully",
                    content = @Content(schema = @Schema(implementation = WorkExperienceDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PostMapping("/{resumeId}/work-experiences")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<WorkExperienceDto>> addWorkExperience(
            @PathVariable Long resumeId, @Valid @RequestBody WorkExperienceDto workExperienceDto);

    @Operation(summary = "Update work experience", description = "Update work experience (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Work experience updated successfully",
                    content = @Content(schema = @Schema(implementation = WorkExperienceDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Work experience not found")
    })
    @PutMapping("/work-experiences/{id}")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<WorkExperienceDto>> updateWorkExperience(
            @PathVariable Long id, @Valid @RequestBody WorkExperienceDto workExperienceDto);

    @Operation(summary = "Delete work experience", description = "Delete work experience (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Work experience deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Work experience not found")
    })
    @DeleteMapping("/work-experiences/{id}")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteWorkExperience(@PathVariable Long id);

    // Education Management
    @Operation(summary = "Add education", description = "Add education to a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Education added successfully",
                    content = @Content(schema = @Schema(implementation = EducationDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PostMapping("/{resumeId}/educations")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<EducationDto>> addEducation(
            @PathVariable Long resumeId, @Valid @RequestBody EducationDto educationDto);

    @Operation(summary = "Update education", description = "Update education (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Education updated successfully",
                    content = @Content(schema = @Schema(implementation = EducationDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Education not found")
    })
    @PutMapping("/educations/{id}")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<EducationDto>> updateEducation(
            @PathVariable Long id, @Valid @RequestBody EducationDto educationDto);

    @Operation(summary = "Delete education", description = "Delete education (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Education deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Education not found")
    })
    @DeleteMapping("/educations/{id}")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteEducation(@PathVariable Long id);

    // Contact Info Management
    @Operation(summary = "Update contact info", description = "Update contact info for a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contact info updated successfully",
                    content = @Content(schema = @Schema(implementation = ContactInfoDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PutMapping("/{resumeId}/contact-info")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<ContactInfoDto>> updateContactInfo(
            @PathVariable Long resumeId, @Valid @RequestBody ContactInfoDto contactInfoDto);

    // Part Time Preference Management
    @Operation(summary = "Update part time preference", description = "Update part time preference for a resume (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Part time preference updated successfully",
                    content = @Content(schema = @Schema(implementation = PartTimePreferenceDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Resume not found")
    })
    @PutMapping("/{resumeId}/part-time-preference")
    @PreAuthorize("hasAuthority('resume_update')")
    ResponseEntity<ApiResponseDto<PartTimePreferenceDto>> updatePartTimePreference(
            @PathVariable Long resumeId, @Valid @RequestBody PartTimePreferenceDto partTimePreferenceDto);
} 