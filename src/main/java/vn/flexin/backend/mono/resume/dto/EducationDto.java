package vn.flexin.backend.mono.resume.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@NoArgsConstructor
public class EducationDto {
    private Long id;
    
    @NotBlank(message = "Institution name is required")
    private String institution;
    
    @NotBlank(message = "Degree is required")
    private String degree;
    
    @NotBlank(message = "Field of study is required")
    private String fieldOfStudy;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    private LocalDate endDate;
    
    private String description;
    
    private Long resumeId;
} 