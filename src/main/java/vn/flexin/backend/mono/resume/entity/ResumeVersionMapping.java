package vn.flexin.backend.mono.resume.entity;

import jakarta.persistence.*;
import lombok.*;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;

import java.io.Serial;
import java.io.Serializable;

@Entity
@Table(name = "t_resume_versions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResumeVersionMapping extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "original_resume_id", nullable = false)
    private Resume original;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "snapshot_resume_id", nullable = false, unique = true)
    private Resume snapshot;
}