package vn.flexin.backend.mono.resume.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.resume.dto.*;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import java.util.List;

public interface AdminResumeService {
    Long createResume(ResumeDto resumeDto);
    Long snapshotResume(Long id);
    ResumeDto getResumeById(Long id);
    ResumeDto getResumeByIdFull(Long id);
    Resume getOriginalResumeById(Long id);
    List<SearchResumeResponse> getResumesByUserId(Long userId);
    List<ResumeDto> getResumesByUserIdFull(Long userId);
    List<SearchResumeResponse> getActiveResumesByUserId(Long userId);
    List<SearchResumeResponse> getAllResumes();
    Pair<PaginationResponse, List<SearchResumeResponse>> searchResumes(ResumeFilter resumeFilter);
    ResumeDto updateResume(Long id, ResumeDto resumeDto);
    void deleteResume(Long id);
    void deleteSnapshotResume(Long id);
    void updateIsActive(Long id, boolean isActive);

    // Work Experience
    WorkExperienceDto addWorkExperience(Long resumeId, WorkExperienceDto workExperienceDto);
    WorkExperienceDto updateWorkExperience(Long id, WorkExperienceDto workExperienceDto);
    void deleteWorkExperience(Long id);
    
    // Education
    EducationDto addEducation(Long resumeId, EducationDto educationDto);
    EducationDto updateEducation(Long id, EducationDto educationDto);
    void deleteEducation(Long id);
    
    // Contact Info
    ContactInfoDto updateContactInfo(Long resumeId, ContactInfoDto contactInfoDto);
    
    // Part Time Preference
    PartTimePreferenceDto updatePartTimePreference(Long resumeId, PartTimePreferenceDto partTimePreferenceDto);
    
    // Helper method
    Resume getResumeEntityById(Long id);

    List<SearchResumeResponse> getMyResumes(Boolean isActive);

    Long adminCreateResume(ResumeDto resumeDto);
} 