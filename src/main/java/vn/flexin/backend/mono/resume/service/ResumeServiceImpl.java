package vn.flexin.backend.mono.resume.service;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.resume.dto.*;
import vn.flexin.backend.mono.resume.entity.*;
import vn.flexin.backend.mono.resume.enums.LanguageLevel;
import vn.flexin.backend.mono.resume.repository.ResumeRepository;
import vn.flexin.backend.mono.resume.repository.ResumeVersionRepository;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;
import vn.flexin.backend.mono.user.entity.ContactInfo;
import vn.flexin.backend.mono.user.entity.PartTimePreference;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ResumeServiceImpl implements ResumeService {

    @Autowired
    private ResumeRepository resumeRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private ResumeVersionRepository resumeVersionRepository;

    @Override
    public Long createResume(ResumeDto resumeDto) {
        User user = userService.getCurrentLoginUser();
        
        Resume resume = new Resume();
        resume.setName(resumeDto.getName());
        resume.setDescription(resumeDto.getDescription());
        resume.setUser(user);
        resume.setActive(resumeDto.isActive());
        resume.setSkills(resumeDto.getSkills() != null ? new HashSet<>(resumeDto.getSkills()) : new HashSet<>());
        List<WorkExperience> workExperiences = toWorkingExperiences(resumeDto.getWorkExperiences(), resume);
        List<Education> educations = toEducations(resumeDto.getEducations(), resume);
        PartTimePreference partTimePreferences = toPartTimePreference(resumeDto.getPartTimePreference(), resume);
        List<Language> languages = toLanguages(resumeDto.getLanguages(), resume);
        ContactInfo contactInfo = toContactInfo(resumeDto.getContactInfo(), resume);
        resume.setWorkExperiences(workExperiences);
        resume.setEducations(educations);
        resume.setPartTimePreference(partTimePreferences);
        resume.setLanguages(languages);
        resume.setContactInfo(contactInfo);
        
        Resume savedResume = resumeRepository.save(resume);
        return savedResume.getId();
    }

    @Override
    public Long snapshotResume(Long id) {
        Resume originalResume = getResumeEntityById(id);

        // Create a copy of the resume for snapshotting by converting to DTO first
        ResumeDto originalDto = mapToDto(originalResume);

        // Create the snapshot resume from the DTO
        Long snapshotResumeId = createResume(originalDto);
        Resume snapshotResume = getResumeEntityById(snapshotResumeId);

        // Fetch the original resume again to ensure we have a clean managed entity for mapping
        Resume originalResumeForMapping = getResumeEntityById(id);

        ResumeVersionMapping mapping = new ResumeVersionMapping();
        mapping.setOriginal(originalResumeForMapping);
        mapping.setSnapshot(snapshotResume);
        resumeVersionRepository.save(mapping);

        return snapshotResumeId;
    }

    @Override
    public Long adminCreateResume(ResumeDto resumeDto) {
        User user = userService.getUserEntityById(resumeDto.getUserId());
        Resume resume = new Resume();
        resume.setName(resumeDto.getName());
        resume.setDescription(resumeDto.getDescription());
        resume.setUser(user);
        resume.setActive(resumeDto.isActive());
        resume.setSkills(resumeDto.getSkills() != null ? new HashSet<>(resumeDto.getSkills()) : new HashSet<>());
        List<WorkExperience> workExperiences = toWorkingExperiences(resumeDto.getWorkExperiences(), resume);
        List<Education> educations = toEducations(resumeDto.getEducations(), resume);
        PartTimePreference partTimePreferences = toPartTimePreference(resumeDto.getPartTimePreference(), resume);
        List<Language> languages = toLanguages(resumeDto.getLanguages(), resume);
        ContactInfo contactInfo = toContactInfo(resumeDto.getContactInfo(), resume);
        resume.setWorkExperiences(workExperiences);
        resume.setEducations(educations);
        resume.setPartTimePreference(partTimePreferences);
        resume.setLanguages(languages);
        resume.setContactInfo(contactInfo);
        Resume savedResume = resumeRepository.save(resume);
        return savedResume.getId();
    }

    private ContactInfo toContactInfo(ContactInfoDto dto, Resume resume) {
        if (dto == null) {
            return null;
        }
        ContactInfo entity = ModelMapperUtils.toObject(dto, ContactInfo.class);
        entity.setId(null); // Clear ID to ensure it's treated as a new entity
        entity.setResume(resume);
        return entity;
    }

    private List<Language> toLanguages(List<LanguageDto> dtos, Resume resume) {
        if (dtos == null) {
            return new ArrayList<>();
        }
        List<Language> entities = new ArrayList<>();
        for (LanguageDto dto : dtos) {
            Language entity = new Language();
            entity.setName(dto.getLanguage());
            entity.setLevel(LanguageLevel.fromString(dto.getLevel()).value());
            entity.setResume(resume);
            entities.add(entity);
        }
        return entities;
    }

    private PartTimePreference toPartTimePreference(PartTimePreferenceDto dto, Resume resume) {
        if (dto == null) {
            return null;
        }
        PartTimePreference entity = ModelMapperUtils.toObject(dto, PartTimePreference.class);
        entity.setId(null); // Clear ID to ensure it's treated as a new entity
        entity.setResume(resume);
        return entity;
    }

    private List<Education> toEducations(List<EducationDto> dtos, Resume resume) {
        if (dtos == null) {
            return new ArrayList<>();
        }
        List<Education> entities = new ArrayList<>();
        for (EducationDto dto : dtos) {
            Education entity = ModelMapperUtils.toObject(dto, Education.class);
            entity.setId(null); // Clear ID to ensure it's treated as a new entity
            entity.setResume(resume);
            entities.add(entity);
        }
        return entities;
    }

    private List<WorkExperience> toWorkingExperiences(List<WorkExperienceDto> dtos, Resume resume) {
        if (dtos == null) {
            return new ArrayList<>();
        }
        List<WorkExperience> entities = new ArrayList<>();
        for (WorkExperienceDto dto : dtos) {
            WorkExperience entity = ModelMapperUtils.toObject(dto, WorkExperience.class);
            entity.setId(null); // Clear ID to ensure it's treated as a new entity
            entity.setResume(resume);
            entities.add(entity);
        }
        return entities;
    }

    @Override
    public ResumeDto getResumeById(Long id) {
        Resume resume = getResumeEntityById(id);
        return mapToDto(resume);
    }

    @Override
    public ResumeDto getResumeByIdFull(Long id) {
        // Fetch basic details first
        Resume resume = resumeRepository.findByIdWithBasicDetails(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resume", "id", id));
        
        // Fetch collections separately to avoid MultipleBagFetchException
        resumeRepository.findByIdWithWorkExperiences(id).ifPresent(r -> 
            resume.setWorkExperiences(r.getWorkExperiences())
        );
        
        resumeRepository.findByIdWithEducations(id).ifPresent(r -> 
            resume.setEducations(r.getEducations())
        );
        
        resumeRepository.findByIdWithLanguages(id).ifPresent(r -> 
            resume.setLanguages(r.getLanguages())
        );
        
        return mapToDto(resume);
    }

    @Override
    public Resume getOriginalResumeById(Long id) {
        return resumeVersionRepository.findOriginResume(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resume", "id", id));
    }

    @Override
    public List<SearchResumeResponse> getResumesByUserId(Long userId) {
        User user = userService.getUserEntityById(userId);
        List<Long> snapshotIds = resumeVersionRepository.findAllSnapshotResumeIds();
        List<Resume> resumes = resumeRepository.findByUser(user);
        return resumes.stream()
                .filter(r -> !snapshotIds.contains(r.getId()))
                .map(SearchResumeResponse::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<ResumeDto> getResumesByUserIdFull(Long userId) {
        User user = userService.getUserEntityById(userId);
        List<Long> snapshotIds = resumeVersionRepository.findAllSnapshotResumeIds();
        List<Resume> resumes = resumeRepository.findByUser(user);
        return resumes.stream()
                .filter(r -> !snapshotIds.contains(r.getId()))
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SearchResumeResponse> getActiveResumesByUserId(Long userId) {
        User user = userService.getUserEntityById(userId);
        List<Long> snapshotIds = resumeVersionRepository.findAllSnapshotResumeIds();
        List<Resume> resumes = resumeRepository.findByUserAndIsActiveTrue(user);
        return resumes.stream()
                .filter(r -> !snapshotIds.contains(r.getId()))
                .map(SearchResumeResponse::new)
                .collect(Collectors.toList());
    }

    @Override
    public ResumeDto updateResume(Long id, ResumeDto resumeDto) {
        Resume resume = getResumeEntityById(id);
        resume.setName(resumeDto.getName());
        resume.setDescription(resumeDto.getDescription());
        resume.setActive(resumeDto.isActive());
        resume.setSkills(resumeDto.getSkills() != null ? new HashSet<>(resumeDto.getSkills()) : new HashSet<>());

        // Update contact info
        if (resumeDto.getContactInfo() != null) {
            ContactInfo contactInfo = resume.getContactInfo();
            if (contactInfo == null) {
                contactInfo = new ContactInfo();
                contactInfo.setResume(resume);
            }
            contactInfo.setPhoneNumber(resumeDto.getContactInfo().getPhoneNumber());
            contactInfo.setEmail(resumeDto.getContactInfo().getEmail());
            contactInfo.setAddress(resumeDto.getContactInfo().getAddress());
            contactInfo.setCity(resumeDto.getContactInfo().getCity());
            contactInfo.setState(resumeDto.getContactInfo().getState());
            contactInfo.setZipCode(resumeDto.getContactInfo().getZipCode());
            contactInfo.setCountry(resumeDto.getContactInfo().getCountry());
            contactInfo.setLinkedInUrl(resumeDto.getContactInfo().getLinkedInUrl());
            contactInfo.setPortfolioUrl(resumeDto.getContactInfo().getPortfolioUrl());
            resume.setContactInfo(contactInfo);
        }

        // Update work experiences
        resume.getWorkExperiences().clear();
        if (resumeDto.getWorkExperiences() != null) {
            for (WorkExperienceDto weDto : resumeDto.getWorkExperiences()) {
                WorkExperience we = ModelMapperUtils.toObject(weDto, WorkExperience.class);
                we.setResume(resume);
                resume.getWorkExperiences().add(we);
            }
        }

        // Update educations
        resume.getEducations().clear();
        if (resumeDto.getEducations() != null) {
            for (EducationDto eduDto : resumeDto.getEducations()) {
                Education edu = ModelMapperUtils.toObject(eduDto, Education.class);
                edu.setResume(resume);
                resume.getEducations().add(edu);
            }
        }

        // Update languages
        resume.getLanguages().clear();
        if (resumeDto.getLanguages() != null) {
            for (LanguageDto langDto : resumeDto.getLanguages()) {
                Language lang = new Language();
                lang.setName(langDto.getLanguage()); // fix mapping
                lang.setLevel(langDto.getLevel());
                lang.setResume(resume);
                resume.getLanguages().add(lang);
            }
        }

        // Update part time preference
        if (resumeDto.getPartTimePreference() != null) {
            PartTimePreference partTimePreference = resume.getPartTimePreference();
            if (partTimePreference == null) {
                partTimePreference = new PartTimePreference();
                partTimePreference.setResume(resume);
            }
            partTimePreference.setMinHourlyRate(resumeDto.getPartTimePreference().getMinHourlyRate());
            partTimePreference.setMaxHoursPerWeek(resumeDto.getPartTimePreference().getMaxHoursPerWeek());
            partTimePreference.setAvailableDays(resumeDto.getPartTimePreference().getAvailableDays());
            partTimePreference.setAvailableTimeSlots(resumeDto.getPartTimePreference().getAvailableTimeSlots());
            partTimePreference.setPreferredJobTypes(resumeDto.getPartTimePreference().getPreferredJobTypes());
            partTimePreference.setPreferredLocations(resumeDto.getPartTimePreference().getPreferredLocations());
            partTimePreference.setRemoteOnly(resumeDto.getPartTimePreference().getRemoteOnly());
            partTimePreference.setMaxTravelDistance(resumeDto.getPartTimePreference().getMaxTravelDistance());
            partTimePreference.setAdditionalNotes(resumeDto.getPartTimePreference().getAdditionalNotes());
            partTimePreference.setIsStudent(resumeDto.getPartTimePreference().getIsStudent());
            partTimePreference.setStudyMajor(resumeDto.getPartTimePreference().getStudyMajor());
            resume.setPartTimePreference(partTimePreference);
        }

        Resume updatedResume = resumeRepository.save(resume);
        return mapToDto(updatedResume);
    }

    @Override
    public void deleteResume(Long id) {
        Resume resume = getResumeEntityById(id);
        resumeRepository.delete(resume);
    }

    @Override
    @Transactional
    public void deleteSnapshotResume(Long id) {
        Resume resume = getResumeEntityById(id);
        resumeVersionRepository.deleteBySnapshotId(id);
        resumeRepository.delete(resume);
    }

    @Override
    public WorkExperienceDto addWorkExperience(Long resumeId, WorkExperienceDto workExperienceDto) {
        Resume resume = getResumeEntityById(resumeId);
        
        WorkExperience workExperience = new WorkExperience();
        workExperience.setCompany(workExperienceDto.getCompany());
        workExperience.setPosition(workExperienceDto.getPosition());
        workExperience.setStartDate(workExperienceDto.getStartDate());
        workExperience.setEndDate(workExperienceDto.getEndDate());
        workExperience.setDescription(workExperienceDto.getDescription());
        
        resume.addWorkExperience(workExperience);
        resumeRepository.save(resume);
        
        return mapToDto(workExperience);
    }

    @Override
    public WorkExperienceDto updateWorkExperience(Long id, WorkExperienceDto workExperienceDto) {
        Resume resume = getResumeEntityById(workExperienceDto.getResumeId());
        
        WorkExperience workExperience = resume.getWorkExperiences().stream()
                .filter(we -> we.getId().equals(id))
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("WorkExperience", "id", id));
        
        workExperience.setCompany(workExperienceDto.getCompany());
        workExperience.setPosition(workExperienceDto.getPosition());
        workExperience.setStartDate(workExperienceDto.getStartDate());
        workExperience.setEndDate(workExperienceDto.getEndDate());
        workExperience.setDescription(workExperienceDto.getDescription());
        
        resumeRepository.save(resume);
        
        return mapToDto(workExperience);
    }

    @Override
    public void deleteWorkExperience(Long id) {
        // Find the resume containing this work experience
        List<Resume> allResumes = resumeRepository.findAll();
        
        for (Resume resume : allResumes) {
            WorkExperience workExperience = resume.getWorkExperiences().stream()
                    .filter(we -> we.getId().equals(id))
                    .findFirst()
                    .orElse(null);
            
            if (workExperience != null) {
                resume.removeWorkExperience(workExperience);
                resumeRepository.save(resume);
                return;
            }
        }
        
        throw new ResourceNotFoundException("WorkExperience", "id", id);
    }

    @Override
    public EducationDto addEducation(Long resumeId, EducationDto educationDto) {
        Resume resume = getResumeEntityById(resumeId);
        
        Education education = new Education();
        education.setInstitution(educationDto.getInstitution());
        education.setDegree(educationDto.getDegree());
        education.setFieldOfStudy(educationDto.getFieldOfStudy());
        education.setStartDate(educationDto.getStartDate());
        education.setEndDate(educationDto.getEndDate());
        education.setDescription(educationDto.getDescription());
        
        resume.addEducation(education);
        resumeRepository.save(resume);
        
        return mapToDto(education);
    }

    @Override
    public EducationDto updateEducation(Long id, EducationDto educationDto) {
        Resume resume = getResumeEntityById(educationDto.getResumeId());
        
        Education education = resume.getEducations().stream()
                .filter(e -> e.getId().equals(id))
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("Education", "id", id));
        
        education.setInstitution(educationDto.getInstitution());
        education.setDegree(educationDto.getDegree());
        education.setFieldOfStudy(educationDto.getFieldOfStudy());
        education.setStartDate(educationDto.getStartDate());
        education.setEndDate(educationDto.getEndDate());
        education.setDescription(educationDto.getDescription());
        
        resumeRepository.save(resume);
        
        return mapToDto(education);
    }

    @Override
    public void deleteEducation(Long id) {
        // Find the resume containing this education
        List<Resume> allResumes = resumeRepository.findAll();
        
        for (Resume resume : allResumes) {
            Education education = resume.getEducations().stream()
                    .filter(e -> e.getId().equals(id))
                    .findFirst()
                    .orElse(null);
            
            if (education != null) {
                resume.removeEducation(education);
                resumeRepository.save(resume);
                return;
            }
        }
        
        throw new ResourceNotFoundException("Education", "id", id);
    }

    @Override
    public ContactInfoDto updateContactInfo(Long resumeId, ContactInfoDto contactInfoDto) {
        Resume resume = getResumeEntityById(resumeId);
        
        ContactInfo contactInfo = resume.getContactInfo();
        if (contactInfo == null) {
            contactInfo = new ContactInfo();
        }
        
        contactInfo.setPhoneNumber(contactInfoDto.getPhoneNumber());
        contactInfo.setEmail(contactInfoDto.getEmail());
        contactInfo.setAddress(contactInfoDto.getAddress());
        contactInfo.setCity(contactInfoDto.getCity());
        contactInfo.setState(contactInfoDto.getState());
        contactInfo.setZipCode(contactInfoDto.getZipCode());
        contactInfo.setCountry(contactInfoDto.getCountry());
        contactInfo.setLinkedInUrl(contactInfoDto.getLinkedInUrl());
        contactInfo.setPortfolioUrl(contactInfoDto.getPortfolioUrl());
        
        resume.setContactInfo(contactInfo);
        resumeRepository.save(resume);
        
        return mapToDto(contactInfo);
    }

    @Override
    public PartTimePreferenceDto updatePartTimePreference(Long resumeId, PartTimePreferenceDto partTimePreferenceDto) {
        Resume resume = getResumeEntityById(resumeId);
        
        PartTimePreference partTimePreference = resume.getPartTimePreference();
        if (partTimePreference == null) {
            partTimePreference = new PartTimePreference();
        }
        
        partTimePreference.setMinHourlyRate(partTimePreferenceDto.getMinHourlyRate());
        partTimePreference.setMaxHoursPerWeek(partTimePreferenceDto.getMaxHoursPerWeek());
        partTimePreference.setAvailableDays(partTimePreferenceDto.getAvailableDays());
        partTimePreference.setAvailableTimeSlots(partTimePreferenceDto.getAvailableTimeSlots());
        partTimePreference.setPreferredJobTypes(partTimePreferenceDto.getPreferredJobTypes());
        partTimePreference.setPreferredLocations(partTimePreferenceDto.getPreferredLocations());
        partTimePreference.setRemoteOnly(partTimePreferenceDto.getRemoteOnly());
        partTimePreference.setMaxTravelDistance(partTimePreferenceDto.getMaxTravelDistance());
        partTimePreference.setAdditionalNotes(partTimePreferenceDto.getAdditionalNotes());
        
        resume.setPartTimePreference(partTimePreference);
        resumeRepository.save(resume);
        
        return mapToDto(partTimePreference);
    }

    @Override
    public Resume getResumeEntityById(Long id) {
        return resumeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resume", "id", id));
    }

    @Override
    public List<SearchResumeResponse> getMyResumes(Boolean isActive) {
        User currentUser = userService.getCurrentLoginUser();
        List<Resume> myResume = resumeRepository.findAllByUserIdAndStatus(currentUser.getId(), isActive);

        return myResume.stream().map(SearchResumeResponse::new).toList();
    }

    @Override
    public List<SearchResumeResponse> getAllResumes() {
        List<Resume> resumes = resumeRepository.findAll();
        return resumes.stream().map(SearchResumeResponse::new).collect(Collectors.toList());
    }

    @Override
    public Pair<PaginationResponse, List<SearchResumeResponse>> searchResumes(ResumeFilter resumeFilter) {
        var resumes = resumeRepository.findAll(resumeFilter);
        List<SearchResumeResponse> responses = resumes.getContent().stream().map(SearchResumeResponse::new).toList();
        PaginationResponse paginationResponse =
                new PaginationResponse(resumeFilter.getPage(), resumeFilter.getLimit(), (int) resumes.getTotalElements());
        return Pair.of(paginationResponse, responses);
    }

    private ResumeDto mapToDto(Resume resume) {
        ResumeDto resumeDto = new ResumeDto();
        resumeDto.setId(resume.getId());
        resumeDto.setUlid(resume.getUser().getUlid());
        resumeDto.setName(resume.getName());
        resumeDto.setDescription(resume.getDescription());
        resumeDto.setUserId(resume.getUser().getId());
        resumeDto.setActive(resume.isActive());
        // Create a new HashSet to avoid shared collection references
        resumeDto.setSkills(resume.getSkills() != null ? new HashSet<>(resume.getSkills()) : new HashSet<>());
        
        if (resume.getWorkExperiences() != null) {
            resumeDto.setWorkExperiences(resume.getWorkExperiences().stream()
                    .map(this::mapToDto)
                    .collect(Collectors.toList()));
        }
        
        if (resume.getEducations() != null) {
            resumeDto.setEducations(resume.getEducations().stream()
                    .map(this::mapToDto)
                    .collect(Collectors.toList()));
        }
        
        if (resume.getContactInfo() != null) {
            resumeDto.setContactInfo(mapToDto(resume.getContactInfo()));
        }
        
        if (resume.getPartTimePreference() != null) {
            resumeDto.setPartTimePreference(mapToDto(resume.getPartTimePreference()));
        }

        if(resume.getLanguages() != null) {
            resumeDto.setLanguages(resume.getLanguages().stream()
                    .map(this::mapToDto)
                    .collect(Collectors.toList()));
        }
        
        return resumeDto;
    }

    private WorkExperienceDto mapToDto(WorkExperience workExperience) {
        WorkExperienceDto workExperienceDto = new WorkExperienceDto();
        workExperienceDto.setId(workExperience.getId());
        workExperienceDto.setCompany(workExperience.getCompany());
        workExperienceDto.setPosition(workExperience.getPosition());
        workExperienceDto.setStartDate(workExperience.getStartDate());
        workExperienceDto.setEndDate(workExperience.getEndDate());
        workExperienceDto.setDescription(workExperience.getDescription());
        workExperienceDto.setResumeId(workExperience.getResume().getId());
        return workExperienceDto;
    }

    private EducationDto mapToDto(Education education) {
        EducationDto educationDto = new EducationDto();
        educationDto.setId(education.getId());
        educationDto.setInstitution(education.getInstitution());
        educationDto.setDegree(education.getDegree());
        educationDto.setFieldOfStudy(education.getFieldOfStudy());
        educationDto.setStartDate(education.getStartDate());
        educationDto.setEndDate(education.getEndDate());
        educationDto.setDescription(education.getDescription());
        educationDto.setResumeId(education.getResume().getId());
        return educationDto;
    }

    private ContactInfoDto mapToDto(ContactInfo contactInfo) {
        ContactInfoDto contactInfoDto = new ContactInfoDto();
        contactInfoDto.setId(contactInfo.getId());
        contactInfoDto.setPhoneNumber(contactInfo.getPhoneNumber());
        contactInfoDto.setEmail(contactInfo.getEmail());
        contactInfoDto.setAddress(contactInfo.getAddress());
        contactInfoDto.setCity(contactInfo.getCity());
        contactInfoDto.setState(contactInfo.getState());
        contactInfoDto.setZipCode(contactInfo.getZipCode());
        contactInfoDto.setCountry(contactInfo.getCountry());
        contactInfoDto.setLinkedInUrl(contactInfo.getLinkedInUrl());
        contactInfoDto.setPortfolioUrl(contactInfo.getPortfolioUrl());
        contactInfoDto.setResumeId(contactInfo.getResume().getId());
        return contactInfoDto;
    }

    private PartTimePreferenceDto mapToDto(PartTimePreference partTimePreference) {
        PartTimePreferenceDto partTimePreferenceDto = new PartTimePreferenceDto();
        partTimePreferenceDto.setId(partTimePreference.getId());
        partTimePreferenceDto.setMinHourlyRate(partTimePreference.getMinHourlyRate());
        partTimePreferenceDto.setMaxHoursPerWeek(partTimePreference.getMaxHoursPerWeek());
        partTimePreferenceDto.setAvailableDays(partTimePreference.getAvailableDays());
        partTimePreferenceDto.setAvailableTimeSlots(partTimePreference.getAvailableTimeSlots());
        partTimePreferenceDto.setPreferredJobTypes(partTimePreference.getPreferredJobTypes());
        partTimePreferenceDto.setPreferredLocations(partTimePreference.getPreferredLocations());
        partTimePreferenceDto.setRemoteOnly(partTimePreference.getRemoteOnly());
        partTimePreferenceDto.setMaxTravelDistance(partTimePreference.getMaxTravelDistance());
        partTimePreferenceDto.setAdditionalNotes(partTimePreference.getAdditionalNotes());
        partTimePreferenceDto.setResumeId(partTimePreference.getResume().getId());
        return partTimePreferenceDto;
    }

    private LanguageDto mapToDto(Language language) {
        LanguageDto languageDto = new LanguageDto();
        languageDto.setLanguage(language.getName());
        languageDto.setLevel(language.getLevel());
        return languageDto;
    }
} 