package vn.flexin.backend.mono.role.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.role.dto.RoleFilter;
import vn.flexin.backend.mono.role.dto.request.CreateRoleRequest;
import vn.flexin.backend.mono.role.dto.request.UpdateRoleRequest;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.util.List;

@RequestMapping("/v1/admin/roles")
@Tag(name = "Admin Roles APIs", description = "Role management for Admin")
public interface AdminRoleController {

    @Operation(summary = "Get all roles", description = "Get a list of all roles (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved roles"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAnyAuthority('role_read')")
    ResponseEntity<PaginationApiResponseDto<List<RoleResponse>>> searchRoles(@RequestBody RoleFilter roleFilter);

    @Operation(summary = "Get role by ID", description = "Get a role by ID (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved role"),
            @ApiResponse(responseCode = "404", description = "Role not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('role_read')")
    ResponseEntity<ApiResponseDto<RoleResponse>> getRoleById(@PathVariable Long id);

    @Operation(summary = "Create role", description = "Creates a new role with specified permissions. (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role created successfully"),
            @ApiResponse(responseCode = "404", description = "Invalid role data"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    @PreAuthorize("hasAnyAuthority('role_create')")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createRole(@RequestBody CreateRoleRequest request);

    @Operation(summary = "Update role", description = "Updates an existing role with new name and/or permissions. (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role updated successfully"),
            @ApiResponse(responseCode = "404", description = "Role not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('role_update')")
    ResponseEntity<ApiResponseDto<Boolean>> updateRole(@PathVariable Long roleId, @RequestBody UpdateRoleRequest request);

    @Operation(summary = "Delete role", description = "Deletes a role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Role not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('role_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteRole(@PathVariable("roleId") Long roleId);

    @Operation(summary = "Delete roles", description = "Deletes a role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Role not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/delete-multiple")
    @PreAuthorize("hasAnyAuthority('role_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteRoles(@RequestBody List<Long> roleIds);

    @Operation(summary = "Get all roles excluded roleIds", description = "Get all roles excluded roleIds")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role fetch successfully"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/get-all")
    @PreAuthorize("hasAnyAuthority('role_read')")
    ResponseEntity<ApiResponseDto<List<RoleResponse>>> getAllRoles(@RequestBody List<Long> roleIds);

    @Operation(summary = "Get Users Assigned to Role", description = "Fetches a list of users assigned to a specific role with optional pagination.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "Role not found")
    })
    @GetMapping("/{roleId}/users")
    @PreAuthorize("hasAnyAuthority('role_read')")
    ResponseEntity<PaginationApiResponseDto<List<BasicUserInfoResponse>>> getUsersAssignedToRole(
            @PathVariable("roleId") Long roleId,
            @RequestParam(name = "page", defaultValue = "1", required = false) int page,
            @RequestParam(name = "limit",defaultValue = "10", required = false) int limit,
            @RequestParam(name = "keyword",required = false) String keyword
    );

    @Operation(summary = "Get Users Assigned to Role", description = "Fetches a list of users assigned to a specific role with optional pagination.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "Role not found")
    })
    @GetMapping("/{roleId}/available-users")
    @PreAuthorize("hasAnyAuthority('role_read')")
    ResponseEntity<ApiResponseDto<List<BasicUserInfoResponse>>> getAvailableUsersForRole(
            @PathVariable("roleId") Long roleId,
            @RequestParam(required = false) String search
    );

    @Operation(summary = "Assigned permissions to Role", description = "Assign a list of permissions to role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "Role not found")
    })
    @PostMapping("/{roleId}/permissions")
    @PreAuthorize("hasAnyAuthority('role_assign_permission')")
    ResponseEntity<ApiResponseDto<Boolean>> assignPermissionsToRole(
            @PathVariable("roleId") Long roleId,
            @RequestBody List<Long> permissionIds
    );

    @Operation(summary = "Remove permissions of Role", description = "Remove a list of permissions of role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "Role not found")
    })
    @DeleteMapping("/{roleId}/permissions")
    @PreAuthorize("hasAnyAuthority('role_unassign_permission')")
    ResponseEntity<ApiResponseDto<Boolean>> removePermissionsOfRole(
            @PathVariable("roleId") Long roleId,
            @RequestBody List<Long> permissionIds
    );
}