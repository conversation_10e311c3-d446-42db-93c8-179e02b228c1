package vn.flexin.backend.mono.role.controller.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.*;
import vn.flexin.backend.mono.role.controller.AdminRoleController;
import vn.flexin.backend.mono.role.dto.RoleFilter;
import vn.flexin.backend.mono.role.dto.request.CreateRoleRequest;
import vn.flexin.backend.mono.role.dto.request.UpdateRoleRequest;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.role.service.RoleService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
public class AdminRoleControllerImpl implements AdminRoleController {

    private final RoleService roleService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<RoleResponse>>> searchRoles(RoleFilter roleFilter) {
        var responses = roleService.searchRoles(roleFilter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Successfully get list role", responses.getLeft(), responses.getRight()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<RoleResponse>> getRoleById(Long id) {
        RoleResponse role = roleService.getDetailRole(id);
        return ResponseEntity.ok(ApiResponseDto.success(role, "Get role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createRole(CreateRoleRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(roleService.createRole(request), "Create role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateRole(Long roleId, UpdateRoleRequest request) {
        request.setId(roleId);
        roleService.updateRole(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Update role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteRole(Long id) {
        roleService.deleteRole(id);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Delete role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteRoles(List<Long> roleIds) {
        roleService.deleteRoles(roleIds);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Delete role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<RoleResponse>>> getAllRoles(List<Long> roleIds) {
        return ResponseEntity.ok(ApiResponseDto.success(roleService.getAllRoles(roleIds),"Fetch roles successfully"));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<BasicUserInfoResponse>>> getUsersAssignedToRole(Long roleId, int page, int pageSize, String search) {
        Pair<List<BasicUserInfoResponse>, PaginationResponse> response = roleService.getListUsersAssignedRole(roleId, page, pageSize, search);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Get list user successfully",response.getLeft(), response.getRight()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<BasicUserInfoResponse>>> getAvailableUsersForRole(Long roleId, String search) {
        return ResponseEntity.ok(ApiResponseDto.success(roleService.getListAvailableUsers(roleId, search), "Get list user successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        roleService.addPermissionToRole(roleId, permissionIds);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Assign permissions to role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> removePermissionsOfRole(Long roleId, List<Long> permissionIds) {
        roleService.removePermissionFromRole(roleId, permissionIds);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Remove permissions of role successfully"));

    }

}