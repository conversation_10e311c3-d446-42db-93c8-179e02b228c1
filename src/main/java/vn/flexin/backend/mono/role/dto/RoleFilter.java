package vn.flexin.backend.mono.role.dto;

import lombok.Data;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.role.entity.Role;

@Data
public class RoleFilter extends BaseFilter<Role> {

    private String keyword;

    @Override
    public Specification<Role> toSpecification() {
        Condition condition = new Condition();
        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Role.Fields.name, Operator.LIKE, keyword));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
