package vn.flexin.backend.mono.role.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleRequest {

    @NotBlank(message = "Role name can not be blank")
    public String name;

    public String description;

    @JsonProperty("isDefault")
    public boolean isDefault;

    public List<Long> permissionIds;
}
