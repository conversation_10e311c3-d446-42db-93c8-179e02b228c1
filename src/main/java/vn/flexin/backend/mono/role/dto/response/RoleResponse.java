package vn.flexin.backend.mono.role.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;
import vn.flexin.backend.mono.permission.entity.Permission;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleResponse {
    private Long id;
    private String name;
    private Set<PermissionResponse> permissions;
    private String description;
    @JsonProperty("isDefault")
    private boolean isDefault;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public RoleResponse(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public RoleResponse(Long id, @NotNull String name, List<Permission> permissions) {
        this.id = id;
        this.name = name;
        this.permissions = permissions.stream().map(PermissionResponse::new).collect(Collectors.toSet());
    }
}