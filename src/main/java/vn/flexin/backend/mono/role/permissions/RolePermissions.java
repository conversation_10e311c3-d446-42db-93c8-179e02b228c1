package vn.flexin.backend.mono.role.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum RolePermissions {
    ROLE_READ("role_read"),
    ROLE_CREATE("role_create"),
    ROLE_DELETE("role_delete"),
    ROLE_UPDATE("role_update"),
    ROLE_ASSIGN_PERMISSION("role_assign_permission"),
    ROLE_UNASSIGN_PERMISSION("role_unassign_permission"),;

    private final String value;

    RolePermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static RolePermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Role permissions type must be any of [" + getValues() + "]");
        }
    }
}

