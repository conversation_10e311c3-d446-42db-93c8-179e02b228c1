package vn.flexin.backend.mono.role.repository;

import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.role.entity.Role;

import java.util.Collection;
import java.util.List;

public interface RoleRepository extends JpaSpecificationRepository<Role, Long> {

    @Query(value = """
        SELECT CASE WHEN COUNT(*) > 0 THEN TRUE ELSE FALSE END
        FROM t_roles r
        JOIN t_roles_permissions rp ON r.id = rp.role_id
        WHERE rp.permission_id = :permissionId
    """, nativeQuery = true)
    boolean existsByPermissionsId(Long permissionId);

    boolean existsByName(String name);

    @Query(value = """
        SELECT CASE WHEN COUNT(*) > 0 THEN TRUE ELSE FALSE END
        FROM t_roles r
        JOIN t_users_roles rp ON r.id = rp.role_id
        WHERE rp.role_id in (:ids)
    """, nativeQuery = true)
    boolean isUsingByAnyUser(List<Long> ids);

    @Query("""
        SELECT role
        FROM Role role
        WHERE role.id NOT IN (:excludedIds)
    """)
    List<Role> findAllByIdNotIn(List<Long> excludedIds);

    @Query("""
        SELECT role
        FROM Role role
        LEFT JOIN FETCH role.permissions
        WHERE role.id = :id
    """)
    Role findByIdWithPermissions(Long id);
}
