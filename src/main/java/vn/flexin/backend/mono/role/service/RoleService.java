package vn.flexin.backend.mono.role.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.role.dto.RoleFilter;
import vn.flexin.backend.mono.role.dto.request.CreateRoleRequest;
import vn.flexin.backend.mono.role.dto.request.UpdateRoleRequest;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.role.entity.Role;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public interface RoleService {
    /**
     * Create a new role.
     *
     * @param roleRequest The role request containing role details.
     * @return The created role response.
     */
    CreateObjectResponse createRole(CreateRoleRequest roleRequest);

    /**
     * Update an existing role.
     *
     * @param roleRequest The role request containing updated role details.
     */
    void updateRole(UpdateRoleRequest roleRequest);

    /**
     * Delete a role by its ID.
     *
     * @param id The ID of the role to delete.
     */
    void deleteRole(Long id);

    /**
     * Get all roles with pagination.
     *
     * @return A list of role responses.
     */
    List<RoleResponse> getAllRoles(List<Long> roleIds);

    /**
     * Search roles by keyword with pagination.
     *
     * @param roleFilter The search keyword.
     * @return A page of role responses matching the search criteria.
     */
    Pair<List<RoleResponse>, PaginationResponse> searchRoles(RoleFilter roleFilter);

    /**
     * Get detailed information about a specific role.
     *
     * @param id The ID of the role.
     * @return The role response with detailed information.
     */
    RoleResponse getDetailRole(Long id);

    /**
     * Add a permission to a role.
     *
     * @param roleId The ID of the role.
     * @param permissionIds The ID of the permission to add.
     */
    void addPermissionToRole(Long roleId, List<Long> permissionIds);

    /**
     * Remove a permission from a role.
     *
     * @param roleId The ID of the role.
     * @param permissionIds The ID of the permission to remove.
     */
    void removePermissionFromRole(Long roleId, List<Long> permissionIds);


    void deleteRoles(List<Long> roleIds);

    Pair<List<BasicUserInfoResponse>, PaginationResponse> getListUsersAssignedRole(Long roleId, int page, int pageSize, String search);

    List<BasicUserInfoResponse> getListAvailableUsers(Long roleId, String search);

    Role findById(Long roleId);

    List<Role> findByIds(Iterable<Long> roleIds);

    List<String> getPermissionByRoleId(Long id);
}
