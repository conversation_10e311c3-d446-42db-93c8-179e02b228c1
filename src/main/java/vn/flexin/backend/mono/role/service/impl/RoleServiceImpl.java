package vn.flexin.backend.mono.role.service.impl;

import jakarta.persistence.EntityNotFoundException;
import jakarta.ws.rs.NotFoundException;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.role.dto.RoleFilter;
import vn.flexin.backend.mono.role.dto.request.CreateRoleRequest;
import vn.flexin.backend.mono.role.dto.request.UpdateRoleRequest;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.permission.entity.Permission;
import vn.flexin.backend.mono.role.entity.Role;
import vn.flexin.backend.mono.permission.repository.PermissionRepository;
import vn.flexin.backend.mono.role.repository.RoleRepository;
import vn.flexin.backend.mono.role.service.RoleService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class RoleServiceImpl implements RoleService {
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final AdminKeycloakService adminKeycloakService;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public CreateObjectResponse createRole(CreateRoleRequest roleRequest) {
        validateRoleName(roleRequest.name);
        Role role = mapRoleRequestToRole(roleRequest);
        String keycloakId = createRoleInKeycloak(role);
        role.setKeycloakId(keycloakId);
        Role savedRole = roleRepository.save(role);
        return new CreateObjectResponse(savedRole.getId());
    }

    @Override
    @Transactional
    public void updateRole(UpdateRoleRequest roleRequest) {
        Role existingRole = findById(roleRequest.getId());
        if (!existingRole.getName().equals(roleRequest.name)) {
            validateRoleName(roleRequest.name);
        }
        updateRoleFromRequest(existingRole, roleRequest);
        updateRoleInKeycloak(existingRole);

        roleRepository.save(existingRole);
    }

    @Override
    public Role findById(Long id) {
        return roleRepository.findById(id).orElseThrow(() -> new EntityNotFoundException(ErrorMessage.ROLE_NOT_FOUND));
    }

    @Override
    public List<Role> findByIds(Iterable<Long> roleIds) {
        return roleRepository.findAllById(roleIds);
    }

    @Override
    @Transactional
    public void deleteRole(Long id) {
        Role role = findById(id);
        if(isUsingByAnyUser(List.of(id))){
            throw new BadRequestException(ErrorMessage.ROLE_IN_USED);
        }
        deleteRoleFromKeycloak(List.of(role.getKeycloakId()));
        roleRepository.delete(role);
    }

    private boolean isUsingByAnyUser(List<Long> ids) {
        return roleRepository.isUsingByAnyUser(ids);
    }

    public List<RoleResponse> getAllRoles(List<Long> excludedIds) {
        return roleRepository.findAllByIdNotIn(excludedIds).stream().map(this::mapRoleToRoleResponse).toList();
    }

    @Override
    public Pair<List<RoleResponse>, PaginationResponse> searchRoles(RoleFilter roleFilter) {
        var roles = roleRepository.findAll(roleFilter);
        var responses = roles.stream().map(this::mapRoleToRoleResponse).toList();
        PaginationResponse paginationResponse = new PaginationResponse(roleFilter.getLimit(), roleFilter.getPage(),(int) roles.getTotalElements());
        return Pair.of(responses, paginationResponse);
    }

    public RoleResponse getDetailRole(Long id) {
        Role role = findById(id);
        return mapRoleToRoleResponse(role);
    }
    @Override
    @Transactional
    public void addPermissionToRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        List<Permission> permissionToAdds = permissionRepository.findAllById(permissionIds);
        List<Permission> permissions = role.getPermissions();
        if (CollectionUtils.isEmpty(permissions)) {
            permissions = new ArrayList<>();
        }
        permissions.addAll(permissionToAdds);
        role.setPermissions(permissions);
        roleRepository.save(role);
        List<String> permissionKeyCloakIds = permissionToAdds.stream().map(Permission::getKeycloakId).toList();
        updateRolePermissionsInKeycloak(role.getKeycloakId(), permissionKeyCloakIds, Boolean.TRUE);
    }

    @Override
    @Transactional
    public void removePermissionFromRole(Long roleId, List<Long> permissionIds) {
        Role role = findById(roleId);
        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        role.getPermissions().removeAll(permissions);
        roleRepository.save(role);
        List<String> permissionKeyCloakIds = permissions.stream().map(Permission::getKeycloakId).toList();
        updateRolePermissionsInKeycloak(role.getKeycloakId(), permissionKeyCloakIds, Boolean.FALSE);
    }

    @Override
    public void deleteRoles(List<Long> roleIds) {
        List<Role> roles = roleRepository.findAllById(roleIds);
        if(isUsingByAnyUser(roleIds)){
            throw new BadRequestException(ErrorMessage.ROLE_IN_USED);
        }
        List<String> keycloakIds = roles.stream().map(Role::getKeycloakId).toList();
        deleteRoleFromKeycloak(keycloakIds);
    }

    @Override
    public Pair<List<BasicUserInfoResponse>, PaginationResponse> getListUsersAssignedRole(Long roleId, int page, int limit, String keyword) {
        Pageable pageable = PageRequest.of(page , limit);
        Page<User> userPage = userRepository.getUsersAssignedToRole(roleId, keyword, pageable);

        List<BasicUserInfoResponse> userResponses = userPage.getContent().stream().map(BasicUserInfoResponse::new).toList();

        PaginationResponse paginationResponse = new PaginationResponse(limit, page, (int) userPage.getTotalElements());
        return Pair.of(userResponses, paginationResponse);
    }

    @Override
    public List<BasicUserInfoResponse> getListAvailableUsers(Long roleId, String search) {
        List<User> users = userRepository.findUsersNotAssignedRole(roleId);

        return users.stream().map(BasicUserInfoResponse::new).toList();
    }

    private void validateRoleName(String name) {
        if (roleRepository.existsByName(name)) {
            throw new IllegalArgumentException(ErrorMessage.ROLE_ALREADY_EXISTED);
        }
    }

    private Role mapRoleRequestToRole(CreateRoleRequest roleRequest) {
        Role role = new Role();
        role.setName(roleRequest.name);
        role.setDefault(roleRequest.isDefault);
        if (roleRequest.permissionIds != null && !roleRequest.permissionIds.isEmpty()) {
            List<Permission> permissions = permissionRepository.findAllById(roleRequest.permissionIds);
            role.setPermissions(permissions);
        }
        return role;
    }

    private void updateRoleFromRequest(Role role, UpdateRoleRequest roleRequest) {
        role.setName(roleRequest.name);
        role.setDefault(roleRequest.isDefault);
        if (roleRequest.permissionIds != null) {
            List<Permission> permissions = permissionRepository.findAllById(roleRequest.permissionIds);
            role.setPermissions(permissions);
        }
    }

    private RoleResponse mapRoleToRoleResponse(Role role) {
        RoleResponse response = new RoleResponse();
        response.setId(role.getId());
        response.setName(role.getName());
        response.setDefault(role.isDefault());
        response.setCreatedAt(role.getCreatedAt());
        response.setUpdatedAt(role.getLastModifiedAt());
        if (role.getPermissions() != null) {
            Set<PermissionResponse> permissionResponses = role.getPermissions().stream()
                    .map(PermissionResponse::new)
                    .collect(Collectors.toSet());
            response.setPermissions(permissionResponses);
        }
        return response;
    }

    private String createRoleInKeycloak(Role role) {
        try {
            return adminKeycloakService.createRole(role);
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_CREATE_ROLE);
        }
    }

    private void updateRoleInKeycloak(Role role) {
        try {
            adminKeycloakService.updateRole(role);
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_UPDATE_ROLE);
        }
    }

    private void deleteRoleFromKeycloak(List<String> keycloakIds) {
        try {
            for (String keycloakId : keycloakIds) {
                adminKeycloakService.deleteRole(keycloakId);
            }
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_DELETE_ROLE);
        }
    }

    private void updateRolePermissionsInKeycloak(String roleId, List<String> permissionIds, boolean isAdd) {
        if (isAdd) {
            try {
                adminKeycloakService.addPermissionsToRole(roleId, permissionIds);
            } catch (NotFoundException ex) {
                throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
            } catch (BadRequestException e) {
                throw new BadRequestException(e.getMessage());
            } catch (Exception e) {
                throw new BadRequestException(ErrorMessage.CAN_NOT_UPDATE_ROLE);
            }
        } else {
            try {
                adminKeycloakService.removePermissionsOfRole(roleId, permissionIds);
            } catch (NotFoundException ex) {
                throw new ResourceNotFoundException(ErrorMessage.ROLE_NOT_FOUND);
            } catch (BadRequestException e) {
                throw new BadRequestException(e.getMessage());
            } catch (Exception e) {
                throw new BadRequestException(ErrorMessage.CAN_NOT_DELETE_ROLE);
            }
        }
    }

    @Override
    public List<String> getPermissionByRoleId(Long id) {
        Role role = findByIdWithPermissions(id);
        if(!CollectionUtils.isEmpty(role.getPermissions())) {
            return role.getPermissions().stream().map(Permission::getName).toList();
        }
        return new ArrayList<>();
    }

    private Role findByIdWithPermissions(Long id) {
        return roleRepository.findByIdWithPermissions(id);
    }
}
