package vn.flexin.backend.mono.user.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.user.dto.CreateUserRequest;
import vn.flexin.backend.mono.user.dto.UserDto;
import vn.flexin.backend.mono.user.dto.UserFilter;

import java.util.List;

@RequestMapping("/v1/admin/users")
@Tag(name = "Admin User APIs", description = "Admin user management endpoints")
public interface AdminUserController {
    
    @Operation(summary = "Get all users", description = "Get a list of all users (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved users"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping
    @PreAuthorize("hasAuthority('user_read')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<UserDto>>> getAllUsers();
    
    @Operation(summary = "Search and filter users", description = "Search and filter users with pagination and sorting (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved users"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('user_read')")
    ResponseEntity<PaginationApiResponseDto<List<UserDto>>> searchUsers(
            @Valid @RequestBody UserFilter searchUserRequest);
    
    @Operation(summary = "Get user by ID", description = "Get a user by their ID (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('user_read')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> getUserById(@PathVariable Long id);
    
    @Operation(summary = "Create user", description = "Create a new user (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully created user"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('user_create')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> createUser(@Valid @RequestBody CreateUserRequest userDto);
    
    @Operation(summary = "Update user", description = "Update a user's information (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('user_update')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> updateUser(@PathVariable Long id, @Valid @RequestBody UserDto userDto);
    
    @Operation(summary = "Delete user", description = "Delete a user (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('user_delete')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteUser(@PathVariable Long id);
    
    @Operation(summary = "Change user status", description = "Activate or deactivate a user (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully changed user status"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasAuthority('user_update')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> changeUserStatus(
            @PathVariable Long id, 
            @RequestParam boolean active);

    @Operation(summary = "Assign role to user", description = "Assign a specific role to user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully assigned role to user"),
            @ApiResponse(responseCode = "404", description = "User not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{userId}/role")
    @PreAuthorize("hasAuthority('user_assign_role')")
    ResponseEntity<ApiResponseDto<Boolean>> assignRoleToUser(
            @PathVariable("userId") Long userId,
            @RequestBody List<Long> roleIds);

    @Operation(summary = "Remove role of user", description = "Remove a specific role of user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully removed role of user"),
            @ApiResponse(responseCode = "404", description = "User not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{userId}/role/{roleId}")
    @PreAuthorize("hasAuthority('user_remove_role')")
    ResponseEntity<ApiResponseDto<Boolean>> removeRoleOfUser(
            @PathVariable("userId") Long userId,
            @PathVariable("roleId") Long roleId);
} 