package vn.flexin.backend.mono.user.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.user.dto.CreateUserRequest;
import vn.flexin.backend.mono.user.dto.UserDto;
import vn.flexin.backend.mono.user.dto.CreateMobileUserRequest;
import vn.flexin.backend.mono.user.dto.SearchUserRequest;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Tag(name = "Mobile User APIs", description = "Mobile user management endpoints")
@RequestMapping("/v1/mobile/auth")
public interface MobileUserController {
    
    @Operation(summary = "Register a new mobile user", description = "Create a new user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully registered user"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "409", description = "User already exists")
    })
    @PostMapping("/register")
    ResponseEntity<User> createUser(@RequestBody @Valid CreateMobileUserRequest createUserRequest);
    
    @Operation(summary = "Get all users", description = "Get a list of all users (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved users"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<UserDto>>> getAllUsers();
    
    @Operation(summary = "Get user by ID", description = "Get a user by their ID (Admin or owner)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isCurrentUser(#id)")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> getUserById(@PathVariable Long id);
    
    @Operation(summary = "Update user", description = "Update a user's information (Admin or owner)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userSecurity.isCurrentUser(#id)")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<UserDto>> updateUser(@PathVariable Long id, @Valid @RequestBody CreateUserRequest userDto);
    
    @Operation(summary = "Delete user", description = "Delete a user (Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted user"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteUser(@PathVariable Long id);
    
    @Operation(summary = "Search and filter users", description = "Search and filter users with pagination and sorting")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved users"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EMPLOYER')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Page<UserDto>>> searchUsers(
            @Valid @RequestBody SearchUserRequest searchUserRequest);

    @Operation(summary = "Accept joining company invitation", description = "User accept joining company invitation from employer or branch manager")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully joining company"),
            @ApiResponse(responseCode = "404", description = "User not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/accept-invitation/{staffId}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> acceptJoiningInvitation(@PathVariable("staffId") Long id);

    @Operation(summary = "Reject joining company invitation", description = "User reject joining company invitation from employer or branch manager")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully reject invitation joining company"),
            @ApiResponse(responseCode = "404", description = "User not found"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/reject-invitation/{staffId}")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> rejectJoiningInvitation(@PathVariable("staffId") Long id);

} 