package vn.flexin.backend.mono.user.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;

@Data
@NoArgsConstructor
public class ContactInfoDto {
    private Long id;
    
    private String phoneNumber;
    
    @Email(message = "Email should be valid")
    private String email;
    
    private String address;
    
    private String city;
    
    private String state;
    
    private String zipCode;
    
    private String country;
    
    private String linkedInUrl;
    
    private String portfolioUrl;
    
    private Long resumeId;
} 