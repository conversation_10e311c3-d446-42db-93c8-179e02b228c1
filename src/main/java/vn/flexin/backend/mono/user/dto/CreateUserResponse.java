package vn.flexin.backend.mono.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class CreateUserResponse {
    @JsonProperty("userId")
    private Long id;
    private String ulid;
    private String phone;
    private String role;
    private String name;
    private String avatarUrl;
    private String email;
    @JsonProperty("isEmailVerified")
    private boolean isEmailVerified;
    @JsonProperty("isPhoneVerified")
    private boolean isPhoneVerified;
    private LocalDateTime createdAt;

    public CreateUserResponse(User user) {
        this.id = user.getId();
        this.ulid = user.getUlid();
        this.phone = user.getPhoneNumber();
        this.role = user.getRole();
        this.name = user.getName();
        this.avatarUrl = user.getProfilePicture();
        this.email = user.getEmail();
        this.isEmailVerified = user.isMailVerified();
        this.isPhoneVerified = user.isPhoneVerified();
        this.createdAt = user.getCreatedAt();
    }
}
