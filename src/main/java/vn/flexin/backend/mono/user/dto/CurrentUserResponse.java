package vn.flexin.backend.mono.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = false)
public class CurrentUserResponse {
    @JsonProperty("id")
    private Long id;
    private String ulid;
    private String email;
    private String name;
    private String avatarUrl;
    private String role;
    private List<String> permissions;
    private LocalDateTime createdAt;

    public CurrentUserResponse(UserDto user) {
        this.id = user.getId();
        this.ulid = user.getUlid();
        this.role = user.getRole();
        this.permissions = user.getRoles().stream()
                .flatMap(role -> role.getPermissions().stream().map(PermissionResponse::getName))
                .collect(Collectors.toList());
        this.name = user.getName();
        this.avatarUrl = user.getProfilePicture();
        this.email = user.getEmail();
        this.createdAt = user.getCreatedAt();
    }
}
