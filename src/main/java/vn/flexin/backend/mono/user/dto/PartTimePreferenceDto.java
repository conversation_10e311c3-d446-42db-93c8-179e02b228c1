package vn.flexin.backend.mono.user.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
public class PartTimePreferenceDto {
    private Long id;
    
    private Double minHourlyRate;
    
    private Integer maxHoursPerWeek;
    
    private Set<String> availableDays = new HashSet<>();
    
    private Set<String> availableTimeSlots = new HashSet<>();
    
    private Set<String> preferredJobTypes = new HashSet<>();
    
    private Set<String> preferredLocations = new HashSet<>();
    
    private Boolean remoteOnly = false;
    
    private Integer maxTravelDistance;
    
    private String additionalNotes;
    
    private Long resumeId;

    private Boolean isStudent;

    private String studyMajor;
} 