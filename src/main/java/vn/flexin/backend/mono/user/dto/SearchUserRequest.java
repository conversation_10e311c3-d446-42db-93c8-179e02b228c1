package vn.flexin.backend.mono.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import vn.flexin.backend.mono.common.dto.SearchRequest;

import jakarta.validation.Valid;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchUserRequest extends SearchRequest {
    private List<@Valid UserFilter> filters;
    
    private UserSortField sortBy;
} 