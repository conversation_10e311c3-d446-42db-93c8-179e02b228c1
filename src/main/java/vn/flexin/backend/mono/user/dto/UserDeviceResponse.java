package vn.flexin.backend.mono.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class UserDeviceResponse {
    private Long id;
    private String deviceName;
    private String deviceId;
    private LocalDateTime lastActivity;
    private String ipAddress;
    private String location;
    @JsonProperty("isCurrentDevice")
    private boolean isCurrentDevice;
}
