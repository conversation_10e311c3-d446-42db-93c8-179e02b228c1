package vn.flexin.backend.mono.user.entity;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import vn.flexin.backend.mono.user.enums.OtpEntryStatus;
import vn.flexin.backend.mono.user.enums.TokenPurpose;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_otp_entries")
public class OtpEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @NotNull
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_device_id")
    private UserDevice userDevice;

    @NotBlank
    private String phoneNumber;

    @NotBlank
    private String otp;

    private LocalDateTime createdAt;

    private LocalDateTime expiredAt;

    @Enumerated(EnumType.STRING)
    private OtpEntryStatus status;

    @Enumerated(EnumType.STRING)
    private TokenPurpose tokenPurpose;

    public OtpEntry(String otp, UserDevice userDevice, TokenPurpose tokenPurpose) {
        this.userDevice = userDevice;
        this.phoneNumber = userDevice.getPhoneNumber();
        this.createdAt = LocalDateTime.now();
        this.expiredAt = this.createdAt.plusMinutes(10);
        this.status = OtpEntryStatus.UNUSED;
        this.tokenPurpose = tokenPurpose;
        this.otp = otp;
    }
}
