package vn.flexin.backend.mono.user.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import vn.flexin.backend.mono.auth.dto.LoginRequest;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.common.util.CommonUtil;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "t_user_devices",
        uniqueConstraints = @UniqueConstraint(
                columnNames = {"deviceId","phoneNumber"}
        )
)
@Getter
@Setter
@RequiredArgsConstructor
@Builder
@AllArgsConstructor
public class UserDevice extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    private String deviceId;

    @NotBlank
    private String deviceName;

    @NotBlank
    private String phoneNumber;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "userId")
    private User user;

    private boolean isPhoneVerified = false;

    private LocalDateTime phoneVerifiedAt;

    private boolean isLogout = true;

    private boolean isSignUpComplete = false;

    private LocalDateTime signUpCompletedAt;

    private String tempPassword;

    private boolean isDeviceVerified;

    private LocalDateTime deviceVerifiedAt;

    private String clientIp;

    private String location;

    private String deviceToken;

    public UserDevice(String deviceId, String phoneNumber, String deviceName, String clientIp) {
        this.deviceId = deviceId;
        this.phoneNumber = phoneNumber;
        this.deviceName = deviceName == null ? "Unknown Device" : deviceName;
        this.clientIp = clientIp;
        this.isPhoneVerified = false;
        this.isSignUpComplete = false;
    }

    public UserDevice (LoginRequest request, User user, String clientIp) {
        this.setDeviceId(request.getDeviceId());
        this.setDeviceName(request.getDeviceName());
        this.setPhoneNumber(request.getPhone());
        this.setUser(user);
        this.setClientIp(clientIp);
        this.setPhoneVerified(true);
        this.setDeviceVerified(true);
        this.setSignUpComplete(true);
        this.setLogout(false);
        this.setLocation(request.getLocation());

        LocalDateTime now = CommonUtil.getCurrentUTCTime();
        this.setDeviceVerifiedAt(now);
        this.setPhoneVerifiedAt(now);
        this.setSignUpCompletedAt(now);
    }
}
