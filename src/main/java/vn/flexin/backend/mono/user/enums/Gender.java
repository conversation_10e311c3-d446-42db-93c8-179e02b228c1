package vn.flexin.backend.mono.user.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum Gender {
    UNKNOWN("Unknown"),
    MALE("Male"),
    FEMALE("Female"),
    OTHER("Other");

    private final String value;

    Gender(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static Gender fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Gender must be any of [" + getValues() + "]");
        }
    }

}
