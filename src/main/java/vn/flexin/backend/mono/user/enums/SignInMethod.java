package vn.flexin.backend.mono.user.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum SignInMethod {
    UNKNOWN("Unknown"),
    MOBILE("Mobile"),
    GOOGLE("Google"),
    APPLE("Apple");

    private final String label;

    SignInMethod(String label) {
        this.label = label;
    }

    public String label() {
        return label;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static SignInMethod fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "SignInMethod must be any of [" + getValues() + "]");
        }
    }
}
