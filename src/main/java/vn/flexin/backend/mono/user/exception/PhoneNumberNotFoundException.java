package vn.flexin.backend.mono.user.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class PhoneNumberNotFoundException extends RuntimeException{
    private static final long serialVersionUID = 1L;

    public PhoneNumberNotFoundException(String message) {
        super(String.format("User with phone number %s not found.", message));
    }

}
