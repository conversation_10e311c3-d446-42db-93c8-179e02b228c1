package vn.flexin.backend.mono.user.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.user.entity.OtpEntry;

import java.time.LocalDateTime;

public interface OtpEntryRepository extends JpaRepository<OtpEntry, String> {

    @Query("""
        SELECT o FROM OtpEntry o
        JOIN FETCH o.userDevice userDevice
        WHERE o.phoneNumber = :phone
            AND userDevice.deviceId = :deviceId
            AND o.status = 'UNUSED'
        ORDER BY o.createdAt DESC LIMIT 1
    """)
    OtpEntry findLatestByPhoneNumberAndDeviceId(@Param("phone") String phone,
                                                @Param("deviceId") String deviceId);

    @Query("""
        SELECT COUNT(*) FROM OtpEntry o
            WHERE o.phoneNumber = :phone
                AND o.createdAt > :frameTime
    """)
    int countOtpByPhoneNumberInFrameTime(@Param("phone") String phone,
                                         @Param("frameTime") LocalDateTime frameTime);
}
