package vn.flexin.backend.mono.user.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.user.entity.UserDevice;

import java.util.List;

public interface UserDeviceRepository extends JpaRepository<UserDevice, Long> {

    UserDevice findFirstByDeviceIdAndPhoneNumber(String deviceId, String phoneNumber);

    @Modifying
    @Query(value = "UPDATE UserDevice u SET u.isLogout = TRUE WHERE u.user.id = :userId AND u.user.isActive = TRUE AND u.deviceId <> :deviceId")
    void logoutAllDevices(@Param("userId") Long userId,
                          @Param("deviceId")  String deviceId);

    @Modifying
    @Query(value = "UPDATE UserDevice u SET u.isLogout = TRUE WHERE u.user.id = :userId AND u.user.isActive = TRUE AND u.deviceId = :deviceId ")
    void logoutDevice(@Param("userId") Long userId,
                      @Param("deviceId")  String deviceId);

    @Query(value = """
            SELECT u FROM UserDevice u WHERE u.user.id = :userId
       """)
    List<UserDevice> finAllByUserId(Long userId);

    @Query(value = """
        SELECT d FROM UserDevice d
        JOIN FETCH d.user u
        WHERE d.deviceId = :deviceId
        AND u.ulid = :ulid
        AND u.isActive IS TRUE
        ORDER BY d.id DESC
        LIMIT 1
    """)
    UserDevice findFirstByUserUlidAndDeviceId(@Param("ulid") String ulid,
                                              @Param("deviceId") String deviceId);

    @Query("""
        SELECT userDevice
        FROM UserDevice userDevice
        WHERE userDevice.phoneNumber = :phoneNumber
    """)
    List<UserDevice> findAllByPhoneNumber(String phoneNumber);
}
