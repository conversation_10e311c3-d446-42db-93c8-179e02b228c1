package vn.flexin.backend.mono.user.repository.user;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface UserRepository extends JpaSpecificationRepository<User, Long> {
    Optional<User> findByEmail(String email);
    Boolean existsByEmail(String email);
    Optional<User> findByPhoneNumber(String phoneNumber);

    @Query(value = "SELECT u FROM User u WHERE LOWER(u.ulid) = LOWER(:ulid) AND u.isActive IS TRUE")
    User findByUlid(String ulid);

    @Query(value = "SELECT u FROM User u WHERE u.ulid IN :ulids AND u.isActive IS TRUE")
    List<User> findByUlidIn(@Param("ulids") List<String> ulids);

    @Query(value = "SELECT u FROM User u WHERE u.ulid NOT IN :ulids AND u.isActive IS TRUE")
    List<User> findUserExcludeUlids(@Param("ulids") List<String> ulids);

    @Query(value = "SELECT u FROM User u WHERE u.keycloakUserId IN :userIds AND u.isActive IS TRUE")
    List<User> findByKeycloakIdIn(@Param("userIds") List<String> userIds);

    @Query(value = "SELECT u.* " +
            "FROM t_users u " +
            "INNER JOIN t_users_roles ur ON u.id = ur.user_id " +
            "WHERE ur.role_id = :roleId " +
            "AND (u.name LIKE %:keyword% OR u.email LIKE %:keyword%) " +
            "ORDER BY u.created_at DESC",
            countQuery = "SELECT COUNT(*) " +
                    "FROM t_users u " +
                    "INNER JOIN t_users_roles ur ON u.id = ur.user_id " +
                    "WHERE ur.role_id = :roleId " +
                    "AND (u.name LIKE %:keyword% OR u.email LIKE %:keyword%)",
            nativeQuery = true)
    Page<User> getUsersAssignedToRole(@Param("roleId") Long roleId,
                                      @Param("keyword") String keyword,
                                      Pageable pageable);

    @Query(value = """
        SELECT u.*
        FROM t_users u
        WHERE u.id NOT IN
            (
                SELECT ur.user_id
                FROM t_users_roles ur WHERE ur.role_id = :roleId
            )
    """, nativeQuery = true)
    List<User> findUsersNotAssignedRole(Long roleId);

    @Query("""
        SELECT u
        FROM User u
        LEFT JOIN FETCH u.roles
        WHERE u.keycloakUserId = :keycloakId
    """)
    User findByKeycloakId(String keycloakId);
}