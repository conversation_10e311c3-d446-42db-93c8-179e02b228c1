package vn.flexin.backend.mono.user.service;

import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;

import java.util.List;

public interface UserDeviceService {
    UserDevice save(UserDevice userDevice);

    UserDevice getByDeviceIdAndPhoneNumber(String deviceId, String phoneNumber);

    void updateUserDeviceAfterUserFullySignUp(User newUser, UserDevice userDevice);

    void logoutAllDeviceExclude(Long userId, String deviceId);

    void logoutDevice(Long userId, String deviceId);

    List<UserDevice> getByUserId(Long userId);

    List<UserDevice> getByPhoneNumber(String phoneNumber);
}
