package vn.flexin.backend.mono.user.service;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import vn.flexin.backend.mono.auth.dto.SelectRoleRequest;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.user.dto.*;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;

import java.util.List;

public interface UserService {
    UserDto createUser(CreateUserRequest userDto);
    User createUser(CreateMobileUserRequest createUserRequest);
    UserDto getUserById(Long id);
    UserDto getUserByEmail(String email);
    User getAdminByEmail(String email);
    User getUserByPhoneNumber(String phoneNumber);
    List<UserDto> getAllUsers();
    UserDto updateUser(Long id, UserDto userDto);
    UserDto updateUserByAdmin(Long id, UserDto userDto);
    void deleteUser(Long id);

    boolean existsByEmail(String email);

    User getUserEntityById(Long id);

    void updateUser(UpdateUserRequest updateUserRequest);

    List<UserDeviceResponse> getUserDevices(Long userId, String deviceId);
    Pair<List<UserDto>, PaginationResponse> searchUsers(UserFilter searchUserRequest);

    User createNewMobileUser(UserDevice userDevice, SelectRoleRequest request);

    void save(User user);

    User getByUlidNullable(String ulid);

    User getUserByUlid(String ulid);

    User getById(Long id);

    User getByIdNullable(Long id);

    User getCurrentLoginUser();

    void acceptJoiningInvitation(Long staffId);

    void rejectJoiningInvitation(Long staffId);
    List<User> getUserByUlids(List<String> userUlids);

    List<User> getUserExcludeUlids(List<String> userUlids);

    void assignRoleToUser(Long userId, List<Long> roleIds);

    void removeRoleOfUser(Long userId, Long roleId);

    List<User> getUserByKeycloakIds(List<String> userIds);

    User getByKeycloakId(String keycloakId);
}

