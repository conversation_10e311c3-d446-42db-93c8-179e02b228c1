package vn.flexin.backend.mono.user.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.user.entity.OtpEntry;
import vn.flexin.backend.mono.user.repository.OtpEntryRepository;
import vn.flexin.backend.mono.user.service.OtpEntryService;

import java.time.LocalDateTime;

@Service
@AllArgsConstructor
public class OtpEntryServiceImpl implements OtpEntryService {

    private final OtpEntryRepository otpEntryRepository;

    public OtpEntry save(OtpEntry otpEntry) {
        return otpEntryRepository.save(otpEntry);
    }

    public OtpEntry getLatestForVerification(String phone, String deviceId) {
        return otpEntryRepository.findLatestByPhoneNumberAndDeviceId(phone, deviceId);
    }

    @Override
    public int countOtpByPhoneNumberInFrameTime(String phone, LocalDateTime frameTime) {
        return otpEntryRepository.countOtpByPhoneNumberInFrameTime(phone, frameTime);
    }
}
