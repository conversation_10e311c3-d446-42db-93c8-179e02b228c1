package vn.flexin.backend.mono.user.service.impl;

import com.github.f4b6a3.ulid.UlidCreator;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.auth.dto.SelectRoleRequest;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.company.entity.Staff;
import vn.flexin.backend.mono.company.service.mobile.StaffService;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.payment.repository.personal.PersonalWalletRepository;
import vn.flexin.backend.mono.payment.service.personal.MobilePersonalWalletService;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.role.entity.Role;
import vn.flexin.backend.mono.role.service.RoleService;
import vn.flexin.backend.mono.user.dto.*;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;
import vn.flexin.backend.mono.user.repository.UserDeviceRepository;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
@AllArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserDeviceRepository userDeviceRepository;
    private final StaffService staffService;
    private final UserKeycloakService userKeycloakService;
    private final AdminKeycloakService adminKeycloakService;
    private final RoleService roleService;
    private final AddressService addressService;
    private final PersonalWalletRepository personalWalletRepository;


    @Override
    public UserDto createUser(CreateUserRequest userDto) {
        // Check if email already exists
        if (Boolean.TRUE.equals(userRepository.existsByEmail(userDto.getEmail()))) {
            throw new BadRequestException("Email is already taken!");
        }

        // Create new user
        User user = new User();
        user.setName(userDto.getName());
        user.setEmail(userDto.getEmail());
        user.setGender(userDto.getGender());
        
        // Parse dateOfBirth string to LocalDate
        LocalDate dateOfBirth = parseDateOfBirth(userDto.getDateOfBirth());
        if (dateOfBirth != null) {
            user.setDateOfBirth(dateOfBirth);
        }
        
        user.setPassword(passwordEncoder.encode(userDto.getPassword()));
        user.setRole(userDto.getRole());
        user.setPhoneNumber(userDto.getPhoneNumber());
        user.setProfilePicture(userDto.getProfilePicture());
        user.setActive(userDto.isActive());
        user.setUlid(UlidCreator.getUlidString());
        
        // Set isAdminPortal if provided
        if (userDto.getIsAdminPortal() != null) {
            user.setIsAdminPortal(userDto.getIsAdminPortal());
        }

        if (userDto.getAddress() != null) {
            Address address = addressService.createAddress(userDto.getAddress());
            user.setAddress(address);
        }

        List<Role> roles = roleService.findByIds(userDto.getRoleIds());
        user.setRoles(new HashSet<>(roles));
        User savedUser = userRepository.save(user);

        ResponseEntity<String> response =
                userKeycloakService.createNormalUser(user.getUlid(), user.getPhoneNumber(), null, userDto.getPassword(), user.getEmail());
        String keycloakUserId = response.getBody();
        List<String> roleKeycloakIds = roles.stream().map(Role::getKeycloakId).toList();

        if (keycloakUserId != null) {
            savedUser.setKeycloakUserId(keycloakUserId);
            save(savedUser);
        }

        adminKeycloakService.assignRoleToUser(user.getKeycloakUserId(), roleKeycloakIds);

        createWalletForUser(user);

        return mapToDto(savedUser);
    }

    private void createWalletForUser(User user) {
        PersonalWallet wallet = new PersonalWallet();
        wallet.setUser(user);
        wallet.setPoint(0L);

        personalWalletRepository.save(wallet);
    }

    @Override
    public UserDto getUserById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
        return mapToDto(user);
    }

    @Override
    public UserDto getUserByEmail(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
        return mapToDto(user);
    }

    @Override
    public User getAdminByEmail(String email) {
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
    }


    @Override
    public List<UserDto> getAllUsers() {
        List<User> users = userRepository.findAll();
        return users.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public UserDto updateUser(Long id, UserDto userDto) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));

        // Update user fields
        user.setName(userDto.getName());
        
        // Update dateOfBirth if provided
        LocalDate dateOfBirth = parseDateOfBirth(userDto.getDateOfBirth());
        if (dateOfBirth != null) {
            user.setDateOfBirth(dateOfBirth);
        }
        
        // Update gender if provided
        if (userDto.getGender() != null) {
            user.setGender(userDto.getGender());
        }
        
        // Update email if provided
        if (userDto.getEmail() != null) {
            user.setEmail(userDto.getEmail());
        }
        
        // Update role if provided
        if (userDto.getRole() != null) {
            user.setRole(userDto.getRole());
        }

        user.setPhoneNumber(userDto.getPhoneNumber());
        user.setProfilePicture(userDto.getProfilePicture());
        user.setActive(userDto.isActive());

        // NOTE: isAdminPortal field is NOT updated here to prevent mobile access
        // Only admin can update this field via updateUserByAdmin method

        // Update address if provided
        if (userDto.getAddress() != null) {
            if (user.getAddress() != null) {
                // Update existing address
                userDto.getAddress().setId(user.getAddress().getId());
                user.setAddress(addressService.updateAddress(userDto.getAddress()));
            } else {
                // Create new address
                user.setAddress(addressService.createAddress(userDto.getAddress()));
            }
        }

        // Update roles if provided
        if (userDto.getRoleIds() != null && !userDto.getRoleIds().isEmpty()) {
            List<Role> roles = roleService.findByIds(userDto.getRoleIds());
            user.setRoles(new HashSet<>(roles));
            
            List<String> roleKeycloakIds = roles.stream().map(Role::getKeycloakId).toList();
            adminKeycloakService.updateRoleOfUser(user.getKeycloakUserId(), roleKeycloakIds);
        }

        User updatedUser = userRepository.save(user);
        return mapToDto(updatedUser);
    }

    @Override
    public UserDto updateUserByAdmin(Long id, UserDto userDto) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));

        // Update user fields (same as updateUser but includes isAdminPortal)
        user.setName(userDto.getName());
        
        // Update dateOfBirth if provided
        LocalDate dateOfBirth = parseDateOfBirth(userDto.getDateOfBirth());
        if (dateOfBirth != null) {
            user.setDateOfBirth(dateOfBirth);
        }
        
        // Update gender if provided
        if (userDto.getGender() != null) {
            user.setGender(userDto.getGender());
        }
        
        // Update email if provided
        if (userDto.getEmail() != null) {
            user.setEmail(userDto.getEmail());
        }
        
        // Update role if provided
        if (userDto.getRole() != null) {
            user.setRole(userDto.getRole());
        }

        user.setPhoneNumber(userDto.getPhoneNumber());
        user.setProfilePicture(userDto.getProfilePicture());
        user.setActive(userDto.isActive());
        
        // ADMIN ONLY: Update isAdminPortal
        if (userDto.getIsAdminPortal() != null) {
            user.setIsAdminPortal(userDto.getIsAdminPortal());
        }

        // Update address if provided
        if (userDto.getAddress() != null) {
            if (user.getAddress() != null) {
                // Update existing address
                userDto.getAddress().setId(user.getAddress().getId());
                user.setAddress(addressService.updateAddress(userDto.getAddress()));
            } else {
                // Create new address
                user.setAddress(addressService.createAddress(userDto.getAddress()));
            }
        }

        // Update roles if provided
        if (userDto.getRoleIds() != null && !userDto.getRoleIds().isEmpty()) {
            List<Role> roles = roleService.findByIds(userDto.getRoleIds());
            user.setRoles(new HashSet<>(roles));
            
            List<String> roleKeycloakIds = roles.stream().map(Role::getKeycloakId).toList();
            adminKeycloakService.updateRoleOfUser(user.getKeycloakUserId(), roleKeycloakIds);
        }

        User updatedUser = userRepository.save(user);
        return mapToDto(updatedUser);
    }

    @Override
    public void deleteUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
        userRepository.delete(user);
        userKeycloakService.deleteUser(user.getKeycloakUserId());
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public User getUserEntityById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
    }

    @Override
    public User createUser(CreateMobileUserRequest createUserRequest) {
        // Đảm bảo name và email luôn có giá trị
        String name = createUserRequest.getFullName();
        if (name == null || name.trim().isEmpty()) {
            name = "User_" + createUserRequest.getPhoneNumber();
        }
        
        String email = createUserRequest.getEmail();
        if (email == null || email.trim().isEmpty()) {
            email = createUserRequest.getPhoneNumber() + "@flexin.vn";
        }
        
        User user = User.builder()
                .name(name)
                .email(email)
                .phoneNumber(createUserRequest.getPhoneNumber())
                .password(passwordEncoder.encode(createUserRequest.getPassword()))
                .role("JOB_SEEKER")
                .isActive(true)
                .build();
        return userRepository.save(user);
    }
    
    @Override
    public User getUserByPhoneNumber(String phoneNumber) {
        return userRepository.findByPhoneNumber(phoneNumber).orElse(null);
    }
    
    @Override
    public void updateUser(UpdateUserRequest updateUserRequest) {
        User user = getUserEntityById(Long.parseLong(updateUserRequest.getUserId()));
        if (updateUserRequest.getFullName() != null) {
            user.setName(updateUserRequest.getFullName());
        }
        if (updateUserRequest.getEmail() != null) {
            user.setEmail(updateUserRequest.getEmail());
        }
        if (updateUserRequest.getPhoneNumber() != null) {
            user.setPhoneNumber(updateUserRequest.getPhoneNumber());
        }
        if (updateUserRequest.getAddress() != null) {
            AddressRequest addressRequest = updateUserRequest.getAddress();
            addressRequest.setId(user.getAddress().getId());
            user.setAddress(addressService.updateAddress(addressRequest));
        }

        if (updateUserRequest.getAvatarUrl() != null) {
            user.setProfilePicture(updateUserRequest.getAvatarUrl());
        }
        userRepository.save(user);
    }
    
    @Override
    public List<UserDeviceResponse> getUserDevices(Long userId, String deviceId) {
        List<UserDevice> userDevices = userDeviceRepository.finAllByUserId(userId);
        return userDevices.stream().map(userDevice -> toUserDeviceResponse(userDevice, deviceId)).toList();
    }

    private UserDeviceResponse toUserDeviceResponse(UserDevice userDevice, String deviceId) {
        UserDeviceResponse response = new UserDeviceResponse();
        response.setId(userDevice.getId());
        response.setDeviceId(userDevice.getDeviceId());
        response.setDeviceName(userDevice.getDeviceName());
        response.setLocation(userDevice.getLocation());
        response.setIpAddress(userDevice.getClientIp());
        response.setCurrentDevice(Objects.equals(userDevice.getDeviceId(), deviceId));
        return response;
    }
    
    @Override
    public Pair<List<UserDto>, PaginationResponse> searchUsers(UserFilter filters) {
        var users = userRepository.findAll(filters);
        var userResponses = users.stream().map(this::mapToDto).toList();
        PaginationResponse paginationResponse = new PaginationResponse(filters.getLimit(),
                filters.getPage(), (int) users.getTotalElements());

        return Pair.of(userResponses, paginationResponse);
    }

    @Override
    @Transactional
    public User createNewMobileUser(UserDevice userDevice, SelectRoleRequest request) {
        User newUser = new User(request.getPhone());
        newUser.setRole(request.getRole().toUpperCase());
        newUser.setPhoneNumber(request.getPhone());
        newUser.setPassword(passwordEncoder.encode(userDevice.getTempPassword()));
        newUser.setPhoneVerified(true);
        newUser.setUlid(UlidCreator.getUlidString());
        newUser.setActive(true);
        newUser = userRepository.save(newUser);
        return newUser;
    }

    @Override
    public void save(User user) {
        userRepository.save(user);
    }

    @Override
    public User getByUlidNullable(String ulid) {
        return userRepository.findByUlid(ulid);
    }

    @Override
    public User getUserByUlid(String ulid) {
        User user = getByUlidNullable(ulid);
        if (user == null || !user.isActive()) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }
        return user;
    }

    @Override
    public User getById(Long id) {
        return userRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND));
    }

    @Override
    public User getByIdNullable(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    @Override
    public User getCurrentLoginUser() {
        UserRepresentation currentUser = userKeycloakService.getCurrentUser();
        return getUserByUlid(currentUser.getUsername());
    }

    @Override
    public void acceptJoiningInvitation(Long staffId) {
        User receiver = getCurrentLoginUser();
        Staff staff = staffService.getById(staffId);
        if (!Objects.equals(receiver.getId(),staff.getUser().getId())) {
            throw new ForbiddenException();
        }
        staff.setActive(true);
        staff.setPending(false);
        staff.setJoinDate(CommonUtil.getCurrentUTCTime());
        staffService.save(staff);
    }

    @Override
    public void rejectJoiningInvitation(Long staffId) {
        User receiver = getCurrentLoginUser();
        Staff staff = staffService.getById(staffId);
        if (!Objects.equals(receiver.getId(), staff.getUser().getId())) {
            throw new ForbiddenException();
        }
        staff.setActive(false);
        staff.setPending(false);
        staffService.save(staff);
    }

    public List<User> getUserByUlids(List<String> userUlids) {
        return userRepository.findByUlidIn(userUlids.stream().map(String::toLowerCase).toList());
    }

    @Override
    public List<User> getUserExcludeUlids(List<String> userUlids) {
        return userRepository.findUserExcludeUlids(userUlids.stream().map(String::toLowerCase).toList());
    }

    @Override
    public void assignRoleToUser(Long userId, List<Long> roleIds) {
        try {
            User user = getById(userId);
            List<Role> roles = roleService.findByIds(roleIds);
            user.setRoles(new HashSet<>(roles));
            save(user);

            List<String> roleKeycloakIds = roles.stream().map(Role::getKeycloakId).toList();

            adminKeycloakService.assignRoleToUser(user.getKeycloakUserId(), roleKeycloakIds);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_ASSIGNED_ROLE_TO_USER);
        }
    }

    @Override
    public void removeRoleOfUser(Long userId, Long roleId) {
        try {
            User user = getById(userId);
            Role role = roleService.findById(roleId);
            Set<Role> roles = user.getRoles();
            if (!roles.contains(role)) {
                throw new BadRequestException(ErrorMessage.USER_NOT_ASSIGNED_ROLE);
            }
            roles.remove(role);
            user.setRoles(roles);
            save(user);

            adminKeycloakService.removeRoleOfUser(user.getKeycloakUserId(), role.getKeycloakId());
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_REMOVE_ROLE_OF_USER);
        }
    }

    @Override
    public List<User> getUserByKeycloakIds(List<String> userIds) {
        return userRepository.findByKeycloakIdIn(userIds);
    }

    public User getByKeycloakId(String keycloakId) {
        return userRepository.findByKeycloakId(keycloakId);
    }

    private LocalDate parseDateOfBirth(String dateOfBirthStr) {
        if (dateOfBirthStr == null) return null;
        
        try {
            // Try parsing as ISO datetime first
            LocalDateTime dateTime = LocalDateTime.parse(dateOfBirthStr.replace("Z", ""));
            return dateTime.toLocalDate();
        } catch (Exception e) {
            try {
                // Try parsing as LocalDate
                return LocalDate.parse(dateOfBirthStr);
            } catch (Exception ex) {
                // Return null if can't parse
                return null;
            }
        }
    }

    private UserDto mapToDto(User user) {
        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setName(user.getName());
        userDto.setEmail(user.getEmail());
        userDto.setRole(user.getRole());
        userDto.setPhoneNumber(user.getPhoneNumber());
        userDto.setProfilePicture(user.getProfilePicture());
        userDto.setActive(user.isActive());
        userDto.setUlid(user.getUlid());
        userDto.setCreatedAt(user.getCreatedAt());
        userDto.setIsAdminPortal(user.getIsAdminPortal());
        
        // Map missing fields
        if (user.getDateOfBirth() != null) {
            // Convert LocalDate to ISO datetime string format
            userDto.setDateOfBirth(user.getDateOfBirth().atStartOfDay().toString() + "Z");
        }
        userDto.setGender(user.getGender());
        
        // Map address if exists
        if (user.getAddress() != null) {
            AddressRequest addressRequest = new AddressRequest();
            addressRequest.setId(user.getAddress().getId());
            addressRequest.setDetailAddress(user.getAddress().getDetailAddress());
            addressRequest.setProvinceCode(user.getAddress().getProvince().getCode());
            addressRequest.setDistrictCode(user.getAddress().getDistrict().getCode());
            addressRequest.setWardCode(user.getAddress().getWard().getCode());
            
            // Add names for display
            addressRequest.setProvinceName(user.getAddress().getProvince().getName());
            addressRequest.setDistrictName(user.getAddress().getDistrict().getName());
            addressRequest.setWardName(user.getAddress().getWard().getName());
            
            userDto.setAddress(addressRequest);
        }
        
        // Map roles and roleIds
        if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            Set<RoleResponse> roles = user.getRoles().stream()
                    .map(role -> new RoleResponse(role.getId(), role.getName(), role.getPermissions()))
                    .collect(Collectors.toSet());
            userDto.setRoles(roles);
            
            Set<Long> roleIds = user.getRoles().stream()
                    .map(Role::getId)
                    .collect(Collectors.toSet());
            userDto.setRoleIds(roleIds);
        }
        
        return userDto;
    }

} 