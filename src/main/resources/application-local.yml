spring:
  datasource:
    url: **************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        '[format_sql]': true
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: 1022889238527-7uj1m4esptuf1udtlkhc82csqot3aajd.apps.googleusercontent.com
            client-secret: 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
            scope:
              - email
              - https://www.googleapis.com/auth/gmail.send
          user:
            provider: keycloak
            client-id: normal-user
            authorization-grant-type: authorization_code
            redirect-uri: http://localhost:8080/oauth2/flexin
            scope:
              - openid
          admin:
            provider: keycloak
            client-id: admin-user
            authorization-grant-type: authorization_code
            redirect-uri: http://localhost:8080/oauth2/flexin
            scope:
              - openid
              - profile
        provider:
          keycloak:
            issuer-uri: http://localhost:8888/realms/flexin
      resource-server:
        jwt:
          issuer-uri: http://localhost:8888/realms/flexin
          jwk-set-uri: ${spring.security.oauth2.client.provider.keycloak.issuer-uri}/protocol/openid-connect/certs


# Keycloak Configuration
keycloak:
  auth-server-url: http://localhost:8888
  realm: flexin
  user:
    client-id: normal-user
    client-secret: pRYqWw5GoSIBVvjUKbnwLXEhv4ox8cAH
  admin:
    client-id: admin-user
    client-secret: owpIyKkq1jq8kJphfexEBrxeysUGzpDv
  token-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/token
  default-role-name: default-roles-flexin

# JWT Configuration
jwt:
  temp-token:
    secret: "your-256-bit-secret-key-for-temporary-token-generation"

google:
  storage:
    bucket-name: tung-flexin-storage
    credential-url: google-cloud-key/ancient-truth-454914-r1-7c2ebd9e4ebc.json
  firebase:
    credential-url: google-cloud-key/flexin-test-firebase-adminsdk-fbsvc-57b48ab4c5.json
