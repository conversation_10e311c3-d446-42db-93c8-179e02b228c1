server:
  port: 8080

spring:
  application:
    name: backend
  profiles:
    active: local
  thymeleaf:
    mode: HTML
  messages:
    basename: i18n/messages

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
    groups:
      enabled: false
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
    try-it-out-enabled: true
    display-request-duration: true
    doc-expansion: none
    disable-swagger-default-url: false
    urls:
      - url: /v3/api-docs
        name: API Documentation
  default-produces-media-type: application/json
  paths-to-match: /**

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
      show-details: always
      group:
        liveness:
          include: livenessState
        readiness:
          include: readinessState
  health:
    probes:
      enabled: true

mail:
  uri:
    forget-password: /v1/admin/auth/reset-password
  from-email: <EMAIL>
  server-url: https://fap.dev.flexin.tech

